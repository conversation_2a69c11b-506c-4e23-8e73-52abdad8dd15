# Relay Monitor - Python Dependencies

# Core dependencies
requests>=2.31.0
flask>=3.0.0
schedule>=1.2.0

# Data handling
# sqlite3 is built-in with Python

# Configuration and validation
pydantic>=2.5.0
python-dotenv>=1.0.0
toml>=0.10.2
bcrypt>=4.0.0

# Cryptography and blockchain
cryptography>=41.0.0
eth-account>=0.10.0
web3>=6.15.0
eth-utils>=2.3.0

# Web interface
jinja2>=3.1.0
werkzeug>=3.0.0

# Utilities
click>=8.1.0
colorama>=0.4.6
tabulate>=0.9.0
pytz>=2023.3

# Development dependencies
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.7.0

# Optional dependencies for enhanced features
# Uncomment as needed:
# aiohttp>=3.9.0  # For async HTTP requests
# redis>=5.0.0    # For caching
# psycopg2>=2.9.0 # For PostgreSQL support

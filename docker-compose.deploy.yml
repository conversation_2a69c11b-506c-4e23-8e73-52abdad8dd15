# Relay Monitor - 生产部署配置
# 使用Docker Hub预构建镜像，无需本地构建
#
# 使用方法:
#   docker-compose -f docker-compose.deploy.yml up -d
#
# 环境变量:
#   HOST_PORT=8080 docker-compose -f docker-compose.deploy.yml up -d

# 项目名称（避免警告）
name: relay-monitor

services:
  relay-monitor:
    image: ljh740/relay-monitor:latest
    container_name: relay-monitor-deploy
    restart: unless-stopped
    
    # 端口映射
    ports:
      - "${HOST_PORT:-5001}:5000"
    
    # 环境变量
    environment:
      # 时区设置
      - TZ=Asia/Shanghai
      
      # 应用配置
      - PYTHONUNBUFFERED=1
      - PYTHONPATH=/app
      
      # 可选：数据库路径（容器内路径）
      - DATABASE_PATH=/app/data/relay_monitor.db
      
      # 可选：日志级别
      - LOG_LEVEL=INFO
    
    # 数据卷挂载
    volumes:
      # 数据持久化
      - relay_monitor_data:/app/data
      - relay_monitor_logs:/app/logs

      # 配置文件持久化（解决交易对配置丢失问题）
      - relay_monitor_config:/app/config
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 网络配置
    networks:
      - relay-monitor-network
    
    # 资源限制（可选）
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

# 数据卷定义
volumes:
  relay_monitor_data:
    driver: local
  relay_monitor_logs:
    driver: local
  relay_monitor_config:
    driver: local

# 网络定义
networks:
  relay-monitor-network:
    driver: bridge

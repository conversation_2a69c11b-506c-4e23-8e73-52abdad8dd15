#!/bin/bash

# Relay Monitor - Docker Test Script

set -e

# 导入通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh"

# 使用带时间戳的日志函数
log() { log_with_timestamp "$1"; }
error() { error_with_timestamp "$1"; }
warn() { warn_with_timestamp "$1"; }
success() { success_with_timestamp "$1"; }

# Test configuration
IMAGE_NAME="relay-monitor:test-3.13"
TEST_PORT="5002"
CONTAINER_NAME="relay-monitor-test"

# Cleanup function
cleanup() {
    log "Cleaning up test containers..."
    docker stop $CONTAINER_NAME 2>/dev/null || true
    docker rm $CONTAINER_NAME 2>/dev/null || true
}

# Trap cleanup on exit
trap cleanup EXIT

log "Starting Docker deployment tests..."

# Test 1: Health check command
log "Test 1: Testing health check command..."
if docker run --rm --name "${CONTAINER_NAME}-health" $IMAGE_NAME health; then
    success "✅ Health check command test passed"
else
    error "❌ Health check command test failed"
    exit 1
fi

# Test 2: Configuration validation
log "Test 2: Testing configuration validation..."
if docker run --rm --name "${CONTAINER_NAME}-config" $IMAGE_NAME config validate; then
    success "✅ Configuration validation test passed"
else
    error "❌ Configuration validation test failed"
    exit 1
fi

# Test 3: Web server startup
log "Test 3: Testing web server startup..."
docker run -d --name $CONTAINER_NAME -p $TEST_PORT:5000 $IMAGE_NAME web --host 0.0.0.0 --port 5000

# Wait for container to start
log "Waiting for container to start..."
sleep 10

# Check if container is running
if docker ps | grep -q $CONTAINER_NAME; then
    success "✅ Container is running"
else
    error "❌ Container failed to start"
    docker logs $CONTAINER_NAME
    exit 1
fi

# Test 4: Health endpoint
log "Test 4: Testing health endpoint..."
for i in {1..10}; do
    if curl -4 -f http://127.0.0.1:$TEST_PORT/health >/dev/null 2>&1; then
        success "✅ Health endpoint test passed"
        break
    else
        if [ $i -eq 10 ]; then
            error "❌ Health endpoint test failed after 10 attempts"
            docker logs $CONTAINER_NAME
            exit 1
        fi
        log "Attempt $i failed, retrying in 3 seconds..."
        sleep 3
    fi
done

# Test 5: Ready endpoint
log "Test 5: Testing ready endpoint..."
if curl -4 -f http://127.0.0.1:$TEST_PORT/ready >/dev/null 2>&1; then
    success "✅ Ready endpoint test passed"
else
    error "❌ Ready endpoint test failed"
    docker logs $CONTAINER_NAME
    exit 1
fi

# Test 6: Main page
log "Test 6: Testing main page..."
if curl -4 -f http://127.0.0.1:$TEST_PORT/ >/dev/null 2>&1; then
    success "✅ Main page test passed"
else
    error "❌ Main page test failed"
    docker logs $CONTAINER_NAME
    exit 1
fi

# Test 7: Container logs
log "Test 7: Checking container logs..."
LOGS=$(docker logs $CONTAINER_NAME 2>&1)
if echo "$LOGS" | grep -q "Starting web server"; then
    success "✅ Container logs show proper startup"
else
    error "❌ Container logs don't show proper startup"
    echo "$LOGS"
    exit 1
fi

# Test 8: Resource usage
log "Test 8: Checking resource usage..."
STATS=$(docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" $CONTAINER_NAME)
log "Container resource usage:"
echo "$STATS"

# Test 9: Docker Compose test
log "Test 9: Testing Docker Compose..."
if command -v docker-compose &> /dev/null; then
    # Create temporary compose file for testing
    cat > docker-compose.test.yml << EOF
version: '3.8'
services:
  relay-monitor-test:
    image: $IMAGE_NAME
    ports:
      - "5003:5000"
    environment:
      - DEBUG=true
    command: ["web", "--host", "0.0.0.0", "--port", "5000"]
EOF
    
    docker-compose -f docker-compose.test.yml up -d
    sleep 10
    
    if curl -f http://localhost:5003/health >/dev/null 2>&1; then
        success "✅ Docker Compose test passed"
    else
        error "❌ Docker Compose test failed"
        docker-compose -f docker-compose.test.yml logs
    fi
    
    docker-compose -f docker-compose.test.yml down
    rm docker-compose.test.yml
else
    warn "⚠️  Docker Compose not available, skipping test"
fi

success "🎉 All Docker tests completed successfully!"

log "Test Summary:"
log "- Health check command: ✅"
log "- Configuration validation: ✅"
log "- Web server startup: ✅"
log "- Health endpoint: ✅"
log "- Ready endpoint: ✅"
log "- Main page: ✅"
log "- Container logs: ✅"
log "- Resource usage: ✅"
log "- Docker Compose: ✅"

log "🚀 Docker deployment is ready for production!"

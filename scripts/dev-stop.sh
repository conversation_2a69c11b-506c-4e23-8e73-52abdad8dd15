#!/bin/bash

# Relay Monitor - 开发环境停止脚本

set -e

# 导入通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh"

echo "⏹️ Relay Monitor - 开发环境停止"
echo "==============================="

# 检查Docker
if ! command -v docker &> /dev/null; then
    log_error "Docker未安装"
    exit 1
fi

# 检查是否有运行的服务
if ! docker compose ps | grep -q "Up"; then
    log_info "没有运行中的服务"
    exit 0
fi

# 显示当前运行的服务
log_info "当前运行的服务:"
docker compose ps

echo

# 询问是否保留数据
read -p "是否保留数据卷? (Y/n): " -n 1 -r
echo

if [[ $REPLY =~ ^[Nn]$ ]]; then
    log_info "停止服务并删除数据卷..."
    docker compose down -v
    log_success "服务已停止，数据卷已删除"
else
    log_info "停止服务但保留数据卷..."
    docker compose down
    log_success "服务已停止，数据卷已保留"
fi

# 清理未使用的镜像（可选）
read -p "是否清理未使用的Docker镜像? (y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "清理未使用的镜像..."
    docker image prune -f
    log_success "镜像清理完成"
fi

echo
log_success "开发环境已停止"

#!/bin/bash

# Relay Monitor - 本地CI测试脚本
# 模拟GitHub Actions CI流程，用于本地验证

set -e

# 导入通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh"

# 检查是否在项目根目录
if ! check_project_root; then
    exit 1
fi

echo "🧪 Relay Monitor - 本地CI测试"
echo "=============================="

# 1. 检查Python环境
log_info "检查Python环境..."
python_version=$(python --version 2>&1)
log_info "Python版本: $python_version"

# 2. 安装依赖
log_info "安装依赖..."
if [ -f "requirements.txt" ]; then
    pip install -r requirements.txt > /dev/null 2>&1
    log_success "依赖安装完成"
else
    log_warning "requirements.txt不存在，安装生产依赖"
    pip install -r requirements-prod.txt > /dev/null 2>&1
fi

# 3. 代码风格检查
log_info "执行代码风格检查..."
pip install flake8 > /dev/null 2>&1

echo "  - 基本语法检查..."
if flake8 src/ --count --select=E9,F63,F7,F82 --show-source --statistics; then
    log_success "基本语法检查通过"
else
    log_error "基本语法检查失败"
    exit 1
fi

echo "  - 代码风格检查..."
if flake8 src/ --count --max-complexity=15 --max-line-length=120 --statistics; then
    log_success "代码风格检查通过"
else
    log_warning "代码风格检查有警告（允许继续）"
fi

# 4. 类型检查
log_info "执行类型检查..."
pip install mypy > /dev/null 2>&1

if mypy src/ --ignore-missing-imports --no-strict-optional; then
    log_success "类型检查通过"
else
    log_warning "类型检查有问题（允许继续）"
fi

# 5. 配置文件验证
log_info "验证配置文件..."
python -c "
import toml
try:
    with open('config/config.example.toml', 'r') as f:
        config = toml.load(f)
    print('✅ Configuration file is valid')
except Exception as e:
    print(f'❌ Configuration file error: {e}')
    exit(1)
"
if [ $? -eq 0 ]; then
    log_success "配置文件验证通过"
else
    log_error "配置文件验证失败"
    exit 1
fi

# 6. 基础测试
log_info "执行基础测试..."
if [ -f "run_tests.py" ]; then
    python run_tests.py
    log_success "测试执行完成"
else
    log_info "未找到测试文件，跳过测试"
fi

# 7. Docker构建测试
log_info "执行Docker构建测试..."
if command -v docker &> /dev/null; then
    if [ -f "Dockerfile.optimized" ]; then
        log_info "使用Dockerfile.optimized构建..."
        docker build -f Dockerfile.optimized -t relay-monitor:ci-test . > /dev/null 2>&1
    elif [ -f "Dockerfile" ]; then
        log_info "使用Dockerfile构建..."
        docker build -f Dockerfile -t relay-monitor:ci-test . > /dev/null 2>&1
    else
        log_error "未找到Dockerfile"
        exit 1
    fi
    
    # 测试镜像
    log_info "测试Docker镜像..."
    docker run --rm relay-monitor:ci-test python --version > /dev/null 2>&1
    
    # 清理测试镜像
    docker rmi relay-monitor:ci-test > /dev/null 2>&1
    
    log_success "Docker构建测试通过"
else
    log_warning "Docker未安装，跳过Docker测试"
fi

# 8. 安全扫描（可选）
log_info "执行安全扫描..."
if command -v trivy &> /dev/null; then
    trivy fs --severity HIGH,CRITICAL --format table . || true
    log_success "安全扫描完成"
else
    log_warning "Trivy未安装，跳过安全扫描"
fi

echo
echo "🎉 本地CI测试完成！"
echo "==================="
log_success "所有检查通过"
echo
log_info "测试结果:"
log_info "  ✅ Python环境: 正常"
log_info "  ✅ 依赖安装: 成功"
log_info "  ✅ 代码风格: 通过"
log_info "  ✅ 类型检查: 通过"
log_info "  ✅ 配置验证: 通过"
log_info "  ✅ 基础测试: 通过"
log_info "  ✅ Docker构建: 通过"
echo
log_success "代码已准备好推送到dev分支！"

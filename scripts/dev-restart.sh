#!/bin/bash

# Relay Monitor - 开发环境快速重启脚本
# 用于代码修改后的快速重启，支持热重载

set -e

# 导入通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh"

echo "🔄 Relay Monitor - 开发环境快速重启"
echo "=================================="

# 解析命令行参数
REBUILD=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --rebuild)
            REBUILD=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --rebuild    重建镜像后重启"
            echo "  -h, --help   显示此帮助信息"
            echo ""
            echo "说明:"
            echo "  默认情况下只重启容器，不重建镜像"
            echo "  由于使用了代码挂载，大部分代码修改无需重建镜像"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 --help 查看帮助"
            exit 1
            ;;
    esac
done

# 检查Docker
if ! command -v docker &> /dev/null; then
    log_error "Docker未安装"
    exit 1
fi

# 检查是否有运行的服务
if ! docker compose ps | grep -q "relay-monitor"; then
    log_warning "开发环境未运行，将启动新环境"
    exec "$SCRIPT_DIR/dev-start.sh"
fi

# 显示当前状态
log_info "当前服务状态:"
docker compose ps

if [ "$REBUILD" = true ]; then
    log_info "重建镜像并重启..."
    docker compose down
    docker compose build
    docker compose up -d
else
    log_info "快速重启容器（保持代码挂载）..."
    docker compose restart
fi

# 等待服务启动
log_info "等待服务重启..."
sleep 3

# 检查服务状态
if docker compose ps | grep -q "Up"; then
    log_success "服务重启成功!"
    
    # 获取端口
    DEV_HOST_PORT=${DEV_HOST_PORT:-5001}
    
    echo
    echo "📊 访问地址:"
    echo "  🌐 Web界面: http://localhost:$DEV_HOST_PORT"
    echo "  🔧 管理面板: http://localhost:$DEV_HOST_PORT/admin"
    echo
    echo "💡 提示:"
    echo "  由于使用了代码挂载，Python代码修改会自动生效"
    echo "  如需查看日志: docker compose logs -f"
else
    log_error "服务重启失败"
    echo "查看日志:"
    docker compose logs
    exit 1
fi

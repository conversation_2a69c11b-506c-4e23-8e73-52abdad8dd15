#!/bin/bash

# Relay Monitor - Docker Deployment Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" >&2
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

# Default values
ENVIRONMENT="production"
COMPOSE_FILE="docker-compose.yml"
PROJECT_NAME="relay-monitor"
ACTION="up"

# Help function
show_help() {
    cat << EOF
Relay Monitor Docker Deployment Script

Usage: $0 [OPTIONS] [ACTION]

Actions:
    up          Start services (default)
    down        Stop and remove services
    restart     Restart services
    logs        Show service logs
    status      Show service status
    update      Pull latest images and restart

Options:
    -h, --help              Show this help message
    -e, --env ENVIRONMENT   Set environment (production|development) (default: production)
    -f, --file FILE         Docker compose file (default: docker-compose.yml)
    -p, --project NAME      Project name (default: relay-monitor)
    --build                 Build images before starting
    --pull                  Pull images before starting
    --force-recreate        Force recreate containers

Examples:
    $0                                  # Start production environment
    $0 -e development up               # Start development environment
    $0 down                            # Stop all services
    $0 logs                            # Show logs
    $0 update                          # Update and restart services

EOF
}

# Parse command line arguments
BUILD=false
PULL=false
FORCE_RECREATE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -f|--file)
            COMPOSE_FILE="$2"
            shift 2
            ;;
        -p|--project)
            PROJECT_NAME="$2"
            shift 2
            ;;
        --build)
            BUILD=true
            shift
            ;;
        --pull)
            PULL=true
            shift
            ;;
        --force-recreate)
            FORCE_RECREATE=true
            shift
            ;;
        up|down|restart|logs|status|update)
            ACTION="$1"
            shift
            ;;
        *)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Set compose files based on environment
if [ "$ENVIRONMENT" = "development" ]; then
    COMPOSE_FILES="-f docker-compose.yml -f docker-compose.dev.yml"
elif [ "$ENVIRONMENT" = "production" ]; then
    # Use production compose file with optimized image
    COMPOSE_FILES="-f docker-compose.prod.yml"
else
    COMPOSE_FILES="-f $COMPOSE_FILE"
fi

# Docker compose command base
DOCKER_COMPOSE="docker-compose $COMPOSE_FILES -p $PROJECT_NAME"

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    error "docker-compose is not installed or not in PATH"
    exit 1
fi

# Check if compose files exist
if [ "$ENVIRONMENT" = "production" ]; then
    if [ ! -f "docker-compose.prod.yml" ]; then
        error "Production compose file not found: docker-compose.prod.yml"
        exit 1
    fi
elif [ "$ENVIRONMENT" = "development" ]; then
    for file in docker-compose.yml docker-compose.dev.yml; do
        if [ ! -f "$file" ]; then
            error "Compose file not found: $file"
            exit 1
        fi
    done
else
    if [ ! -f "$COMPOSE_FILE" ]; then
        error "Compose file not found: $COMPOSE_FILE"
        exit 1
    fi
fi

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    mkdir -p docker-data/data
    mkdir -p docker-data/logs
    mkdir -p docker-data/dev-data
    mkdir -p docker-data/dev-logs
    mkdir -p config
    
    # Initialize configuration if needed
    if [ ! -f "config/config.toml" ] || [ ! -f ".env" ]; then
        log "Configuration files missing. Initializing..."
        if [ -x "scripts/init-config.sh" ]; then
            ./scripts/init-config.sh --minimal
        else
            # Fallback to manual creation
            if [ ! -f ".env" ] && [ -f ".env.example" ]; then
                log "Creating .env from example..."
                cp .env.example .env
            fi
            if [ ! -f "config/config.toml" ] && [ -f "config/config.simple.toml" ]; then
                log "Creating config.toml from simple template..."
                cp config/config.simple.toml config/config.toml
            fi
        fi
        warn "Please edit .env and config/config.toml with your settings before starting the service"
    fi
}

# Function to handle different actions
case $ACTION in
    up)
        log "Starting Relay Monitor ($ENVIRONMENT environment)..."
        create_directories
        
        # Build options
        UP_ARGS=""
        if [ "$BUILD" = true ]; then
            UP_ARGS="$UP_ARGS --build"
        fi
        if [ "$PULL" = true ]; then
            UP_ARGS="$UP_ARGS --pull"
        fi
        if [ "$FORCE_RECREATE" = true ]; then
            UP_ARGS="$UP_ARGS --force-recreate"
        fi
        
        $DOCKER_COMPOSE up -d $UP_ARGS
        
        # Wait for services to be ready
        log "Waiting for services to be ready..."
        sleep 10
        
        # Check health
        if $DOCKER_COMPOSE ps | grep -q "Up"; then
            success "Services started successfully!"
            $DOCKER_COMPOSE ps
            
            # Show access information
            if [ "$ENVIRONMENT" = "development" ]; then
                PORT=${DEV_HOST_PORT:-5001}
            else
                PORT=${HOST_PORT:-5000}
            fi
            
            log "Access the application at: http://localhost:$PORT"
            log "Admin panel: http://localhost:$PORT/admin/login"
        else
            error "Some services failed to start"
            $DOCKER_COMPOSE logs
            exit 1
        fi
        ;;
        
    down)
        log "Stopping Relay Monitor..."
        $DOCKER_COMPOSE down
        success "Services stopped successfully!"
        ;;
        
    restart)
        log "Restarting Relay Monitor..."
        $DOCKER_COMPOSE restart
        success "Services restarted successfully!"
        ;;
        
    logs)
        log "Showing service logs..."
        $DOCKER_COMPOSE logs -f
        ;;
        
    status)
        log "Service status:"
        $DOCKER_COMPOSE ps
        ;;
        
    update)
        log "Updating Relay Monitor..."
        $DOCKER_COMPOSE pull
        $DOCKER_COMPOSE up -d
        success "Services updated successfully!"
        ;;
        
    *)
        error "Unknown action: $ACTION"
        show_help
        exit 1
        ;;
esac

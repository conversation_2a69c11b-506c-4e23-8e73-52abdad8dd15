#!/bin/bash

# Relay Monitor - 通用脚本函数库
# 提供所有脚本共用的函数和变量定义

# 颜色定义
export RED='\033[0;31m'
export GREEN='\033[0;32m'
export YELLOW='\033[1;33m'
export BLUE='\033[0;34m'
export NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 带时间戳的日志函数
log_with_timestamp() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error_with_timestamp() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" >&2
}

warn_with_timestamp() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

success_with_timestamp() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

# 检查命令是否存在
check_command() {
    local cmd=$1
    local description=${2:-$cmd}
    
    if ! command -v "$cmd" &> /dev/null; then
        log_error "$description 未安装或不在PATH中"
        return 1
    fi
    return 0
}

# 检查文件是否存在
check_file() {
    local file=$1
    local description=${2:-$file}
    
    if [ ! -f "$file" ]; then
        log_error "文件不存在: $description"
        return 1
    fi
    return 0
}

# 检查目录是否存在
check_directory() {
    local dir=$1
    local description=${2:-$dir}
    
    if [ ! -d "$dir" ]; then
        log_error "目录不存在: $description"
        return 1
    fi
    return 0
}

# 检查是否在项目根目录
check_project_root() {
    if [ ! -f "src/relay_monitor/__init__.py" ]; then
        log_error "请在项目根目录运行此脚本"
        return 1
    fi
    return 0
}

# 检查Docker环境
check_docker() {
    log_info "检查Docker环境..."
    
    if ! check_command "docker" "Docker"; then
        return 1
    fi
    
    if ! docker --version &> /dev/null; then
        log_error "Docker未运行或无权限访问"
        return 1
    fi
    
    log_success "Docker环境正常"
    return 0
}

# 检查Python环境
check_python() {
    log_info "检查Python环境..."
    
    if ! check_command "python" "Python"; then
        return 1
    fi
    
    local python_version=$(python --version 2>&1)
    log_info "Python版本: $python_version"
    
    return 0
}

# 安全地清理Docker镜像
cleanup_docker_image() {
    local image_name=$1
    
    if [ -z "$image_name" ]; then
        log_warning "未指定要清理的镜像名称"
        return 1
    fi
    
    log_info "清理Docker镜像: $image_name"
    if docker rmi "$image_name" > /dev/null 2>&1; then
        log_success "镜像清理完成: $image_name"
    else
        log_warning "镜像清理失败或镜像不存在: $image_name"
    fi
}

# 显示脚本标题
show_header() {
    local title=$1
    local subtitle=${2:-""}
    
    echo
    echo "🔧 $title"
    echo "$(printf '=%.0s' $(seq 1 ${#title}))"
    if [ -n "$subtitle" ]; then
        echo "$subtitle"
    fi
    echo
}

# 显示使用帮助
show_usage() {
    local script_name=$1
    local description=$2
    shift 2
    
    echo "用法: $script_name [选项]"
    echo
    echo "$description"
    echo
    echo "选项:"
    while [ $# -gt 0 ]; do
        echo "  $1"
        shift
    done
    echo
}

# 确认操作
confirm_action() {
    local message=${1:-"是否继续？"}
    local default=${2:-"n"}
    
    echo -n "$message [y/N]: "
    read -r response
    
    case "$response" in
        [yY][eE][sS]|[yY])
            return 0
            ;;
        *)
            return 1
            ;;
    esac
}

# 等待用户按键
wait_for_key() {
    local message=${1:-"按任意键继续..."}
    echo -n "$message"
    read -r
}

# 检查网络连接
check_network() {
    local url=${1:-"https://api.relay.link"}
    
    log_info "检查网络连接: $url"
    
    if curl -s --head --request GET "$url" | grep "200 OK" > /dev/null; then
        log_success "网络连接正常"
        return 0
    else
        log_warning "网络连接异常或服务不可用"
        return 1
    fi
}

# 获取脚本目录
get_script_dir() {
    echo "$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
}

# 获取项目根目录
get_project_root() {
    local script_dir=$(get_script_dir)
    echo "$(dirname "$script_dir")"
}

# 导出所有函数，使其在sourcing脚本中可用
export -f log_info log_success log_warning log_error
export -f log_with_timestamp error_with_timestamp warn_with_timestamp success_with_timestamp
export -f check_command check_file check_directory check_project_root
export -f check_docker check_python cleanup_docker_image
export -f show_header show_usage confirm_action wait_for_key check_network
export -f get_script_dir get_project_root

#!/bin/bash

# Relay Monitor - 开发环境启动脚本
# 简化的开发环境启动命令

set -e

# 导入通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh"

echo "🚀 Relay Monitor - 开发环境启动"
echo "==============================="

# 解析命令行参数
REBUILD=false
NO_CACHE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --rebuild)
            REBUILD=true
            shift
            ;;
        --no-cache)
            NO_CACHE=true
            REBUILD=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --rebuild    强制重建镜像"
            echo "  --no-cache   重建镜像时不使用缓存"
            echo "  -h, --help   显示此帮助信息"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 --help 查看帮助"
            exit 1
            ;;
    esac
done

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装"
    exit 1
fi

# 检查配置文件
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ 未找到docker-compose.yml"
    exit 1
fi

# 显示当前配置
log_info "当前配置:"
if [ -f "docker-compose.override.yml" ]; then
    echo "  📄 使用开发环境覆盖配置 (docker-compose.override.yml)"
    echo "  🔧 命令: docker compose up -d"
else
    echo "  📄 使用基础配置 (docker-compose.yml)"
fi

# 检查端口（开发环境使用DEV_HOST_PORT）
DEV_HOST_PORT=${DEV_HOST_PORT:-5001}
log_info "检查开发环境端口 $DEV_HOST_PORT..."

if lsof -i :$DEV_HOST_PORT &> /dev/null; then
    log_warning "端口 $DEV_HOST_PORT 已被占用"
    echo "正在使用端口 $DEV_HOST_PORT 的进程:"
    lsof -i :$DEV_HOST_PORT
    echo
    read -p "是否继续启动? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "启动已取消"
        exit 1
    fi
fi

# 检查是否需要构建镜像
if [ "$REBUILD" = true ]; then
    if [ "$NO_CACHE" = true ]; then
        log_info "重建镜像（不使用缓存）..."
        docker compose build --no-cache
    else
        log_info "重建镜像（使用缓存）..."
        docker compose build
    fi
elif ! docker images | grep -q "relay-monitor.*optimized"; then
    log_info "镜像不存在，开始构建（使用缓存）..."
    docker compose build
else
    log_info "使用现有镜像..."
fi

# 启动服务
log_info "启动开发环境..."
docker compose up -d

# 等待服务启动
log_info "等待服务启动..."
sleep 5

# 检查服务状态
if docker compose ps | grep -q "Up"; then
    log_success "服务启动成功!"
    echo
    echo "📊 访问地址:"
    echo "  🌐 Web界面: http://localhost:$DEV_HOST_PORT"
    echo "  🔧 管理面板: http://localhost:$DEV_HOST_PORT/admin"
    echo "  📋 API文档: http://localhost:$DEV_HOST_PORT/docs"
    echo
    echo "🔍 查看日志:"
    echo "  docker compose logs -f"
    echo
    echo "⏹️ 停止服务:"
    echo "  docker compose down"
    echo "  或运行: ./scripts/dev-stop.sh"
else
    echo "❌ 服务启动失败"
    echo "查看日志:"
    docker compose logs
    exit 1
fi

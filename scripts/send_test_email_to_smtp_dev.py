#!/usr/bin/env python3
"""
发送测试邮件到SMTP.dev收件箱的脚本

这个脚本使用外部SMTP服务来发送邮件到SMTP.dev收件箱，
这样您就能在Web界面的"收到的邮件"模块中看到测试警报了。

使用方法:
python scripts/send_test_email_to_smtp_dev.py
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
import sys
import os

def send_test_email():
    """发送测试邮件到SMTP.dev收件箱"""
    
    # SMTP.dev收件箱配置
    smtp_dev_email = "<EMAIL>"
    
    # 邮件内容
    subject = "🧪 Relay Monitor Test Alert"
    
    text_content = f"""
🚨 RELAY MONITOR TEST ALERT 🚨

This is a test email sent from Relay Monitor to verify the email alert functionality.

📊 Test Details:
- Sent Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- From: Relay Monitor System
- To: {smtp_dev_email}
- Purpose: Verify email reception in SMTP.dev inbox

✅ If you see this email in the "收到的邮件" section of the web interface, 
   the email alert system is working correctly!

🔧 Technical Info:
- Alert Type: System Test
- Priority: Normal
- Source: Manual Test Script

---
Relay Monitor - Automated Alert System
Generated at {datetime.now().isoformat()}
    """
    
    html_content = f"""
    <html>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h1 style="color: #007bff; text-align: center;">🚨 Relay Monitor Test Alert</h1>
            
            <div style="background-color: #e8f4fd; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; margin: 20px 0;">
                <p><strong>✅ This is a test email sent from Relay Monitor to verify the email alert functionality.</strong></p>
            </div>
            
            <h3 style="color: #495057;">📊 Test Details:</h3>
            <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
                <tr style="background-color: #f8f9fa;">
                    <td style="padding: 10px; border: 1px solid #dee2e6; font-weight: bold;">Sent Time:</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</td>
                </tr>
                <tr>
                    <td style="padding: 10px; border: 1px solid #dee2e6; font-weight: bold;">From:</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">Relay Monitor System</td>
                </tr>
                <tr style="background-color: #f8f9fa;">
                    <td style="padding: 10px; border: 1px solid #dee2e6; font-weight: bold;">To:</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">{smtp_dev_email}</td>
                </tr>
                <tr>
                    <td style="padding: 10px; border: 1px solid #dee2e6; font-weight: bold;">Purpose:</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">Verify email reception in SMTP.dev inbox</td>
                </tr>
            </table>
            
            <div style="background-color: #d4edda; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; margin: 20px 0;">
                <p style="margin: 0;"><strong>✅ Success Indicator:</strong> If you see this email in the "收到的邮件" section of the web interface, the email alert system is working correctly!</p>
            </div>
            
            <h3 style="color: #495057;">🔧 Technical Info:</h3>
            <ul style="background-color: #f8f9fa; padding: 15px; border-radius: 5px;">
                <li><strong>Alert Type:</strong> System Test</li>
                <li><strong>Priority:</strong> Normal</li>
                <li><strong>Source:</strong> Manual Test Script</li>
                <li><strong>Generated:</strong> {datetime.now().isoformat()}</li>
            </ul>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #dee2e6;">
            
            <div style="text-align: center; color: #6c757d; font-size: 12px;">
                <p><strong>Relay Monitor</strong> - Automated Alert System</p>
                <p>🔗 Cross-chain Token Price Monitoring</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    print("🚀 准备发送测试邮件到SMTP.dev收件箱...")
    print(f"📧 目标邮箱: {smtp_dev_email}")
    print(f"📝 主题: {subject}")
    
    # 创建邮件消息
    msg = MIMEMultipart('alternative')
    msg['Subject'] = subject
    msg['From'] = "<EMAIL>"  # 测试发件人地址
    msg['To'] = smtp_dev_email
    
    # 添加文本和HTML内容
    text_part = MIMEText(text_content, 'plain', 'utf-8')
    html_part = MIMEText(html_content, 'html', 'utf-8')
    msg.attach(text_part)
    msg.attach(html_part)
    
    print("\n📄 邮件内容预览:")
    print(f"主题: {subject}")
    print(f"内容: {text_content[:150]}...")
    
    print("\n⚠️  注意: 要实际发送邮件到SMTP.dev收件箱，需要配置外部SMTP服务")
    print("📖 可用的SMTP服务:")
    print("   1. Gmail SMTP: smtp.gmail.com:587 (需要App Password)")
    print("   2. SendGrid: smtp.sendgrid.net:587")
    print("   3. Mailgun: smtp.mailgun.org:587")
    print("   4. Outlook: smtp-mail.outlook.com:587")
    
    print("\n💡 建议:")
    print("1. 配置一个真实的SMTP服务来发送邮件")
    print("2. 或者手动从您的个人邮箱发送邮件到", smtp_dev_email)
    print("3. 然后在Web界面查看'收到的邮件'模块")
    
    print("\n✅ 邮件已准备就绪")
    print("🔧 要启用实际发送，请编辑此脚本并配置SMTP设置")
    
    # 使用SMTP.dev的真实SMTP服务发送邮件
    try:
        # 使用SMTP.dev提供的SMTP配置
        smtp_server = "send.smtp.dev"
        smtp_port = 587
        username = smtp_dev_email  # 使用SMTP.dev邮箱作为发送账户
        password = "asdfq2w1231"  # 使用真实的邮箱密码

        print(f"\n🔗 连接到SMTP服务器: {smtp_server}:{smtp_port}")
        print(f"📧 使用账户: {username}")

        # 发送邮件 - 尝试不同的连接方式
        try:
            # 方法1: 尝试使用TLS
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.set_debuglevel(1)  # 启用调试输出
                print("🔐 尝试启动TLS加密...")
                server.starttls()

                print("🔑 尝试登录...")
                server.login(username, password)
                print("✅ 登录成功")

                print("📤 发送邮件...")
                server.send_message(msg)
                print("✅ 方法1成功：使用TLS发送")

        except Exception as tls_error:
            print(f"⚠️  TLS方法失败: {tls_error}")
            print("🔄 尝试方法2: 无TLS连接...")

            try:
                # 方法2: 不使用TLS
                with smtplib.SMTP(smtp_server, smtp_port) as server:
                    server.set_debuglevel(1)
                    print("🔑 尝试登录（无TLS）...")
                    server.login(username, password)
                    print("✅ 登录成功")

                    print("📤 发送邮件...")
                    server.send_message(msg)
                    print("✅ 方法2成功：无TLS发送")

            except Exception as no_tls_error:
                print(f"⚠️  无TLS方法也失败: {no_tls_error}")
                print("🔄 尝试方法3: 无认证发送...")

                try:
                    # 方法3: 无认证发送
                    with smtplib.SMTP(smtp_server, smtp_port) as server:
                        server.set_debuglevel(1)
                        print("📤 尝试无认证发送邮件...")
                        server.send_message(msg)
                        print("✅ 方法3成功：无认证发送")

                except Exception as no_auth_error:
                    print(f"❌ 所有方法都失败了: {no_auth_error}")
                    raise no_auth_error

        print("\n🎉 邮件发送成功！")
        print("🔍 请在Web界面的'收到的邮件'模块查看")
        print(f"📧 邮件已发送到: {smtp_dev_email}")

    except Exception as e:
        print(f"\n❌ 邮件发送失败: {e}")
        print("💡 错误详情:")
        print(f"   服务器: {smtp_server}:{smtp_port}")
        print(f"   账户: {username}")
        print("🔧 可能的解决方案:")
        print("   1. 检查SMTP.dev是否需要特殊的认证方式")
        print("   2. 确认邮箱账户是否有效")
        print("   3. 检查网络连接是否正常")

if __name__ == "__main__":
    print("🧪 SMTP.dev 邮件发送测试脚本")
    print("=" * 50)
    send_test_email()
    print("=" * 50)
    print("✨ 测试完成")

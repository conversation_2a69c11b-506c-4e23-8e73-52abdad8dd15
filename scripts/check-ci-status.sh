#!/bin/bash

# Relay Monitor - CI状态检查脚本
# 检查GitHub Actions CI的运行状态

set -e

# 导入通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh"

# 检查是否安装了gh CLI
check_gh_cli() {
    if ! command -v gh &> /dev/null; then
        log_error "GitHub CLI (gh) 未安装"
        log_info "请安装GitHub CLI: https://cli.github.com/"
        log_info "macOS: brew install gh"
        log_info "Ubuntu: sudo apt install gh"
        exit 1
    fi
}

# 检查是否已登录GitHub
check_gh_auth() {
    if ! gh auth status &> /dev/null; then
        log_error "未登录GitHub CLI"
        log_info "请先登录: gh auth login"
        exit 1
    fi
}

# 获取最新的工作流运行状态
get_workflow_status() {
    local branch=${1:-"dev"}
    
    log_info "检查 $branch 分支的CI状态..."
    
    # 获取最新的工作流运行
    local workflow_runs=$(gh run list --branch "$branch" --limit 5 --json status,conclusion,workflowName,createdAt,url)
    
    if [ -z "$workflow_runs" ] || [ "$workflow_runs" = "[]" ]; then
        log_warning "未找到 $branch 分支的工作流运行记录"
        return 1
    fi
    
    echo "$workflow_runs"
}

# 显示工作流状态
display_status() {
    local branch=${1:-"dev"}
    local runs=$(get_workflow_status "$branch")
    
    if [ $? -ne 0 ]; then
        return 1
    fi
    
    echo
    log_info "📊 最近的CI运行状态 ($branch 分支):"
    echo "=================================================="
    
    # 解析JSON并显示状态
    echo "$runs" | jq -r '.[] | "\(.workflowName) - \(.status) - \(.conclusion // "running") - \(.createdAt) - \(.url)"' | while IFS=' - ' read -r workflow status conclusion created_at url; do
        local status_icon=""
        local status_color=""
        
        case "$conclusion" in
            "success")
                status_icon="✅"
                status_color="$GREEN"
                ;;
            "failure")
                status_icon="❌"
                status_color="$RED"
                ;;
            "cancelled")
                status_icon="⏹️"
                status_color="$YELLOW"
                ;;
            "running"|"null")
                status_icon="🔄"
                status_color="$BLUE"
                ;;
            *)
                status_icon="❓"
                status_color="$NC"
                ;;
        esac
        
        echo -e "${status_color}${status_icon} ${workflow}${NC}"
        echo "   状态: $status"
        if [ "$conclusion" != "null" ] && [ "$conclusion" != "running" ]; then
            echo "   结果: $conclusion"
        fi
        echo "   时间: $created_at"
        echo "   链接: $url"
        echo
    done
}

# 等待CI完成
wait_for_ci() {
    local branch=${1:-"dev"}
    local timeout=${2:-300}  # 5分钟超时
    local start_time=$(date +%s)
    
    log_info "等待 $branch 分支的CI完成..."
    
    while true; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        if [ $elapsed -gt $timeout ]; then
            log_error "等待超时 (${timeout}秒)"
            return 1
        fi
        
        local runs=$(get_workflow_status "$branch")
        if [ $? -ne 0 ]; then
            log_error "无法获取工作流状态"
            return 1
        fi
        
        # 检查最新运行是否完成
        local latest_status=$(echo "$runs" | jq -r '.[0].status')
        local latest_conclusion=$(echo "$runs" | jq -r '.[0].conclusion')
        
        if [ "$latest_status" = "completed" ]; then
            if [ "$latest_conclusion" = "success" ]; then
                log_success "CI运行成功！"
                return 0
            else
                log_error "CI运行失败: $latest_conclusion"
                return 1
            fi
        fi
        
        echo -n "."
        sleep 10
    done
}

# 显示帮助信息
show_help() {
    cat << EOF
🔍 Relay Monitor - CI状态检查工具

使用方法:
    $0 [选项] [分支名]

选项:
    -s, --status        显示CI状态 (默认)
    -w, --wait          等待CI完成
    -t, --timeout SEC   设置等待超时时间 (默认: 300秒)
    -h, --help          显示此帮助信息

示例:
    $0                  # 显示dev分支CI状态
    $0 main             # 显示main分支CI状态
    $0 -w dev           # 等待dev分支CI完成
    $0 -w -t 600 dev    # 等待dev分支CI完成，超时600秒

环境要求:
    - GitHub CLI (gh)
    - jq (JSON处理工具)

EOF
}

# 主函数
main() {
    local action="status"
    local branch="dev"
    local timeout=300
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--status)
                action="status"
                shift
                ;;
            -w|--wait)
                action="wait"
                shift
                ;;
            -t|--timeout)
                timeout="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            -*)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
            *)
                branch="$1"
                shift
                ;;
        esac
    done
    
    echo "🔍 Relay Monitor CI状态检查"
    echo "=========================="
    
    # 检查依赖
    check_gh_cli
    check_gh_auth
    
    # 检查jq
    if ! command -v jq &> /dev/null; then
        log_error "jq 未安装"
        log_info "请安装jq: brew install jq 或 sudo apt install jq"
        exit 1
    fi
    
    # 执行操作
    case $action in
        status)
            display_status "$branch"
            ;;
        wait)
            wait_for_ci "$branch" "$timeout"
            ;;
    esac
}

# 运行主函数
main "$@"

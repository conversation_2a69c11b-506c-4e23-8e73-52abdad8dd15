#!/bin/bash

# Docker Build and Push Script for Relay Monitor
# 用于构建Docker镜像并推送到Docker Hub的脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
DOCKER_USERNAME="ljh740"
IMAGE_NAME="relay-monitor"
DOCKER_REPO="${DOCKER_USERNAME}/${IMAGE_NAME}"
DOCKERFILE="Dockerfile.optimized"

# 日志函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}" >&2
}

info() {
    echo -e "${CYAN}[$(date +'%Y-%m-%d %H:%M:%S')] ℹ️  $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${PURPLE}Relay Monitor Docker Build & Push Script${NC}"
    echo ""
    echo "用法: $0 [选项] <tag>"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -l, --latest            同时标记为latest标签"
    echo "  -f, --force             强制重新构建（不使用缓存）"
    echo "  -p, --push-only         仅推送，不构建（假设镜像已存在）"
    echo "  -t, --test              测试模式，不推送到Docker Hub"
    echo "  --no-cache              构建时不使用缓存"
    echo "  --platform PLATFORM     指定构建平台 (默认: linux/amd64,linux/arm64)"
    echo ""
    echo "参数:"
    echo "  tag                     Docker镜像标签 (必需)"
    echo ""
    echo "示例:"
    echo "  $0 v1.0.0                    # 构建并推送 v1.0.0 标签"
    echo "  $0 -l v1.0.0                 # 构建并推送 v1.0.0 和 latest 标签"
    echo "  $0 -f v1.0.0                 # 强制重新构建 v1.0.0"
    echo "  $0 -t v1.0.0                 # 测试构建 v1.0.0 (不推送)"
    echo "  $0 -p v1.0.0                 # 仅推送已存在的 v1.0.0 镜像"
    echo ""
    echo "环境变量:"
    echo "  DOCKER_USERNAME             Docker Hub用户名 (默认: ljh740)"
    echo "  DOCKER_PASSWORD              Docker Hub密码或访问令牌"
    echo ""
}

# 检查依赖
check_dependencies() {
    log "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        error "Docker未安装或不在PATH中"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        error "Git未安装或不在PATH中"
        exit 1
    fi
    
    success "依赖检查通过"
}

# 检查Docker登录状态
check_docker_login() {
    log "检查Docker Hub登录状态..."
    
    if ! docker info | grep -q "Username: ${DOCKER_USERNAME}"; then
        warning "未登录到Docker Hub"
        
        if [ -n "$DOCKER_PASSWORD" ]; then
            log "使用环境变量中的密码登录..."
            echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
        else
            log "请登录到Docker Hub:"
            docker login -u "$DOCKER_USERNAME"
        fi
    else
        success "已登录到Docker Hub (用户: ${DOCKER_USERNAME})"
    fi
}

# 获取Git信息
get_git_info() {
    log "获取Git信息..."
    
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        error "当前目录不是Git仓库"
        exit 1
    fi
    
    GIT_COMMIT=$(git rev-parse --short HEAD)
    GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
    GIT_TAG=$(git describe --tags --exact-match 2>/dev/null || echo "")
    BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    info "Git提交: ${GIT_COMMIT}"
    info "Git分支: ${GIT_BRANCH}"
    info "Git标签: ${GIT_TAG:-"无"}"
    info "构建时间: ${BUILD_DATE}"
}

# 构建Docker镜像
build_image() {
    local tag=$1
    local use_cache=$2
    local platform=$3
    
    log "开始构建Docker镜像..."
    
    local build_args=(
        "--file" "$DOCKERFILE"
        "--tag" "${DOCKER_REPO}:${tag}"
        "--build-arg" "BUILD_DATE=${BUILD_DATE}"
        "--build-arg" "VERSION=${tag}"
        "--build-arg" "VCS_REF=${GIT_COMMIT}"
    )
    
    if [ "$use_cache" = "false" ]; then
        build_args+=("--no-cache")
        warning "使用 --no-cache 构建"
    fi
    
    if [ -n "$platform" ]; then
        build_args+=("--platform" "$platform")
        info "构建平台: ${platform}"
    fi
    
    build_args+=(".")
    
    info "构建命令: docker build ${build_args[*]}"
    
    if docker build "${build_args[@]}"; then
        success "镜像构建成功: ${DOCKER_REPO}:${tag}"
    else
        error "镜像构建失败"
        exit 1
    fi
}

# 标记latest标签
tag_latest() {
    local tag=$1
    
    log "标记latest标签..."
    
    if docker tag "${DOCKER_REPO}:${tag}" "${DOCKER_REPO}:latest"; then
        success "latest标签创建成功"
    else
        error "latest标签创建失败"
        exit 1
    fi
}

# 推送镜像
push_image() {
    local tag=$1
    local push_latest=$2
    
    log "推送镜像到Docker Hub..."
    
    # 推送指定标签
    info "推送 ${DOCKER_REPO}:${tag}..."
    if docker push "${DOCKER_REPO}:${tag}"; then
        success "镜像推送成功: ${DOCKER_REPO}:${tag}"
    else
        error "镜像推送失败: ${DOCKER_REPO}:${tag}"
        exit 1
    fi
    
    # 推送latest标签
    if [ "$push_latest" = "true" ]; then
        info "推送 ${DOCKER_REPO}:latest..."
        if docker push "${DOCKER_REPO}:latest"; then
            success "镜像推送成功: ${DOCKER_REPO}:latest"
        else
            error "镜像推送失败: ${DOCKER_REPO}:latest"
            exit 1
        fi
    fi
}

# 显示镜像信息
show_image_info() {
    local tag=$1
    
    log "镜像信息:"
    docker images "${DOCKER_REPO}:${tag}" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}"
    
    echo ""
    info "Docker Hub链接:"
    echo "  https://hub.docker.com/r/${DOCKER_REPO}/tags"
    echo ""
    info "拉取命令:"
    echo "  docker pull ${DOCKER_REPO}:${tag}"
    if [ "$PUSH_LATEST" = "true" ]; then
        echo "  docker pull ${DOCKER_REPO}:latest"
    fi
}

# 主函数
main() {
    # 默认参数
    TAG=""
    PUSH_LATEST=false
    FORCE_BUILD=false
    PUSH_ONLY=false
    TEST_MODE=false
    USE_CACHE=true
    PLATFORM=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -l|--latest)
                PUSH_LATEST=true
                shift
                ;;
            -f|--force)
                FORCE_BUILD=true
                USE_CACHE=false
                shift
                ;;
            -p|--push-only)
                PUSH_ONLY=true
                shift
                ;;
            -t|--test)
                TEST_MODE=true
                shift
                ;;
            --no-cache)
                USE_CACHE=false
                shift
                ;;
            --platform)
                PLATFORM="$2"
                shift 2
                ;;
            -*)
                error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [ -z "$TAG" ]; then
                    TAG="$1"
                else
                    error "多余的参数: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 检查必需参数
    if [ -z "$TAG" ]; then
        error "缺少必需参数: tag"
        show_help
        exit 1
    fi
    
    # 验证标签格式
    if [[ ! "$TAG" =~ ^[a-zA-Z0-9._-]+$ ]]; then
        error "无效的标签格式: $TAG"
        exit 1
    fi
    
    echo -e "${PURPLE}🐳 Relay Monitor Docker Build & Push Script${NC}"
    echo -e "${PURPLE}===========================================${NC}"
    echo ""
    
    # 显示配置
    info "配置信息:"
    echo "  Docker仓库: ${DOCKER_REPO}"
    echo "  标签: ${TAG}"
    echo "  推送latest: ${PUSH_LATEST}"
    echo "  强制构建: ${FORCE_BUILD}"
    echo "  仅推送: ${PUSH_ONLY}"
    echo "  测试模式: ${TEST_MODE}"
    echo "  使用缓存: ${USE_CACHE}"
    echo "  构建平台: ${PLATFORM:-"默认"}"
    echo ""
    
    # 执行步骤
    check_dependencies
    get_git_info
    
    if [ "$TEST_MODE" = "false" ] && [ "$PUSH_ONLY" = "false" ]; then
        check_docker_login
    fi
    
    if [ "$PUSH_ONLY" = "false" ]; then
        build_image "$TAG" "$USE_CACHE" "$PLATFORM"
        
        if [ "$PUSH_LATEST" = "true" ]; then
            tag_latest "$TAG"
        fi
    fi
    
    if [ "$TEST_MODE" = "false" ]; then
        push_image "$TAG" "$PUSH_LATEST"
    else
        warning "测试模式：跳过推送步骤"
    fi
    
    show_image_info "$TAG"
    
    echo ""
    success "🎉 所有操作完成！"
    
    if [ "$TEST_MODE" = "true" ]; then
        warning "这是测试模式，镜像未推送到Docker Hub"
    fi
}

# 运行主函数
main "$@"

#!/bin/bash

# Quick Docker Build and Push Script
# 快速构建并推送Docker镜像到Docker Hub

set -e

# 配置
DOCKER_USERNAME="ljh740"
IMAGE_NAME="relay-monitor"
DOCKER_REPO="${DOCKER_USERNAME}/${IMAGE_NAME}"

# 颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}" >&2
}

# 检查参数
if [ $# -eq 0 ]; then
    echo "用法: $0 <tag> [latest]"
    echo "示例: $0 v1.0.0        # 推送 v1.0.0"
    echo "示例: $0 v1.0.0 latest # 推送 v1.0.0 和 latest"
    exit 1
fi

TAG=$1
PUSH_LATEST=$2

# 获取Git信息
GIT_COMMIT=$(git rev-parse --short HEAD)
BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

log "开始构建镜像: ${DOCKER_REPO}:${TAG}"

# 构建镜像
docker build \
    --file Dockerfile.optimized \
    --tag "${DOCKER_REPO}:${TAG}" \
    --build-arg BUILD_DATE="${BUILD_DATE}" \
    --build-arg VERSION="${TAG}" \
    --build-arg VCS_REF="${GIT_COMMIT}" \
    .

success "镜像构建完成"

# 标记latest
if [ "$PUSH_LATEST" = "latest" ]; then
    log "标记latest标签"
    docker tag "${DOCKER_REPO}:${TAG}" "${DOCKER_REPO}:latest"
    success "latest标签创建完成"
fi

# 推送镜像
log "推送镜像到Docker Hub"
docker push "${DOCKER_REPO}:${TAG}"
success "推送完成: ${DOCKER_REPO}:${TAG}"

if [ "$PUSH_LATEST" = "latest" ]; then
    docker push "${DOCKER_REPO}:latest"
    success "推送完成: ${DOCKER_REPO}:latest"
fi

echo ""
success "🎉 所有操作完成！"
echo ""
echo "Docker Hub链接: https://hub.docker.com/r/${DOCKER_REPO}/tags"
echo "拉取命令: docker pull ${DOCKER_REPO}:${TAG}"
if [ "$PUSH_LATEST" = "latest" ]; then
    echo "拉取命令: docker pull ${DOCKER_REPO}:latest"
fi

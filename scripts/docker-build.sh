#!/bin/bash

# Relay Monitor - Docker Build Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" >&2
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

# Default values
IMAGE_NAME="relay-monitor"
VERSION="latest"
BUILD_ARGS=""
PUSH=false
REGISTRY=""
PLATFORM=""

# Help function
show_help() {
    cat << EOF
Relay Monitor Docker Build Script

Usage: $0 [OPTIONS]

Options:
    -h, --help              Show this help message
    -v, --version VERSION   Set image version (default: latest)
    -n, --name NAME         Set image name (default: relay-monitor)
    -p, --push              Push image to registry after build
    -r, --registry REGISTRY Set registry URL for push
    --platform PLATFORM    Set target platform (e.g., linux/amd64,linux/arm64)
    --no-cache              Build without using cache
    --build-arg ARG=VALUE   Pass build argument

Examples:
    $0                                          # Build with defaults
    $0 -v 1.0.0 -p -r docker.io/myuser         # Build version 1.0.0 and push
    $0 --platform linux/amd64,linux/arm64      # Multi-platform build
    $0 --build-arg VERSION=1.0.0               # Pass custom build arg

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -p|--push)
            PUSH=true
            shift
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        --platform)
            PLATFORM="$2"
            shift 2
            ;;
        --no-cache)
            BUILD_ARGS="$BUILD_ARGS --no-cache"
            shift
            ;;
        --build-arg)
            BUILD_ARGS="$BUILD_ARGS --build-arg $2"
            shift 2
            ;;
        *)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Set full image name
if [ -n "$REGISTRY" ]; then
    FULL_IMAGE_NAME="$REGISTRY/$IMAGE_NAME:$VERSION"
else
    FULL_IMAGE_NAME="$IMAGE_NAME:$VERSION"
fi

# Get build information
BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')
VCS_REF=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# Add build arguments
BUILD_ARGS="$BUILD_ARGS --build-arg BUILD_DATE=$BUILD_DATE"
BUILD_ARGS="$BUILD_ARGS --build-arg VERSION=$VERSION"
BUILD_ARGS="$BUILD_ARGS --build-arg VCS_REF=$VCS_REF"

# Add platform if specified
if [ -n "$PLATFORM" ]; then
    BUILD_ARGS="$BUILD_ARGS --platform $PLATFORM"
fi

log "Starting Docker build..."
log "Image: $FULL_IMAGE_NAME"
log "Version: $VERSION"
log "Build Date: $BUILD_DATE"
log "VCS Ref: $VCS_REF"

# Check if Dockerfile exists
if [ ! -f "Dockerfile" ]; then
    error "Dockerfile not found in current directory"
    exit 1
fi

# Build the image
log "Building Docker image..."
if docker build $BUILD_ARGS -t "$FULL_IMAGE_NAME" .; then
    success "Docker image built successfully: $FULL_IMAGE_NAME"
else
    error "Docker build failed"
    exit 1
fi

# Tag as latest if not already
if [ "$VERSION" != "latest" ]; then
    LATEST_TAG="${FULL_IMAGE_NAME%:*}:latest"
    log "Tagging as latest: $LATEST_TAG"
    docker tag "$FULL_IMAGE_NAME" "$LATEST_TAG"
fi

# Push if requested
if [ "$PUSH" = true ]; then
    if [ -z "$REGISTRY" ]; then
        warn "No registry specified, pushing to default registry"
    fi
    
    log "Pushing image to registry..."
    if docker push "$FULL_IMAGE_NAME"; then
        success "Image pushed successfully: $FULL_IMAGE_NAME"
        
        # Push latest tag if applicable
        if [ "$VERSION" != "latest" ]; then
            docker push "$LATEST_TAG"
            success "Latest tag pushed: $LATEST_TAG"
        fi
    else
        error "Failed to push image"
        exit 1
    fi
fi

# Show image information
log "Build completed successfully!"
docker images | grep "$IMAGE_NAME" | head -5

success "Docker build script completed!"

#!/bin/bash

# Relay Monitor 部署设置脚本
# 用于创建必要的Docker卷和网络，解决配置持久化问题

set -e

echo "🚀 Relay Monitor 部署设置"
echo "=========================="

# 检查Docker是否运行
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

echo "📦 创建Docker卷..."

# 创建数据卷
if ! docker volume inspect relay_monitor_data >/dev/null 2>&1; then
    docker volume create relay_monitor_data
    echo "✅ 创建数据卷: relay_monitor_data"
else
    echo "ℹ️  数据卷已存在: relay_monitor_data"
fi

# 创建日志卷
if ! docker volume inspect relay_monitor_logs >/dev/null 2>&1; then
    docker volume create relay_monitor_logs
    echo "✅ 创建日志卷: relay_monitor_logs"
else
    echo "ℹ️  日志卷已存在: relay_monitor_logs"
fi

# 创建配置卷（解决交易对配置丢失问题）
if ! docker volume inspect relay_monitor_config >/dev/null 2>&1; then
    docker volume create relay_monitor_config
    echo "✅ 创建配置卷: relay_monitor_config"
else
    echo "ℹ️  配置卷已存在: relay_monitor_config"
fi

echo ""
echo "🌐 创建Docker网络..."

# 创建网络
if ! docker network inspect relay-monitor-network >/dev/null 2>&1; then
    docker network create relay-monitor-network
    echo "✅ 创建网络: relay-monitor-network"
else
    echo "ℹ️  网络已存在: relay-monitor-network"
fi

echo ""
echo "🔧 初始化配置文件..."

# 创建临时容器来初始化配置文件
echo "正在初始化配置文件到配置卷..."
docker run --rm \
    -v relay_monitor_config:/app/config \
    ljh740/relay-monitor:latest \
    sh -c "
        if [ ! -f /app/config/config.toml ]; then
            echo '正在复制默认配置文件...'
            cp -r /app/config.default/* /app/config/ 2>/dev/null || true
            cp /app/config.default/config.toml /app/config/config.toml 2>/dev/null || true
            echo '配置文件初始化完成'
        else
            echo '配置文件已存在，跳过初始化'
        fi
    "

echo ""
echo "✅ 设置完成！"
echo ""
echo "📋 下一步操作："
echo "1. 启动服务: docker-compose -f docker-compose.deploy.yml up -d"
echo "2. 查看日志: docker-compose -f docker-compose.deploy.yml logs -f"
echo "3. 访问管理界面: http://localhost:5001"
echo ""
echo "🔧 故障排除："
echo "- 如果遇到权限问题，请检查Docker卷权限"
echo "- 如果配置丢失，请重新运行此脚本"
echo "- 查看详细日志: docker-compose -f docker-compose.deploy.yml logs relay-monitor"
echo ""
echo "📚 重要说明："
echo "- 交易对配置现在会持久化保存在 relay_monitor_config 卷中"
echo "- 容器重启后配置不会丢失"
echo "- 管理员密码和交易对配置都会保持"

#!/bin/bash

# Relay Monitor - Docker Hub部署脚本
# 直接从Docker Hub拉取镜像并部署，无需本地构建
#
# 使用方法:
#   ./scripts/deploy-from-hub.sh
#   ./scripts/deploy-from-hub.sh -p 8080
#   ./scripts/deploy-from-hub.sh -t v1.0.0
#   ./scripts/deploy-from-hub.sh --help

set -e

# 默认配置
DEFAULT_PORT=5000
DEFAULT_TAG="latest"
DOCKER_IMAGE="ljh740/relay-monitor"
COMPOSE_FILE="docker-compose.deploy.yml"

# 导入通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh"

# 显示帮助信息
show_help() {
    cat << EOF
🐳 Relay Monitor - Docker Hub部署脚本

使用方法:
    $0 [选项]

选项:
    -p, --port PORT     设置端口号 (默认: $DEFAULT_PORT)
    -t, --tag TAG       设置镜像标签 (默认: $DEFAULT_TAG)
    -u, --update        更新到最新镜像
    -s, --stop          停止服务
    -r, --restart       重启服务
    -l, --logs          查看日志
    --status            查看服务状态
    --cleanup           清理未使用的镜像
    -h, --help          显示此帮助信息

示例:
    $0                          # 使用默认配置部署
    $0 -p 8080                  # 在8080端口部署
    $0 -t v1.0.0                # 部署指定版本
    $0 -p 8080 -t v1.0.0        # 指定端口和版本
    $0 --update                 # 更新到最新版本
    $0 --stop                   # 停止服务
    $0 --logs                   # 查看日志

环境要求:
    - Docker
    - Docker Compose

EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查compose文件
check_compose_file() {
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "找不到compose文件: $COMPOSE_FILE"
        log_info "请确保在项目根目录运行此脚本"
        exit 1
    fi
}

# 拉取最新镜像
pull_image() {
    local tag=$1
    log_info "拉取镜像: ${DOCKER_IMAGE}:${tag}"
    
    if docker pull "${DOCKER_IMAGE}:${tag}"; then
        log_success "镜像拉取成功"
    else
        log_error "镜像拉取失败"
        exit 1
    fi
}

# 部署服务
deploy_service() {
    local port=$1
    local tag=$2
    
    log_info "部署配置:"
    log_info "  镜像: ${DOCKER_IMAGE}:${tag}"
    log_info "  端口: ${port}"
    
    # 设置环境变量
    export HOST_PORT=$port
    export IMAGE_TAG=$tag
    
    # 更新compose文件中的镜像标签
    if [ "$tag" != "latest" ]; then
        sed -i.bak "s|image: ${DOCKER_IMAGE}:latest|image: ${DOCKER_IMAGE}:${tag}|g" "$COMPOSE_FILE"
    fi
    
    # 启动服务
    log_info "启动服务..."
    if docker-compose -f "$COMPOSE_FILE" up -d; then
        log_success "服务启动成功"
        
        # 恢复compose文件
        if [ "$tag" != "latest" ] && [ -f "${COMPOSE_FILE}.bak" ]; then
            mv "${COMPOSE_FILE}.bak" "$COMPOSE_FILE"
        fi
        
        # 显示服务信息
        show_service_info $port
    else
        log_error "服务启动失败"
        
        # 恢复compose文件
        if [ -f "${COMPOSE_FILE}.bak" ]; then
            mv "${COMPOSE_FILE}.bak" "$COMPOSE_FILE"
        fi
        exit 1
    fi
}

# 显示服务信息
show_service_info() {
    local port=$1
    
    echo
    log_success "🎉 Relay Monitor部署完成！"
    echo
    log_info "服务信息:"
    log_info "  访问地址: http://localhost:${port}"
    log_info "  健康检查: http://localhost:${port}/health"
    log_info "  管理面板: http://localhost:${port}/admin"
    echo
    log_info "常用命令:"
    log_info "  查看日志: docker-compose -f $COMPOSE_FILE logs -f"
    log_info "  停止服务: docker-compose -f $COMPOSE_FILE down"
    log_info "  重启服务: docker-compose -f $COMPOSE_FILE restart"
    echo
}

# 停止服务
stop_service() {
    log_info "停止服务..."
    if docker-compose -f "$COMPOSE_FILE" down; then
        log_success "服务已停止"
    else
        log_error "停止服务失败"
        exit 1
    fi
}

# 重启服务
restart_service() {
    log_info "重启服务..."
    if docker-compose -f "$COMPOSE_FILE" restart; then
        log_success "服务已重启"
    else
        log_error "重启服务失败"
        exit 1
    fi
}

# 查看日志
show_logs() {
    log_info "显示服务日志..."
    docker-compose -f "$COMPOSE_FILE" logs -f
}

# 查看状态
show_status() {
    log_info "服务状态:"
    docker-compose -f "$COMPOSE_FILE" ps
    echo
    
    # 检查健康状态
    if docker-compose -f "$COMPOSE_FILE" ps | grep -q "Up"; then
        log_info "健康检查:"
        local port=$(docker-compose -f "$COMPOSE_FILE" port relay-monitor 5000 2>/dev/null | cut -d: -f2)
        if [ -n "$port" ]; then
            if curl -s "http://localhost:${port}/health" > /dev/null; then
                log_success "服务健康状态: 正常"
            else
                log_warning "服务健康状态: 异常"
            fi
        fi
    fi
}

# 清理镜像
cleanup_images() {
    log_info "清理未使用的镜像..."
    docker image prune -f
    log_success "清理完成"
}

# 更新服务
update_service() {
    log_info "更新服务到最新版本..."
    
    # 拉取最新镜像
    pull_image "latest"
    
    # 重新部署
    docker-compose -f "$COMPOSE_FILE" up -d
    
    log_success "服务更新完成"
}

# 主函数
main() {
    local port=$DEFAULT_PORT
    local tag=$DEFAULT_TAG
    local action="deploy"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -p|--port)
                port="$2"
                shift 2
                ;;
            -t|--tag)
                tag="$2"
                shift 2
                ;;
            -u|--update)
                action="update"
                shift
                ;;
            -s|--stop)
                action="stop"
                shift
                ;;
            -r|--restart)
                action="restart"
                shift
                ;;
            -l|--logs)
                action="logs"
                shift
                ;;
            --status)
                action="status"
                shift
                ;;
            --cleanup)
                action="cleanup"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查依赖和文件
    check_dependencies
    check_compose_file
    
    # 执行操作
    case $action in
        deploy)
            pull_image "$tag"
            deploy_service "$port" "$tag"
            ;;
        update)
            update_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        logs)
            show_logs
            ;;
        status)
            show_status
            ;;
        cleanup)
            cleanup_images
            ;;
    esac
}

# 运行主函数
main "$@"

#!/bin/bash

# Relay Monitor - Docker Compose验证脚本
# 验证所有Docker Compose文件的语法和配置

set -e

# 导入通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh"

# 验证Docker Compose文件
validate_compose_file() {
    local file=$1
    local description=$2
    
    log_info "验证 $file ($description)..."
    
    if [ ! -f "$file" ]; then
        log_warning "文件不存在: $file"
        return 1
    fi
    
    # 检查语法
    if docker compose -f "$file" config --quiet 2>/dev/null; then
        log_success "$file 语法正确"
    else
        log_error "$file 语法错误"
        echo "详细错误信息:"
        docker compose -f "$file" config 2>&1 || true
        return 1
    fi
    
    # 检查是否包含废弃的version字段
    if grep -q "^version:" "$file"; then
        log_warning "$file 包含废弃的version字段，建议移除"
    fi
    
    return 0
}

# 验证组合配置
validate_compose_combinations() {
    log_info "验证Docker Compose组合配置..."
    
    # 验证开发环境组合
    if [ -f "docker-compose.yml" ] && [ -f "docker-compose.dev.yml" ]; then
        log_info "验证开发环境组合..."
        if docker compose -f docker-compose.yml -f docker-compose.dev.yml config --quiet 2>/dev/null; then
            log_success "开发环境组合配置正确"
        else
            log_error "开发环境组合配置错误"
            return 1
        fi
    fi
    
    # 验证生产环境组合
    if [ -f "docker-compose.yml" ] && [ -f "docker-compose.prod.yml" ]; then
        log_info "验证生产环境组合..."
        if docker compose -f docker-compose.yml -f docker-compose.prod.yml config --quiet 2>/dev/null; then
            log_success "生产环境组合配置正确"
        else
            log_error "生产环境组合配置错误"
            return 1
        fi
    fi
}

# 检查镜像和构建配置
check_build_config() {
    log_info "检查构建配置..."
    
    # 检查Dockerfile
    local dockerfiles=("Dockerfile" "Dockerfile.optimized")
    for dockerfile in "${dockerfiles[@]}"; do
        if [ -f "$dockerfile" ]; then
            log_success "找到 $dockerfile"
        else
            log_warning "未找到 $dockerfile"
        fi
    done
    
    # 检查.dockerignore
    if [ -f ".dockerignore" ]; then
        log_success "找到 .dockerignore"
    else
        log_warning "未找到 .dockerignore，建议创建以优化构建"
    fi
}

# 检查环境变量文件
check_env_files() {
    log_info "检查环境变量文件..."
    
    local env_files=(".env" ".env.example" ".env.local")
    for env_file in "${env_files[@]}"; do
        if [ -f "$env_file" ]; then
            log_success "找到 $env_file"
        else
            log_info "未找到 $env_file"
        fi
    done
}

# 检查数据目录
check_data_directories() {
    log_info "检查数据目录..."
    
    local data_dirs=("docker-data" "docker-data/data" "docker-data/logs")
    for dir in "${data_dirs[@]}"; do
        if [ -d "$dir" ]; then
            log_success "找到目录 $dir"
        else
            log_info "目录不存在: $dir (运行时会自动创建)"
        fi
    done
}

# 显示配置摘要
show_config_summary() {
    log_info "生成配置摘要..."
    
    echo
    echo "📋 Docker Compose配置摘要"
    echo "=========================="
    
    # 显示所有compose文件
    echo "🐳 Docker Compose文件:"
    for file in docker-compose*.yml; do
        if [ -f "$file" ]; then
            echo "  - $file"
        fi
    done
    
    echo
    echo "🔧 构建文件:"
    for file in Dockerfile*; do
        if [ -f "$file" ]; then
            echo "  - $file"
        fi
    done
    
    echo
    echo "⚙️ 环境配置:"
    for file in .env*; do
        if [ -f "$file" ]; then
            echo "  - $file"
        fi
    done
    
    echo
    echo "📁 数据目录:"
    if [ -d "docker-data" ]; then
        echo "  - docker-data/ (存在)"
    else
        echo "  - docker-data/ (将自动创建)"
    fi
    
    echo
}

# 提供使用建议
show_usage_tips() {
    echo "💡 使用建议"
    echo "=========="
    echo
    echo "开发环境:"
    echo "  docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d"
    echo
    echo "生产环境:"
    echo "  docker compose -f docker-compose.prod.yml up -d"
    echo
    echo "部署环境 (使用预构建镜像):"
    echo "  docker compose -f docker-compose.deploy.yml up -d"
    echo
    echo "查看日志:"
    echo "  docker compose logs -f relay-monitor"
    echo
    echo "停止服务:"
    echo "  docker compose down"
    echo
}

# 主函数
main() {
    echo "🐳 Relay Monitor - Docker Compose验证"
    echo "===================================="
    echo
    
    # 检查Docker环境
    check_docker
    echo
    
    # 验证各个compose文件
    local files_validated=0
    local files_failed=0

    # 主要的compose文件列表
    local compose_files=(
        "docker-compose.yml:主配置文件"
        "docker-compose.dev.yml:开发环境覆盖"
        "docker-compose.prod.yml:生产环境配置"
        "docker-compose.deploy.yml:部署配置"
    )

    for file_desc in "${compose_files[@]}"; do
        local file="${file_desc%%:*}"
        local desc="${file_desc##*:}"
        if validate_compose_file "$file" "$desc"; then
            files_validated=$((files_validated + 1))
        else
            files_failed=$((files_failed + 1))
        fi
        echo
    done
    
    # 验证组合配置
    validate_compose_combinations
    echo
    
    # 其他检查
    check_build_config
    echo
    check_env_files
    echo
    check_data_directories
    echo
    
    # 显示摘要
    show_config_summary
    show_usage_tips
    
    # 最终结果
    echo "🎯 验证结果"
    echo "=========="
    echo "验证通过: $files_validated 个文件"
    echo "验证失败: $files_failed 个文件"
    
    if [ $files_failed -eq 0 ]; then
        log_success "所有Docker Compose配置验证通过！"
        exit 0
    else
        log_error "部分配置验证失败，请检查上述错误信息"
        exit 1
    fi
}

# 运行主函数
main "$@"

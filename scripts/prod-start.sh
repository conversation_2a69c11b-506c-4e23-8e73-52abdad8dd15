#!/bin/bash

# Relay Monitor - 生产环境启动脚本

set -e

# 导入通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh"

echo "🚀 Relay Monitor - 生产环境启动"
echo "==============================="

# 检查Docker
if ! command -v docker &> /dev/null; then
    log_error "Docker未安装"
    exit 1
fi

# 检查配置文件
if [ ! -f "docker-compose.prod.yml" ]; then
    log_error "未找到docker-compose.prod.yml"
    exit 1
fi

# 检查环境变量
if [ ! -f ".env" ] && [ ! -f "config/config.toml" ]; then
    log_warning "未找到配置文件，请确保已正确配置"
fi

# 显示当前配置
log_info "生产环境配置:"
echo "  📄 配置文件: docker-compose.prod.yml"
echo "  🔧 命令: docker compose -f docker-compose.prod.yml up -d"

# 检查端口（生产环境默认5000）
HOST_PORT=${HOST_PORT:-5000}
log_info "检查生产环境端口 $HOST_PORT..."

if lsof -i :$HOST_PORT &> /dev/null; then
    log_warning "端口 $HOST_PORT 已被占用"
    echo "正在使用端口 $HOST_PORT 的进程:"
    lsof -i :$HOST_PORT
    echo
    read -p "是否继续启动? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "启动已取消"
        exit 1
    fi
fi

# 拉取最新镜像
log_info "拉取最新镜像..."
docker compose -f docker-compose.prod.yml pull

# 启动服务
log_info "启动生产环境..."
docker compose -f docker-compose.prod.yml up -d

# 等待服务启动
log_info "等待服务启动..."
sleep 10

# 检查服务状态
if docker compose -f docker-compose.prod.yml ps | grep -q "Up"; then
    log_success "生产环境启动成功!"
    echo
    echo "📊 访问地址:"
    echo "  🌐 Web界面: http://localhost:$HOST_PORT"
    echo "  🔧 管理面板: http://localhost:$HOST_PORT/admin"
    echo
    echo "🔍 查看日志:"
    echo "  docker compose -f docker-compose.prod.yml logs -f"
    echo
    echo "⏹️ 停止服务:"
    echo "  docker compose -f docker-compose.prod.yml down"
    echo
    echo "📊 监控服务:"
    echo "  docker compose -f docker-compose.prod.yml ps"
    echo "  docker stats"
else
    log_error "服务启动失败"
    echo "查看日志:"
    docker compose -f docker-compose.prod.yml logs
    exit 1
fi

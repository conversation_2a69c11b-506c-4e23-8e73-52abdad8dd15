#!/bin/bash

# Relay Monitor - Configuration Initialization Script
# Simplifies configuration setup by using environment variables for sensitive data

set -e

# 导入通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh"

# 使用带时间戳的日志函数
log() { log_with_timestamp "$1"; }
error() { error_with_timestamp "$1"; }
warn() { warn_with_timestamp "$1"; }
success() { success_with_timestamp "$1"; }

# Help function
show_help() {
    cat << EOF
Relay Monitor Configuration Initialization Script

🚀 NEW: Zero-config startup available!
For most users: Just run 'python main.py web' and use the web interface.

This script is for advanced scenarios:
1. Batch configuration setup
2. CI/CD automation
3. Docker environment preparation
4. Legacy configuration migration

Usage: $0 [OPTIONS]

Options:
    -h, --help              Show this help message
    --interactive           Interactive configuration setup
    --minimal               Create minimal configuration files
    --example               Create example configuration with sample data

Examples:
    $0                      # Create minimal configuration
    $0 --interactive        # Interactive setup with prompts
    $0 --example           # Create example configuration

EOF
}

# Default values
INTERACTIVE=false
MINIMAL=true
EXAMPLE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --interactive)
            INTERACTIVE=true
            MINIMAL=false
            shift
            ;;
        --minimal)
            MINIMAL=true
            shift
            ;;
        --example)
            EXAMPLE=true
            MINIMAL=false
            shift
            ;;
        *)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Create .env file with only essential variables
create_env_file() {
    log "Creating .env file..."
    
    cat > .env << 'EOF'
# Relay Monitor - Environment Configuration
# Only essential and sensitive configuration here

# Security (REQUIRED - Change these!)
ADMIN_PASSWORD=admin123!!.
SMTP_API_KEY=your_smtp_dev_api_key_here
BARK_KEYS=your_bark_key_1,your_bark_key_2

# Network Configuration
HOST_PORT=5000
DEV_HOST_PORT=5001

# Application Environment
DEBUG=false
LOG_LEVEL=INFO
FLASK_ENV=production

# Data Paths (for Docker)
DATA_PATH=./docker-data/data
LOGS_PATH=./docker-data/logs
EOF

    success "Created .env file with essential configuration"
    warn "IMPORTANT: Please update ADMIN_PASSWORD, SMTP_API_KEY, and BARK_KEYS in .env file"
}

# Create simplified config.toml focused on business logic
create_config_file() {
    log "Creating config.toml file..."
    
    if [ "$EXAMPLE" = true ]; then
        # Create example with sample monitoring pairs
        cat > config/config.toml << 'EOF'
# Relay Monitor - Business Configuration
# Monitoring pairs and application behavior settings

[[monitor_pairs]]
name = "ARB_ETH_to_POLYGON_POL"
description = "Monitor Arbitrum ETH to Polygon POL bridge price"
origin_chain = "arbitrum"
destination_chain = "polygon"
origin_token = "ETH"
destination_token = "POL"
amount = "1.0"
enabled = true
alert_threshold_percent = 2.0

[[monitor_pairs]]
name = "ABSTRACT_ETH_to_POLYGON_USDT"
description = "Monitor Abstract ETH to Polygon USDT bridge price"
origin_chain = "abstract"
destination_chain = "polygon"
origin_token = "ETH"
destination_token = "USDT"
amount = "1.0"
enabled = true
alert_threshold_percent = 2.5

[monitoring]
interval_seconds = 30
price_change_threshold_percent = 5.0
enabled = true
max_history_days = 15

[alerts]
enabled = true
rate_limit_minutes = 5

[alerts.console]
enabled = true

[alerts.smtp_dev]
enabled = true
to_emails = ["<EMAIL>"]
from_email = "<EMAIL>"

[alerts.bark]
enabled = true
timeout = 10
EOF
    else
        # Create minimal config
        cat > config/config.toml << 'EOF'
# Relay Monitor - Business Configuration
# Add your monitoring pairs and customize settings here

# Example monitoring pair (edit or add more)
[[monitor_pairs]]
name = "ARB_ETH_to_POLYGON_POL"
description = "Monitor Arbitrum ETH to Polygon POL bridge price"
origin_chain = "arbitrum"
destination_chain = "polygon"
origin_token = "ETH"
destination_token = "POL"
amount = "1.0"
enabled = true
alert_threshold_percent = 2.0

[monitoring]
interval_seconds = 30
price_change_threshold_percent = 5.0
enabled = true
max_history_days = 15

[alerts]
enabled = true
rate_limit_minutes = 5

[alerts.console]
enabled = true

[alerts.smtp_dev]
enabled = false  # Enable and configure in .env file

[alerts.bark]
enabled = false  # Enable and configure in .env file
EOF
    fi

    success "Created config.toml file with monitoring configuration"
}

# Interactive configuration
interactive_setup() {
    log "Starting interactive configuration setup..."
    
    # Admin password
    echo -n "Enter admin password (default: admin123!!.): "
    read -s admin_password
    echo
    admin_password=${admin_password:-admin123!!.}
    
    # SMTP API key
    echo -n "Enter SMTP.dev API key (optional): "
    read smtp_key
    
    # Bark keys
    echo -n "Enter Bark push notification keys (comma-separated, optional): "
    read bark_keys
    
    # Create .env with user input
    cat > .env << EOF
# Relay Monitor - Environment Configuration
ADMIN_PASSWORD=${admin_password}
SMTP_API_KEY=${smtp_key:-your_smtp_dev_api_key_here}
BARK_KEYS=${bark_keys:-your_bark_key_1,your_bark_key_2}

# Network Configuration
HOST_PORT=5000
DEV_HOST_PORT=5001

# Application Environment
DEBUG=false
LOG_LEVEL=INFO
FLASK_ENV=production

# Data Paths (for Docker)
DATA_PATH=./docker-data/data
LOGS_PATH=./docker-data/logs
EOF

    success "Created .env file with your settings"
}

# Main execution
main() {
    log "Initializing Relay Monitor configuration..."
    
    # Create config directory if it doesn't exist
    mkdir -p config
    
    # Check if files already exist
    if [ -f ".env" ] || [ -f "config/config.toml" ]; then
        warn "Configuration files already exist!"
        echo -n "Do you want to overwrite them? (y/N): "
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log "Configuration initialization cancelled"
            exit 0
        fi
    fi
    
    if [ "$INTERACTIVE" = true ]; then
        interactive_setup
    else
        create_env_file
    fi
    
    create_config_file
    
    # Create necessary directories
    mkdir -p docker-data/data
    mkdir -p docker-data/logs
    mkdir -p logs
    mkdir -p data
    
    success "Configuration initialization completed!"
    echo
    log "Next steps:"
    echo "1. Edit .env file to update sensitive information"
    echo "2. Edit config/config.toml to add your monitoring pairs"
    echo "3. Run: ./scripts/docker-deploy.sh"
    echo
    warn "Security reminder: Never commit .env file to version control!"
}

# Run main function
main

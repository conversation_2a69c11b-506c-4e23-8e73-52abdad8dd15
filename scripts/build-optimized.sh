#!/usr/bin/env bash

# Relay Monitor - Optimized Build Script
# Tests different optimization strategies

set -e

# 导入通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh"

# 使用带时间戳的日志函数
log() { log_with_timestamp "$1"; }
error() { error_with_timestamp "$1"; }
warn() { warn_with_timestamp "$1"; }
success() { success_with_timestamp "$1"; }

# Build configurations
builds="original:Dockerfile:relay-monitor:original optimized:Dockerfile.optimized:relay-monitor:optimized minimal:Dockerfile.minimal:relay-monitor:minimal"

log "Starting optimized build comparison..."

# Build each version
for build in $builds; do
    IFS=':' read -r name dockerfile tag <<< "$build"
    
    log "Building $name version using $dockerfile..."
    
    # Copy appropriate dockerignore
    if [ "$name" = "optimized" ] || [ "$name" = "minimal" ]; then
        cp .dockerignore.optimized .dockerignore.temp
    else
        cp .dockerignore .dockerignore.temp
    fi
    
    # Build with timing
    start_time=$(date +%s)
    
    if docker build -f "$dockerfile" --progress=plain -t "$tag" . > "build-$name.log" 2>&1; then
        end_time=$(date +%s)
        build_time=$((end_time - start_time))
        
        # Get image size
        size=$(docker images "$tag" --format "{{.Size}}")
        
        success "✅ $name build completed in ${build_time}s - Size: $size"
        
        # Store results
        echo "$name,$size,$build_time" >> build-results.csv
    else
        error "❌ $name build failed"
        echo "Build log for $name:"
        tail -20 "build-$name.log"
    fi
    
    # Restore original dockerignore
    rm -f .dockerignore.temp
done

# Create results summary
log "Build Results Summary:"
echo "=================================="
printf "%-12s %-12s %-12s\n" "Version" "Size" "Build Time"
echo "=================================="

if [ -f build-results.csv ]; then
    while IFS=',' read -r version size time; do
        printf "%-12s %-12s %-12ss\n" "$version" "$size" "$time"
    done < build-results.csv
fi

echo "=================================="

# Test the smallest image
log "Testing optimized images..."

for build in $builds; do
    IFS=':' read -r name dockerfile tag <<< "$build"
    
    if docker images "$tag" >/dev/null 2>&1; then
        log "Testing $name image..."
        
        # Quick test
        if [ "$name" = "minimal" ]; then
            # Minimal image doesn't have curl, so just check if it starts
            if timeout 30 docker run --rm "$tag" python -c "print('Test successful')" >/dev/null 2>&1; then
                success "✅ $name image test passed"
            else
                warn "⚠️  $name image test failed or timed out"
            fi
        else
            # Test with health check
            container_id=$(docker run -d -p 0:5000 "$tag")
            sleep 10
            
            port=$(docker port "$container_id" 5000 | cut -d: -f2)
            
            if curl -f "http://localhost:$port/health" >/dev/null 2>&1; then
                success "✅ $name image test passed"
            else
                warn "⚠️  $name image health check failed"
            fi
            
            docker stop "$container_id" >/dev/null 2>&1
            docker rm "$container_id" >/dev/null 2>&1
        fi
    fi
done

# Cleanup
rm -f build-results.csv build-*.log

log "Optimization analysis complete!"

# Show final recommendations
log "Recommendations:"
echo "1. Use 'optimized' version for production (Alpine-based, minimal deps)"
echo "2. Use 'minimal' version for ultra-secure environments (distroless)"
echo "3. Original version is good for development with full tooling"

success "Build optimization complete!"

# Docker推送示例

本文档展示如何使用我们的脚本将Relay Monitor推送到Docker Hub。

## 🚀 快速开始

### 1. 登录Docker Hub

```bash
# 方法1: 交互式登录
docker login

# 方法2: 使用环境变量
export DOCKER_PASSWORD="your_docker_hub_token"
```

### 2. 构建并推送镜像

```bash
# 基本用法 - 构建并推送v1.0.0
./scripts/docker-build-push.sh v1.0.0

# 同时推送latest标签
./scripts/docker-build-push.sh -l v1.0.0

# 快速推送（使用简化脚本）
./scripts/quick-push.sh v1.0.0 latest
```

## 📋 完整示例

### 示例1: 发布新版本

```bash
#!/bin/bash

# 设置版本号
VERSION="v1.2.0"

# 确保在正确的分支
git checkout main
git pull origin main

# 创建并推送标签
git tag $VERSION
git push origin $VERSION

# 构建并推送Docker镜像（包含latest标签）
./scripts/docker-build-push.sh -l $VERSION

echo "✅ 版本 $VERSION 发布完成！"
echo "🐳 Docker镜像: ljh740/relay-monitor:$VERSION"
echo "🔗 Docker Hub: https://hub.docker.com/r/ljh740/relay-monitor"
```

### 示例2: 测试构建

```bash
#!/bin/bash

# 测试构建（不推送到Docker Hub）
./scripts/docker-build-push.sh -t v1.0.0-test

# 本地运行测试
docker run -d \
  --name relay-monitor-test \
  -p 8080:5000 \
  -e TZ=Asia/Shanghai \
  ljh740/relay-monitor:v1.0.0-test

# 等待启动
sleep 10

# 测试健康检查
curl -f http://localhost:8080/health

# 清理测试容器
docker stop relay-monitor-test
docker rm relay-monitor-test
```

### 示例3: 强制重新构建

```bash
#!/bin/bash

# 强制重新构建（不使用缓存）
./scripts/docker-build-push.sh -f v1.0.0

# 或者清理所有缓存后构建
docker system prune -a -f
./scripts/docker-build-push.sh v1.0.0
```

## 🔧 环境配置

### Docker Hub访问令牌

1. 登录 [Docker Hub](https://hub.docker.com/)
2. 访问 Account Settings > Security
3. 创建新的访问令牌
4. 设置环境变量：

```bash
export DOCKER_PASSWORD="dckr_pat_your_token_here"
```

### GitHub Actions配置

在GitHub仓库设置中添加Secret：

- Name: `DOCKER_PASSWORD`
- Value: 你的Docker Hub访问令牌

## 📊 构建结果示例

成功构建后的输出：

```
🐳 Relay Monitor Docker Build & Push Script
===========================================

[2025-06-30 10:24:40] ℹ️  配置信息:
  Docker仓库: ljh740/relay-monitor
  标签: v1.0.0
  推送latest: true
  强制构建: false

[2025-06-30 10:24:40] ✅ 依赖检查通过
[2025-06-30 10:24:40] ℹ️  Git提交: d4fbb2f
[2025-06-30 10:24:40] ℹ️  构建时间: 2025-06-30T02:24:40Z

[2025-06-30 10:25:14] ✅ 镜像构建成功: ljh740/relay-monitor:v1.0.0
[2025-06-30 10:25:14] ✅ 镜像推送成功: ljh740/relay-monitor:v1.0.0
[2025-06-30 10:25:14] ✅ 镜像推送成功: ljh740/relay-monitor:latest

镜像信息:
REPOSITORY             TAG       IMAGE ID       CREATED AT                      SIZE
ljh740/relay-monitor   v1.0.0    4e0eed8ddc95   2025-06-27 16:54:41 +0800 CST   77.8MB

Docker Hub链接: https://hub.docker.com/r/ljh740/relay-monitor/tags

拉取命令:
  docker pull ljh740/relay-monitor:v1.0.0
  docker pull ljh740/relay-monitor:latest

✅ 🎉 所有操作完成！
```

## 🐛 故障排除

### 常见问题

1. **Docker登录失败**
   ```bash
   # 检查登录状态
   docker info | grep Username
   
   # 重新登录
   docker logout
   docker login
   ```

2. **构建失败**
   ```bash
   # 清理Docker缓存
   docker system prune -a
   
   # 强制重新构建
   ./scripts/docker-build-push.sh -f v1.0.0
   ```

3. **推送超时**
   ```bash
   # 检查网络连接
   docker pull hello-world
   
   # 重试推送
   ./scripts/docker-build-push.sh -p v1.0.0
   ```

### 调试模式

启用详细日志：

```bash
# 设置调试模式
export DEBUG=1
./scripts/docker-build-push.sh v1.0.0

# 或者使用Docker调试
docker --debug build -f Dockerfile.optimized -t test .
```

## 📚 相关文档

- [Docker部署指南](../docs/docker-deployment.md)
- [GitHub Actions工作流](.github/workflows/docker-build-push.yml)
- [Docker Hub仓库](https://hub.docker.com/r/ljh740/relay-monitor)

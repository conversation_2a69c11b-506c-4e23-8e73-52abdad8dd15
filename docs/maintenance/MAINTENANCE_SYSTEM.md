# 🧹 Relay Monitor 维护系统

## 📋 概述

Relay Monitor 现在包含了完整的自动化维护系统，用于管理日志文件和数据库的清理、备份和优化。这个系统确保应用程序能够长期稳定运行，避免磁盘空间耗尽和性能下降。

## 🚨 **解决的问题**

### ❌ **之前缺少的功能**
- **日志清理**：只有轮转，没有自动清理旧文件
- **数据库清理**：有清理方法但没有定期调用
- **维护监控**：缺少维护状态和统计信息

### ✅ **现在提供的解决方案**
- **自动化维护**：定期执行清理任务
- **智能调度**：根据配置自动判断是否需要执行
- **空间管理**：防止磁盘空间耗尽
- **操作可见性**：提供详细的维护统计和状态

## 🔧 功能特性

### 1. **数据库维护**
- ✅ **自动清理**：删除超过保留期的历史数据（15天）
- ✅ **空间统计**：跟踪清理释放的空间
- ✅ **性能优化**：保持数据库精简，提升查询速度

### 2. **日志维护**
- ✅ **文件清理**：删除超过保留期的日志文件
- ✅ **轮转管理**：配合Python logging的轮转功能
- ✅ **空间回收**：释放旧日志文件占用的磁盘空间
- ✅ **灵活配置**：可配置保留天数和清理间隔

### 3. **自动调度**
- ✅ **智能执行**：只在需要时执行维护任务
- ✅ **可配置间隔**：支持小时级别的调度配置
- ✅ **集成监控**：与现有监控系统无缝集成
- ✅ **错误处理**：完善的错误处理和日志记录

## ⚙️ 配置选项

### 数据库配置 `[database]`
```toml
[database]
path = "data/relay_monitor.db"
cleanup_enabled = true          # 启用自动清理
cleanup_interval_hours = 6      # 清理间隔（小时）
```

### 日志配置 `[logging]`
```toml
[logging]
level = "INFO"
file = "logs/relay_monitor.log"
max_file_size_mb = 10
backup_count = 5
cleanup_enabled = true          # 启用日志清理
cleanup_interval_hours = 24     # 清理检查间隔
max_log_age_days = 7            # 日志保留天数
```

### 监控配置 `[monitoring]`
```toml
[monitoring]
max_history_days = 15           # 历史数据保留天数
```

## 💻 CLI 命令

### 查看维护状态
```bash
python main.py maintenance status
```
显示：
- 各项维护功能的启用状态
- 下次执行时间
- 配置参数

### 手动执行清理
```bash
python main.py maintenance cleanup
```
执行：
- 数据库历史数据清理
- 日志文件清理
- 显示释放的空间



### 查看维护统计
```bash
python main.py maintenance stats
```
显示：
- 执行次数统计
- 清理的数据量
- 最后执行时间

## 📊 自动调度

### 默认调度策略
| 任务 | 默认间隔 | 触发条件 |
|------|----------|----------|
| **数据库清理** | 6小时 | 有超过15天的数据 |
| **日志清理** | 24小时 | 有超过7天的日志 |

### 智能执行
- **按需执行**：只在实际需要时运行
- **避免重复**：跟踪上次执行时间
- **错误恢复**：失败后会在下次间隔重试
- **资源友好**：在监控间隙执行，不影响主要功能

## 🔍 监控和日志

### 维护日志示例
```
2025-06-26 20:33:51 - INFO - Starting database cleanup...
2025-06-26 20:33:51 - INFO - Cleaned up old data: 0 price records, 0 alert records, 0 stats records
2025-06-26 20:33:51 - INFO - Database cleanup completed: saved 0.00MB, cleaned data older than 15 days
2025-06-26 20:33:51 - INFO - Log cleanup completed: removed 0 files, freed 0.00MB
```

### 统计信息跟踪
- 执行次数计数
- 清理的数据量统计
- 最后执行时间记录

## 🐳 Docker 集成

### 容器内维护
维护系统在Docker容器中完全兼容：
- ✅ 路径处理适配容器环境
- ✅ 权限管理正确配置
- ✅ 数据持久化支持
- ✅ 日志输出到容器日志

### 数据卷建议
```yaml
services:
  relay-monitor:
    volumes:
      - ./data:/app/data          # 数据库文件
      - ./logs:/app/logs          # 日志文件
      - ./config:/app/config      # 配置文件
```

## 🚀 生产环境建议

### 推荐配置
```toml
# 生产环境维护配置
[database]
cleanup_enabled = true
cleanup_interval_hours = 4      # 更频繁的清理

[logging]
cleanup_enabled = true
cleanup_interval_hours = 12
max_log_age_days = 14           # 保留更长时间

[monitoring]
max_history_days = 7            # 较短的数据保留期
```

### 监控建议
1. **定期检查**：使用 `maintenance stats` 监控维护状态
2. **磁盘监控**：监控数据目录的磁盘使用情况
3. **日志审查**：检查维护操作的日志记录
4. **性能监控**：观察清理后的查询性能改善

## 🎯 效益总结

### 🔧 **运维效益**
- **自动化管理**：减少手动维护工作
- **空间优化**：防止磁盘空间耗尽
- **性能保持**：保持数据库查询性能
- **数据精简**：15天保留期保持系统轻量

### 📈 **系统效益**
- **长期稳定**：支持长期无人值守运行
- **资源效率**：优化磁盘和内存使用
- **故障预防**：主动预防存储相关问题
- **操作透明**：提供完整的维护可见性

### 💰 **成本效益**
- **存储成本**：15天保留期大幅减少存储需求
- **运维成本**：降低手动维护需求
- **故障成本**：预防因存储问题导致的故障
- **扩展成本**：支持系统长期扩展

## ✅ 验证测试

所有维护功能已经过测试验证：
- ✅ 数据库清理功能正常（15天保留期）
- ✅ 日志清理机制工作
- ✅ CLI命令响应正确
- ✅ 配置参数生效
- ✅ 错误处理完善
- ✅ 简化配置易于管理

**🎉 现在您的 Relay Monitor 具备了精简高效的自动化维护能力，15天数据保留期确保系统轻量运行，无需担心存储管理问题！**

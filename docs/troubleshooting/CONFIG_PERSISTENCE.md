# 配置持久化故障排除

## 问题描述

用户报告：使用 `docker compose down` 和 `docker compose up` 后，交易对配置丢失，但管理员密码不会丢失。

## 根本原因

### 数据存储架构差异

1. **管理员密码**：存储在数据库中（`runtime_config` 表）
   - 位置：`/app/data/relay_monitor.db`
   - 持久化：通过 `relay_monitor_data` 数据卷

2. **交易对配置**：存储在TOML配置文件中
   - 位置：`/app/config/config.toml`
   - 问题：容器重启时恢复为镜像默认值

### 为什么会出现这种差异

- **历史原因**：系统最初设计为文件配置，后来添加了运行时数据库配置
- **迁移不完整**：管理员密码迁移到数据库，但交易对配置仍在文件中
- **Docker卷配置**：只挂载了数据目录，没有挂载配置目录

## 解决方案

### 方案1：配置文件持久化（已实施）

**修改内容：**

1. **docker-compose.deploy.yml**：
   ```yaml
   volumes:
     - relay_monitor_config:/app/config  # 新增配置卷挂载
   ```

2. **创建设置脚本**：`scripts/setup-deploy.sh`
   - 自动创建必要的Docker卷
   - 初始化配置文件到配置卷

**使用方法：**

```bash
# 1. 运行设置脚本
./scripts/setup-deploy.sh

# 2. 启动服务
docker-compose -f docker-compose.deploy.yml up -d
```

### 方案2：完全迁移到数据库（未来改进）

将交易对配置也迁移到数据库存储：

1. 修改 `_apply_runtime_overrides()` 方法
2. 添加交易对的数据库操作
3. 更新Web界面直接操作数据库

## 验证修复

### 测试步骤

1. **配置交易对**：
   ```bash
   # 访问管理界面添加交易对
   curl -X POST http://localhost:5001/api/admin/pairs \
     -H "Content-Type: application/json" \
     -d '{"name":"test","description":"测试","origin_chain":"arbitrum",...}'
   ```

2. **重启容器**：
   ```bash
   docker-compose -f docker-compose.deploy.yml down
   docker-compose -f docker-compose.deploy.yml up -d
   ```

3. **验证配置保持**：
   ```bash
   # 检查交易对是否仍然存在
   curl http://localhost:5001/api/admin/pairs
   ```

### 预期结果

- ✅ 管理员密码保持不变
- ✅ 交易对配置保持不变
- ✅ 监控服务正常启动
- ✅ 历史数据保持不变

## 预防措施

### 部署最佳实践

1. **始终使用设置脚本**：
   ```bash
   ./scripts/setup-deploy.sh
   ```

2. **检查数据卷状态**：
   ```bash
   docker volume ls | grep relay_monitor
   ```

3. **备份重要配置**：
   ```bash
   # 备份配置卷
   docker run --rm -v relay_monitor_config:/config -v $(pwd):/backup alpine \
     tar czf /backup/config-backup.tar.gz -C /config .
   ```

### 监控配置状态

添加健康检查来验证配置完整性：

```bash
# 检查配置文件是否存在
docker exec relay-monitor-deploy test -f /app/config/config.toml && echo "配置文件存在" || echo "配置文件缺失"

# 检查交易对数量
docker exec relay-monitor-deploy python -c "
import toml
config = toml.load('/app/config/config.toml')
print(f'交易对数量: {len(config.get(\"monitor_pairs\", []))}')
"
```

## 相关文档

- [Docker部署指南](../deployment/DOCKER_DEPLOYMENT.md)
- [配置管理文档](../CONFIGURATION.md)
- [快速开始指南](../QUICK_START.md)

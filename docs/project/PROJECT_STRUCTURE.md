# Relay Monitor - Project Structure

## 📁 Directory Structure

```
relayMonitor/
├── src/                          # Source code
│   └── relay_monitor/           # Main package
│       ├── __init__.py         # Package initialization
│       ├── api/                # API client module
│       │   ├── __init__.py
│       │   └── client.py       # Relay API client
│       ├── config/             # Configuration management
│       │   ├── __init__.py
│       │   ├── manager.py      # Config manager
│       │   └── models.py       # Config data models
│       ├── storage/            # Data storage layer
│       │   ├── __init__.py
│       │   ├── database.py     # SQLite database
│       │   └── models.py       # Data models
│       ├── monitor/            # Core monitoring engine
│       │   ├── __init__.py
│       │   ├── engine.py       # Main monitoring engine
│       │   ├── scheduler.py    # Task scheduler
│       │   └── analyzer.py     # Price analysis
│       ├── alerts/             # Alert system
│       │   ├── __init__.py
│       │   ├── system.py       # Alert manager
│       │   ├── channels.py     # Notification channels
│       │   └── templates.py    # Message templates
│       ├── web/                # Web interface
│       │   ├── __init__.py
│       │   ├── app.py          # Flask application
│       │   ├── routes.py       # Web routes
│       │   └── templates/      # HTML templates
│       │       ├── base.html
│       │       ├── dashboard.html
│       │       ├── status.html
│       │       ├── alerts.html
│       │       ├── pair_detail.html
│       │       └── error.html
│       ├── cli/                # Command line interface
│       │   ├── __init__.py
│       │   └── commands.py     # CLI commands
│       └── utils/              # Utility functions
│           ├── __init__.py
│           ├── logging.py      # Logging setup
│           └── formatting.py   # Output formatting
├── tests/                      # Test suite
│   ├── __init__.py
│   ├── test_api_client.py      # API client tests
│   ├── test_storage.py         # Storage tests
│   ├── test_monitor.py         # Monitor tests
│   ├── test_alerts.py          # Alert tests
│   ├── test_web.py             # Web interface tests
│   └── test_complete_system.py # Integration tests
├── config/                     # Configuration files
│   └── config.toml            # Main configuration
├── data/                       # Data directory
│   └── relay_monitor.db       # SQLite database
├── logs/                       # Log files
├── docs/                       # Documentation
├── main.py                     # Main entry point
├── run_tests.py               # Test runner
├── pytest.ini                # Pytest configuration
├── requirements.txt           # Python dependencies
├── pyproject.toml            # Project metadata
└── README.md                 # Project documentation
```

## 🎯 Design Principles

### 1. **Separation of Concerns**
- Each module has a single responsibility
- Clear boundaries between components
- Minimal coupling between modules

### 2. **Standard Python Structure**
- Follows PEP 8 and Python packaging standards
- Tests in separate `tests/` directory
- Source code in `src/` directory

### 3. **Modular Architecture**
- **API Layer**: Handles external API communication
- **Storage Layer**: Manages data persistence
- **Monitor Layer**: Core business logic
- **Alert Layer**: Notification system
- **Web Layer**: User interface
- **CLI Layer**: Command line interface

### 4. **Configuration Management**
- Centralized configuration in TOML format
- Environment-specific settings
- Validation and type checking

## 🧪 Testing Strategy

### Test Organization
- **Unit Tests**: Test individual components
- **Integration Tests**: Test component interactions
- **System Tests**: End-to-end functionality

### Test Files
- `test_api_client.py`: API client functionality
- `test_storage.py`: Database operations
- `test_monitor.py`: Monitoring engine
- `test_alerts.py`: Alert system
- `test_web.py`: Web interface
- `test_complete_system.py`: Full system integration

### Running Tests
```bash
# Run all tests
python run_tests.py

# Run specific test
python tests/test_api_client.py

# Run with pytest (if installed)
pytest tests/
```

## 📦 Package Structure

### Main Package (`relay_monitor`)
The main package contains all core functionality organized into logical modules.

### Entry Points
- **CLI**: `python main.py [command]`
- **Web**: `python main.py web`
- **Direct Import**: `from relay_monitor import PriceMonitor`

### Dependencies
- **Core**: requests, pydantic, toml
- **Web**: flask, colorama, tabulate
- **Storage**: sqlite3 (built-in)
- **Scheduling**: schedule

## 🔧 Development Workflow

### 1. **Setup Development Environment**
```bash
pip install -r requirements.txt
```

### 2. **Run Tests**
```bash
python run_tests.py
```

### 3. **Start Development Server**
```bash
python main.py web --with-monitor
```

### 4. **Check System Status**
```bash
python main.py status
```

## 📝 Best Practices

### Code Organization
- ✅ One class per file (when appropriate)
- ✅ Clear module boundaries
- ✅ Consistent naming conventions
- ✅ Comprehensive docstrings

### Testing
- ✅ Tests in separate directory
- ✅ Test file naming: `test_*.py`
- ✅ Descriptive test function names
- ✅ Setup and teardown for resources

### Configuration
- ✅ External configuration files
- ✅ Environment variable support
- ✅ Configuration validation
- ✅ Sensible defaults

This structure follows Python best practices and makes the project maintainable, testable, and extensible.

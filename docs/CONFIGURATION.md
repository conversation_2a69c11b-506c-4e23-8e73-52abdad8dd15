# Relay Monitor 配置指南

## 📋 配置文件概述

Relay Monitor 使用 TOML 格式的配置文件，位于 `config/config.toml`。这个文件控制系统的所有行为，包括监控参数、警报设置、数据库配置等。

## 🔧 配置部分详解

### 1. API 配置 `[api]`

```toml
[api]
base_url = "https://api.relay.link"  # Relay API 基础URL
timeout = 30                         # 请求超时时间（秒）
retry_attempts = 3                   # 失败重试次数
retry_delay = 1.0                    # 重试延迟时间（秒）
cache_ttl = 1800                     # 缓存生存时间（秒，默认30分钟）
```

**说明**：
- `base_url`: Relay API的官方地址，通常不需要修改
- `timeout`: API请求的超时时间，网络较慢时可以增加
- `retry_attempts`: 请求失败时的重试次数
- `retry_delay`: 重试之间的延迟时间
- `cache_ttl`: 链和代币信息的缓存时间，默认30分钟，减少API调用和日志输出

### 2. 监控配置 `[monitoring]`

```toml
[monitoring]
interval_seconds = 30                    # 价格检查间隔（秒）
price_change_threshold_percent = 5.0     # 价格变化警报阈值（%）
enabled = true                           # 是否启用监控
max_history_days = 15                    # 历史数据保留天数
```

**说明**：
- `interval_seconds`: 多久检查一次价格，建议30-300秒
- `price_change_threshold_percent`: 价格变化超过此百分比时触发警报
- `enabled`: 是否启用整个监控系统
- `max_history_days`: 价格历史数据保留天数

### 3. 数据库配置 `[database]`

```toml
[database]
path = "data/relay_monitor.db"  # SQLite数据库文件路径
cleanup_enabled = true          # 是否启用数据库清理
cleanup_interval_hours = 6      # 清理间隔（小时）
```

**说明**：
- `path`: 数据库文件的存储位置，支持相对路径和绝对路径
- `cleanup_enabled`: 是否启用自动数据清理
- `cleanup_interval_hours`: 数据清理的间隔时间

### 4. 日志配置 `[logging]`

```toml
[logging]
level = "INFO"                      # 日志级别
file = "logs/relay_monitor.log"     # 日志文件路径
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
max_file_size_mb = 10               # 日志文件最大大小（MB）
backup_count = 5                    # 保留的日志文件数量
cleanup_enabled = true              # 是否启用日志清理
cleanup_interval_hours = 24         # 日志清理间隔（小时）
max_log_age_days = 7                # 日志文件最大保留天数
```

**日志级别**：
- `DEBUG`: 详细调试信息
- `INFO`: 一般信息（推荐）
- `WARNING`: 警告信息
- `ERROR`: 错误信息

**日志清理**：
- `cleanup_enabled`: 是否启用自动日志清理
- `cleanup_interval_hours`: 日志清理的检查间隔
- `max_log_age_days`: 超过此天数的日志文件将被删除

### 5. 警报配置 `[alerts]`

```toml
[alerts]
enabled = true                      # 是否启用警报系统
rate_limit_minutes = 5.0            # 同一对的警报间隔（分钟）

[alerts.console]
enabled = true                      # 控制台警报

[alerts.email]
enabled = false                     # 邮件警报
smtp_server = "smtp.gmail.com"
smtp_port = 587
username = "<EMAIL>"
password = "your-app-password"
from_email = "<EMAIL>"
to_emails = ["<EMAIL>"]

[alerts.webhook]
enabled = false                     # Webhook警报
url = "https://hooks.slack.com/services/YOUR/WEBHOOK/URL"

[alerts.webhook.headers]
"Content-Type" = "application/json"
```

### 6. 缓存配置 `[cache]`

```toml
[cache]
chains_ttl = 3600                   # 链信息缓存时间（秒）
tokens_ttl = 3600                   # 代币信息缓存时间（秒）
refresh_on_startup = true           # 启动时刷新缓存
```

**说明**：
- `chains_ttl`: 区块链信息的缓存生存时间
- `tokens_ttl`: 代币信息的缓存生存时间
- `refresh_on_startup`: 系统启动时是否刷新所有缓存

### 7. 管理员配置 `[admin]`

```toml
[admin]
enabled = true                      # 是否启用管理后台
password = "admin123!!."               # 管理员密码
session_timeout_minutes = 6000      # 会话超时时间（分钟）
max_login_attempts = 5              # 最大登录尝试次数
lockout_duration_minutes = 15       # 锁定持续时间（分钟）
```

**说明**：
- `enabled`: 是否启用Web管理后台
- `password`: 管理员登录密码（建议修改默认密码）
- `session_timeout_minutes`: 管理员会话的超时时间
- `max_login_attempts`: 连续登录失败的最大次数
- `lockout_duration_minutes`: 超过最大尝试次数后的锁定时间

### 8. Web界面配置 `[web]`

```toml
[web]
host = "127.0.0.1"                  # Web服务器绑定地址
port = 5000                         # Web服务器端口
debug = false                       # 调试模式
```

### 9. 监控对配置 `[[monitor_pairs]]`

```toml
[[monitor_pairs]]
name = "ARB_ETH_to_POLYGON_POL"     # 监控对名称（唯一标识）
description = "Arbitrum ETH to Polygon POL"  # 描述
origin_chain = "arbitrum"           # 源链名称
destination_chain = "polygon"       # 目标链名称
origin_token = "ETH"                # 源代币符号
destination_token = "POL"           # 目标代币符号
amount = "1.0"                      # 监控金额
enabled = true                      # 是否启用此监控对
alert_threshold_percent = 5.0       # 此对的特定警报阈值
```

## 🌐 支持的链和代币

### 主要区块链网络

系统支持76个区块链网络，以下是常用的主要网络：

| 链名称 | 配置中使用 | 说明 |
|--------|------------|------|
| Ethereum | `ethereum` | 以太坊主网 |
| Arbitrum | `arbitrum` | Arbitrum One |
| Polygon | `polygon` | Polygon PoS |
| Optimism | `optimism` | Optimism |
| Base | `base` | Base |
| BSC | `bsc` | Binance Smart Chain |
| Avalanche | `avalanche` | Avalanche C-Chain |
| Abstract | `abstract` | Abstract 测试网 |
| Linea | `linea` | Linea |
| Scroll | `scroll` | Scroll |
| Blast | `blast` | Blast |
| Mantle | `mantle` | Mantle |
| Zora | `zora` | Zora Network |

> **注意**：系统会自动从Relay API加载所有支持的链，无需手动配置。使用小写的链名称即可。

### 常见代币符号

| 代币 | 符号 | 说明 |
|------|------|------|
| Ether | `ETH` | 以太坊原生代币 |
| Polygon | `POL` | Polygon原生代币 |
| USD Coin | `USDC` | 美元稳定币 |
| Tether | `USDT` | 泰达币 |
| Wrapped Bitcoin | `WBTC` | 包装比特币 |

## 📝 配置示例

### 基础监控配置

```toml
# 监控 Arbitrum ETH 到 Polygon POL
[[monitor_pairs]]
name = "ARB_ETH_to_POL"
description = "Arbitrum ETH to Polygon POL"
origin_chain = "arbitrum"
destination_chain = "polygon"
origin_token = "ETH"
destination_token = "POL"
amount = "1.0"
enabled = true
alert_threshold_percent = 5.0
```

### 多对监控配置

```toml
# 监控多个代币对
[[monitor_pairs]]
name = "ETH_USDC_to_POLYGON"
description = "Ethereum USDC to Polygon USDC"
origin_chain = "ethereum"
destination_chain = "polygon"
origin_token = "USDC"
destination_token = "USDC"
amount = "1000.0"
enabled = true
alert_threshold_percent = 2.0

[[monitor_pairs]]
name = "ARBITRUM_USDT_to_BSC"
description = "Arbitrum USDT to BSC USDT"
origin_chain = "arbitrum"
destination_chain = "bsc"
origin_token = "USDT"
destination_token = "USDT"
amount = "500.0"
enabled = true
alert_threshold_percent = 3.0
```

## ⚙️ 高级配置

### 邮件警报设置

1. **Gmail 配置**：
   ```toml
   [alerts.email]
   enabled = true
   smtp_server = "smtp.gmail.com"
   smtp_port = 587
   username = "<EMAIL>"
   password = "your-app-password"  # 使用应用专用密码
   ```

2. **Outlook 配置**：
   ```toml
   [alerts.email]
   enabled = true
   smtp_server = "smtp-mail.outlook.com"
   smtp_port = 587
   username = "<EMAIL>"
   password = "your-password"
   ```

### Slack Webhook 配置

```toml
[alerts.webhook]
enabled = true
url = "*****************************************************************************"
headers = { "Content-Type" = "application/json" }
```

## 🎯 智能化管理

### Web管理后台

系统提供了完整的Web管理后台，支持：

1. **智能添加交易对**：
   - 自动加载76个区块链网络
   - 动态加载每个链支持的代币
   - 自动生成标准格式的交易对名称
   - 零配置错误，只能选择系统支持的组合

2. **可视化管理**：
   - 实时监控状态显示
   - 价格历史图表
   - 警报记录查看
   - 系统性能监控

3. **安全认证**：
   - 密码保护的管理后台
   - 会话管理和超时控制
   - 登录失败锁定机制

### 访问管理后台

```bash
# 启动Web界面
python main.py web --with-monitor

# 访问地址
http://127.0.0.1:5000/admin/login
```

## 🔄 配置热重载

系统支持配置热重载，修改配置文件后：

1. **自动重载**：系统会检测配置文件变化并自动重载
2. **手动重载**：使用 `python main.py config validate` 验证配置
3. **重启服务**：重启监控服务以应用新配置
4. **Web界面重载**：管理后台提供"重载配置"按钮

## ⚠️ 注意事项

1. **链名称**：使用小写的链名称，系统会自动解析
2. **代币符号**：使用标准的代币符号（如 ETH, USDC）
3. **金额格式**：使用字符串格式的数字（如 "1.0"）
4. **警报频率**：避免设置过短的检查间隔，以免触发API限制
5. **敏感信息**：邮件密码等敏感信息建议使用环境变量
6. **管理员密码**：务必修改默认的管理员密码 `admin123!!.`
7. **会话安全**：生产环境中建议设置较短的会话超时时间

## 🛠️ 故障排除

### 常见问题

1. **链不支持**：检查链名称是否正确，使用 `python main.py status` 查看支持的链
2. **代币不存在**：确认代币在指定链上存在
3. **API错误**：检查网络连接和API配置
4. **权限问题**：确保数据库和日志目录有写入权限

### 调试模式

启用调试模式获取更多信息：

```toml
[web]
debug = true  # 启用Web调试模式，自动将日志级别设为DEBUG

[logging]
level = "INFO"  # 当web.debug=true时，此设置会被自动覆盖为DEBUG
```

**智能日志级别**：
- 当 `web.debug = true` 时，系统自动将日志级别设为 `DEBUG`，无需手动修改 `logging.level`
- 当 `web.debug = false` 时，使用 `logging.level` 配置的级别
- 这样您只需要修改一个地方就能控制调试模式

这个配置指南涵盖了 Relay Monitor 的所有配置选项，帮助您根据需求定制监控系统。

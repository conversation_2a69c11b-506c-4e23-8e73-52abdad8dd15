# 📚 Relay Monitor 文档中心

欢迎来到 Relay Monitor 的文档中心！这里包含了项目的所有技术文档和指南。

## 📋 文档目录

### 🚀 快速开始
- [快速开始指南](QUICK_START.md) - 最快速的部署和使用方法
- [配置说明](CONFIGURATION.md) - 详细的配置参数说明
- [端口配置](PORT_CONFIGURATION.md) - 端口使用策略和配置方法
- [使用示例](EXAMPLES.md) - 常见使用场景和示例

### 🐳 部署相关
- [部署总览](deployment/DEPLOYMENT_SUMMARY.md) - 部署方案总结
- [Docker部署](deployment/DOCKER_DEPLOYMENT.md) - Docker容器化部署
- [快速部署](deployment/quick-deploy.md) - 一键部署脚本
- [Docker Hub说明](deployment/docker-hub-description.md) - 镜像仓库信息

### 👨‍💻 开发相关
- [CI故障排除](development/ci-troubleshooting.md) - CI/CD问题诊断和解决
- [Git工作流](development/git-workflow.md) - 代码提交和分支管理
- [GitHub设置](development/github-setup.md) - GitHub Actions配置

### 🔧 运维相关
- [维护系统](maintenance/MAINTENANCE_SYSTEM.md) - 系统维护和监控
- [Docker优化](maintenance/DOCKER_OPTIMIZATION_SUMMARY.md) - 容器优化策略

### 📊 项目信息
- [项目结构](project/PROJECT_STRUCTURE.md) - 代码结构和架构说明
- [项目完成报告](project/PROJECT_COMPLETION_REPORT.md) - 项目开发总结

## 🔍 快速导航

### 我想要...

#### 🚀 **快速部署**
1. [快速开始指南](QUICK_START.md) - 5分钟部署
2. [快速部署脚本](deployment/quick-deploy.md) - 一键部署

#### 🐳 **Docker部署**
1. [Docker部署指南](deployment/DOCKER_DEPLOYMENT.md) - 完整Docker部署
2. [部署总览](deployment/DEPLOYMENT_SUMMARY.md) - 选择合适的部署方案

#### ⚙️ **配置系统**
1. [配置说明](CONFIGURATION.md) - 详细配置参数
2. [端口配置](PORT_CONFIGURATION.md) - 端口使用和映射
3. [使用示例](EXAMPLES.md) - 配置示例

#### 🛠️ **开发贡献**
1. [项目结构](project/PROJECT_STRUCTURE.md) - 了解代码结构
2. [Git工作流](development/git-workflow.md) - 代码提交规范
3. [CI故障排除](development/ci-troubleshooting.md) - 解决CI问题

#### 🔧 **运维维护**
1. [维护系统](maintenance/MAINTENANCE_SYSTEM.md) - 系统维护指南
2. [Docker优化](maintenance/DOCKER_OPTIMIZATION_SUMMARY.md) - 性能优化

## 📖 文档约定

### 文档分类
- **deployment/** - 部署相关文档
- **development/** - 开发相关文档  
- **maintenance/** - 运维相关文档
- **project/** - 项目信息文档

### 文档命名
- 使用英文命名，单词间用连字符分隔
- 重要文档使用大写（如 README.md）
- 分类文档放在对应子目录

### 更新说明
文档会随着项目发展持续更新，如有疑问请查看：
- [项目主页](../README.md)
- [GitHub Issues](https://github.com/ljh740/relayMonitor/issues)

## 🤝 贡献文档

如果您发现文档有误或需要补充，欢迎：
1. 提交 Issue 报告问题
2. 提交 Pull Request 改进文档
3. 联系维护者讨论

---

**快速链接**: [主页](../README.md) | [快速开始](QUICK_START.md) | [配置说明](CONFIGURATION.md) | [部署指南](deployment/DOCKER_DEPLOYMENT.md)

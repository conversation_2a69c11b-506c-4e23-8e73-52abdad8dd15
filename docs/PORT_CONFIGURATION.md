# 🔌 端口配置说明

本文档说明 Relay Monitor 的端口配置策略和使用方法。

## 📋 端口配置总览

### 🎯 **标准端口配置**

| 环境 | 外部端口 | 内部端口 | 说明 |
|------|----------|----------|------|
| **生产环境** | 5000 | 5000 | 标准Flask端口，生产部署推荐 |
| **开发环境** | 5001 | 5000 | 避免与本地服务冲突 |
| **自定义端口** | 用户指定 | 5000 | 通过环境变量配置 |

### 🔧 **配置文件中的端口**

```toml
# config/config.toml
[web]
host = "127.0.0.1"
port = 5000          # 应用内部监听端口（固定）
debug = false
```

## 🐳 Docker 端口映射

### **生产环境**
```yaml
# docker-compose.yml 或 docker-compose.prod.yml
ports:
  - "${HOST_PORT:-5000}:5000"
```

### **开发环境**
```yaml
# docker-compose.override.yml
ports:
  - "${DEV_HOST_PORT:-5001}:5000"
```

## 🚀 使用方法

### **1. 默认端口启动**

```bash
# 生产环境 - 端口5000
docker compose -f docker-compose.prod.yml up -d
# 访问: http://localhost:5000

# 开发环境 - 端口5001
docker compose up -d
# 访问: http://localhost:5001
```

### **2. 自定义端口启动**

```bash
# 使用8080端口
HOST_PORT=8080 docker compose -f docker-compose.prod.yml up -d
# 访问: http://localhost:8080

# 开发环境使用8081端口
DEV_HOST_PORT=8081 docker compose up -d
# 访问: http://localhost:8081
```

### **3. 脚本启动**

```bash
# 使用部署脚本（默认5000端口）
./scripts/deploy-from-hub.sh

# 指定端口
./scripts/deploy-from-hub.sh -p 8080

# 开发环境启动（默认5001端口）
./scripts/dev-start.sh
```

## 🔍 端口检查

### **检查端口占用**
```bash
# 检查5000端口
lsof -i :5000

# 检查5001端口
lsof -i :5001

# 检查自定义端口
lsof -i :8080
```

### **检查服务状态**
```bash
# 查看Docker容器端口映射
docker compose ps

# 测试服务可用性
curl http://localhost:5000/health
curl http://localhost:5001/health
```

## ⚠️ 常见问题

### **Q: 为什么开发环境使用5001端口？**
A: 避免与本地可能运行的其他Flask应用（通常使用5000端口）冲突。

### **Q: 可以修改应用内部端口吗？**
A: 不建议修改。应用内部固定使用5000端口，通过Docker端口映射来改变外部访问端口。

### **Q: 端口冲突怎么办？**
A: 使用环境变量指定不同的外部端口：
```bash
HOST_PORT=8080 docker compose up -d
```

### **Q: 如何在生产环境使用80端口？**
A: 
```bash
# 需要root权限或使用反向代理
sudo HOST_PORT=80 docker compose -f docker-compose.prod.yml up -d

# 或使用nginx反向代理（推荐）
# nginx配置proxy_pass到localhost:5000
```

## 🌐 反向代理配置

### **Nginx 配置示例**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### **Traefik 配置示例**
```yaml
# docker-compose.yml
services:
  relay-monitor:
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.relay-monitor.rule=Host(`your-domain.com`)"
      - "traefik.http.services.relay-monitor.loadbalancer.server.port=5000"
```

## 📊 端口使用建议

### **开发环境**
- 使用 **5001** 端口（默认）
- 避免与本地服务冲突
- 便于调试和开发

### **测试环境**
- 使用 **5000** 端口
- 模拟生产环境
- 便于集成测试

### **生产环境**
- 使用 **5000** 端口（内网）
- 通过反向代理暴露 **80/443** 端口
- 配置SSL证书和域名

### **多实例部署**
```bash
# 实例1
HOST_PORT=5000 docker compose -f docker-compose.prod.yml up -d

# 实例2
HOST_PORT=5002 docker compose -f docker-compose.prod.yml up -d

# 实例3
HOST_PORT=5003 docker compose -f docker-compose.prod.yml up -d
```

## 🔗 相关文档

- [快速开始](QUICK_START.md) - 基础部署指南
- [配置说明](CONFIGURATION.md) - 详细配置参数
- [Docker部署](deployment/DOCKER_DEPLOYMENT.md) - 容器化部署
- [开发指南](development/git-workflow.md) - 开发环境设置

---

**记住**: 应用内部始终使用5000端口，通过Docker端口映射来控制外部访问端口！

# Docker 部署指南

本文档介绍如何使用 Docker 部署 Relay Monitor 系统。

## 📋 前置要求

- Docker Engine 20.10+
- Docker Compose 2.0+
- 至少 512MB 可用内存
- 至少 1GB 可用磁盘空间

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd relay-monitor
```

### 2. 配置环境

```bash
# 复制环境变量示例文件
cp .env.example .env

# 编辑环境变量
nano .env
```

### 3. 配置应用

```bash
# 复制配置示例文件
cp config/config.example.toml config/config.toml

# 编辑配置文件
nano config/config.toml
```

### 4. 启动服务

```bash
# 使用部署脚本（推荐）
./scripts/docker-deploy.sh

# 或直接使用 docker-compose
docker-compose up -d
```

### 5. 访问应用

- **主页**: http://localhost:5000
- **管理面板**: http://localhost:5000/admin/login
- **健康检查**: http://localhost:5000/health

## 🔧 配置说明

### 环境变量

在 `.env` 文件中配置以下变量：

```bash
# 应用版本
VERSION=latest

# 网络配置
HOST_PORT=5000                    # 主机端口
DEV_HOST_PORT=5001               # 开发环境端口

# 数据路径
DATA_PATH=./docker-data/data     # 数据库文件路径
LOGS_PATH=./docker-data/logs     # 日志文件路径

# 安全配置
ADMIN_PASSWORD=your_secure_password_here

# SMTP.dev 配置
SMTP_API_KEY=your_smtp_dev_api_key_here

# Bark 推送配置
BARK_KEYS=key1,key2,key3

# 应用配置
DEBUG=false
LOG_LEVEL=INFO
```

### 应用配置

编辑 `config/config.toml` 文件：

```toml
# 监控交易对
[[monitor_pairs]]
name = "ARB_ETH_to_POLYGON_POL"
origin_chain = "arbitrum"
destination_chain = "polygon"
origin_token = "ETH"
destination_token = "POL"
amount = "1.0"
enabled = true
alert_threshold_percent = 2.0

# 管理员配置
[admin]
enabled = true
password = "admin123!!."  # 请修改为安全密码

# 警报配置
[alerts.bark]
enabled = true
keys = ["your_bark_key_here"]
```

## 🐳 Docker 命令

### 构建镜像

```bash
# 使用构建脚本（推荐）
./scripts/docker-build.sh

# 手动构建
docker build -t relay-monitor:latest .

# 多平台构建
./scripts/docker-build.sh --platform linux/amd64,linux/arm64
```

### 部署管理

```bash
# 启动服务
./scripts/docker-deploy.sh up

# 停止服务
./scripts/docker-deploy.sh down

# 重启服务
./scripts/docker-deploy.sh restart

# 查看日志
./scripts/docker-deploy.sh logs

# 查看状态
./scripts/docker-deploy.sh status

# 更新服务
./scripts/docker-deploy.sh update
```

### 开发环境

```bash
# 启动开发环境
./scripts/docker-deploy.sh -e development up

# 开发环境特性：
# - 端口 5001
# - Debug 模式
# - 源代码挂载
# - 详细日志
```

## 📊 监控和维护

### 健康检查

```bash
# 检查服务健康状态
curl http://localhost:5000/health

# 检查服务就绪状态
curl http://localhost:5000/ready
```

### 日志管理

```bash
# 查看实时日志
docker-compose logs -f relay-monitor

# 查看特定时间的日志
docker-compose logs --since="2024-01-01T00:00:00" relay-monitor

# 导出日志
docker-compose logs relay-monitor > relay-monitor.log
```

### 数据备份

```bash
# 备份数据库
docker cp relay-monitor:/app/data/relay_monitor.db ./backup/

# 备份配置
cp config/config.toml ./backup/

# 备份日志
docker cp relay-monitor:/app/logs ./backup/
```

## 🔒 安全配置

### 生产环境安全

1. **更改默认密码**
   ```toml
   [admin]
   password = "your_very_secure_password_here"
   ```

2. **使用环境变量**
   ```bash
   export ADMIN_PASSWORD="your_secure_password"
   export SMTP_API_KEY="your_api_key"
   export BARK_KEYS="key1,key2"
   ```

3. **限制网络访问**
   ```yaml
   # docker-compose.yml
   ports:
     - "127.0.0.1:5000:5000"  # 只允许本地访问
   ```

4. **使用 HTTPS**
   - 配置反向代理（Nginx/Traefik）
   - 使用 SSL 证书

### 防火墙配置

```bash
# 只允许必要端口
ufw allow 5000/tcp
ufw enable
```

## 🚀 生产部署

### 使用预构建镜像部署（推荐）

**重要：首次部署或解决配置丢失问题**

```bash
# 1. 运行设置脚本（创建必要的卷和网络）
./scripts/setup-deploy.sh

# 2. 启动服务
docker-compose -f docker-compose.deploy.yml up -d

# 自定义端口
HOST_PORT=8080 docker-compose -f docker-compose.deploy.yml up -d
```

#### 配置持久化说明

为了解决容器重启后交易对配置丢失的问题，现在使用以下数据卷：

- `relay_monitor_data`: 数据库文件（管理员密码、历史数据）
- `relay_monitor_logs`: 日志文件
- `relay_monitor_config`: **配置文件（交易对配置）** ⭐

这确保了：
- ✅ 管理员密码持久化（存储在数据库中）
- ✅ 交易对配置持久化（存储在配置卷中）
- ✅ 容器重启后所有配置保持不变

#### 故障排除

如果遇到交易对配置丢失问题：

```bash
# 1. 停止服务
docker-compose -f docker-compose.deploy.yml down

# 2. 重新运行设置脚本
./scripts/setup-deploy.sh

# 3. 重新启动服务
docker-compose -f docker-compose.deploy.yml up -d
```

### 使用 Docker Swarm

```bash
# 初始化 Swarm
docker swarm init

# 部署服务
docker stack deploy -c docker-compose.yml relay-monitor
```

### 使用 Kubernetes

```yaml
# kubernetes/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: relay-monitor
spec:
  replicas: 1
  selector:
    matchLabels:
      app: relay-monitor
  template:
    metadata:
      labels:
        app: relay-monitor
    spec:
      containers:
      - name: relay-monitor
        image: relay-monitor:latest
        ports:
        - containerPort: 5000
        env:
        - name: ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: relay-monitor-secrets
              key: admin-password
```

## 🔧 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs relay-monitor
   
   # 检查配置
   docker-compose config
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据目录权限
   ls -la docker-data/
   
   # 重新创建数据卷
   docker-compose down -v
   docker-compose up -d
   ```

3. **端口冲突**
   ```bash
   # 修改端口
   export HOST_PORT=5001
   docker-compose up -d
   ```

### 性能优化

1. **资源限制**
   ```yaml
   deploy:
     resources:
       limits:
         memory: 1G
         cpus: '1.0'
   ```

2. **日志轮转**
   ```yaml
   logging:
     driver: "json-file"
     options:
       max-size: "10m"
       max-file: "3"
   ```

## 📞 支持

如果遇到问题，请：

1. 查看日志：`docker-compose logs relay-monitor`
2. 检查健康状态：`curl http://localhost:5000/health`
3. 验证配置：`docker-compose config`
4. 提交 Issue 到项目仓库

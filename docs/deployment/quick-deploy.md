# 🚀 Relay Monitor 快速部署指南

使用Docker Hub预构建镜像，无需本地构建，一键部署！

## 📋 前置要求

- ✅ Docker
- ✅ Docker Compose
- ✅ 网络连接（用于拉取镜像）

## ⚡ 一键部署

### 方法1: 使用部署脚本（推荐）

```bash
# 1. 下载项目文件
git clone https://github.com/ljh740/relayMonitor.git
cd relayMonitor

# 2. 一键部署（默认端口5000）
./scripts/deploy-from-hub.sh

# 3. 自定义端口部署
./scripts/deploy-from-hub.sh -p 8080

# 4. 部署指定版本
./scripts/deploy-from-hub.sh -t v1.0.0 -p 8080
```

### 方法2: 直接使用Docker Compose

```bash
# 1. 下载compose文件
wget https://raw.githubusercontent.com/ljh740/relayMonitor/main/docker-compose.deploy.yml

# 2. 启动服务（默认端口5000）
docker-compose -f docker-compose.deploy.yml up -d

# 3. 自定义端口启动
HOST_PORT=8080 docker-compose -f docker-compose.deploy.yml up -d
```

### 方法3: 纯Docker命令

```bash
# 创建数据卷
docker volume create relay_monitor_data
docker volume create relay_monitor_logs

# 启动容器
docker run -d \
  --name relay-monitor \
  --restart unless-stopped \
  -p 8080:5000 \
  -e TZ=Asia/Shanghai \
  -v relay_monitor_data:/app/data \
  -v relay_monitor_logs:/app/logs \
  ljh740/relay-monitor:latest
```

## 🎯 部署后访问

部署完成后，访问以下地址：

- **主页**: http://localhost:5000 (或您设置的端口)
- **健康检查**: http://localhost:5000/health
- **管理面板**: http://localhost:5000/admin

## 🔧 常用管理命令

### 使用部署脚本管理

```bash
# 查看服务状态
./scripts/deploy-from-hub.sh --status

# 查看实时日志
./scripts/deploy-from-hub.sh --logs

# 重启服务
./scripts/deploy-from-hub.sh --restart

# 更新到最新版本
./scripts/deploy-from-hub.sh --update

# 停止服务
./scripts/deploy-from-hub.sh --stop

# 清理未使用的镜像
./scripts/deploy-from-hub.sh --cleanup
```

### 使用Docker Compose管理

```bash
# 查看服务状态
docker-compose -f docker-compose.deploy.yml ps

# 查看日志
docker-compose -f docker-compose.deploy.yml logs -f

# 重启服务
docker-compose -f docker-compose.deploy.yml restart

# 停止服务
docker-compose -f docker-compose.deploy.yml down

# 更新镜像并重启
docker-compose -f docker-compose.deploy.yml pull
docker-compose -f docker-compose.deploy.yml up -d
```

## 📊 可用镜像标签

| 标签 | 描述 | 使用场景 |
|------|------|----------|
| `latest` | 最新稳定版本 | 生产环境推荐 |
| `v1.0.0` | 具体版本号 | 版本锁定 |
| `dev` | 开发版本 | 测试新功能 |

## 🔧 配置自定义

### 环境变量配置

在 `docker-compose.deploy.yml` 中可以设置以下环境变量：

```yaml
environment:
  - TZ=Asia/Shanghai          # 时区设置
  - LOG_LEVEL=INFO           # 日志级别
  - DATABASE_PATH=/app/data/relay_monitor.db  # 数据库路径
```

### 自定义配置文件

如果需要自定义配置，可以挂载配置文件：

```bash
# 1. 创建自定义配置文件
cp config/config.example.toml my-config.toml

# 2. 编辑配置文件
nano my-config.toml

# 3. 在docker-compose.deploy.yml中取消注释配置文件挂载
# - ./my-config.toml:/app/config/config.toml:ro
```

### 端口配置

```bash
# 方法1: 环境变量
HOST_PORT=8080 docker-compose -f docker-compose.deploy.yml up -d

# 方法2: 修改compose文件
# 编辑 docker-compose.deploy.yml 中的 ports 配置
```

## 🛡️ 安全建议

### 1. 网络安全
```bash
# 只绑定本地接口（更安全）
# 修改端口映射为: "127.0.0.1:5000:5000"
```

### 2. 数据备份
```bash
# 备份数据卷
docker run --rm -v relay_monitor_data:/data -v $(pwd):/backup alpine tar czf /backup/relay_monitor_backup.tar.gz -C /data .

# 恢复数据卷
docker run --rm -v relay_monitor_data:/data -v $(pwd):/backup alpine tar xzf /backup/relay_monitor_backup.tar.gz -C /data
```

### 3. 定期更新
```bash
# 设置定时任务自动更新
echo "0 2 * * 0 cd /path/to/relayMonitor && ./scripts/deploy-from-hub.sh --update" | crontab -
```

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -i :5000
   
   # 使用其他端口
   ./scripts/deploy-from-hub.sh -p 8080
   ```

2. **镜像拉取失败**
   ```bash
   # 检查网络连接
   docker pull hello-world
   
   # 手动拉取镜像
   docker pull ljh740/relay-monitor:latest
   ```

3. **服务启动失败**
   ```bash
   # 查看详细日志
   docker-compose -f docker-compose.deploy.yml logs
   
   # 检查容器状态
   docker ps -a
   ```

4. **数据丢失**
   ```bash
   # 检查数据卷
   docker volume ls | grep relay_monitor
   
   # 检查数据卷内容
   docker run --rm -v relay_monitor_data:/data alpine ls -la /data
   ```

### 健康检查

```bash
# 检查服务健康状态
curl -f http://localhost:5000/health

# 预期返回
{
  "status": "healthy",
  "checks": {
    "database": "ok",
    "config": "ok",
    "monitor": "running",
    "alerts": "1 channels enabled"
  }
}
```

## 📈 性能优化

### 资源限制

在 `docker-compose.deploy.yml` 中已配置合理的资源限制：

```yaml
deploy:
  resources:
    limits:
      memory: 512M      # 最大内存使用
      cpus: '0.5'       # 最大CPU使用
    reservations:
      memory: 256M      # 保留内存
      cpus: '0.25'      # 保留CPU
```

### 监控建议

```bash
# 监控容器资源使用
docker stats relay-monitor-deploy

# 监控数据卷大小
docker system df -v
```

## 🔗 相关链接

- [Docker Hub镜像](https://hub.docker.com/r/ljh740/relay-monitor)
- [GitHub仓库](https://github.com/ljh740/relayMonitor)
- [完整部署文档](docker-deployment.md)
- [配置指南](../config/README.md)

**现在您可以在几分钟内部署Relay Monitor，无需任何构建过程！** 🎉

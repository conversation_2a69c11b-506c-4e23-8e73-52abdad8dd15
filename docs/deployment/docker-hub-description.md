# Relay Monitor - 跨链桥价格监控系统

[![Docker Pulls](https://img.shields.io/docker/pulls/ljh740/relay-monitor)](https://hub.docker.com/r/ljh740/relay-monitor)
[![Docker Image Size](https://img.shields.io/docker/image-size/ljh740/relay-monitor/latest)](https://hub.docker.com/r/ljh740/relay-monitor)
[![Docker Image Version](https://img.shields.io/docker/v/ljh740/relay-monitor?sort=semver)](https://hub.docker.com/r/ljh740/relay-monitor)

一个专业的跨链桥价格监控系统，支持实时监控多个区块链网络之间的代币价格差异，并提供多种警报通知方式。

## 🚀 快速开始

### 基本运行

```bash
docker run -d \
  --name relay-monitor \
  -p 8080:5000 \
  -e TZ=Asia/Shanghai \
  ljh740/relay-monitor:latest
```

### 使用Docker Compose

```yaml
version: '3.8'
services:
  relay-monitor:
    image: ljh740/relay-monitor:latest
    container_name: relay-monitor
    restart: unless-stopped
    ports:
      - "8080:5000"
    environment:
      - TZ=Asia/Shanghai
      - ADMIN_PASSWORD=your_secure_password
    volumes:
      - ./config:/app/config
      - relay_monitor_data:/app/data
      - relay_monitor_logs:/app/logs

volumes:
  relay_monitor_data:
  relay_monitor_logs:
```

## ✨ 主要功能

- 🔄 **实时价格监控**: 监控跨链桥代币价格变化
- 📊 **多链支持**: 支持以太坊、Polygon、Arbitrum等多个网络
- 🚨 **智能警报**: 价格异常时自动发送通知
- 📧 **多种通知方式**: 支持邮件、Bark推送、Webhook等
- 🎛️ **Web管理界面**: 直观的配置和监控界面
- 📈 **历史数据**: 价格历史记录和趋势分析
- 🐳 **容器化部署**: 开箱即用的Docker镜像

## 🏷️ 镜像标签

- `latest` - 最新稳定版本
- `v1.x.x` - 具体版本号
- `main` - 主分支最新构建

## 🔧 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `TZ` | 时区设置 | `Asia/Shanghai` |
| `ADMIN_PASSWORD` | 管理员密码 | - |
| `SMTP_API_KEY` | SMTP.dev API密钥 | - |
| `BARK_KEYS` | Bark推送密钥 | - |
| `LOG_LEVEL` | 日志级别 | `INFO` |

## 📁 数据卷

- `/app/config` - 配置文件目录
- `/app/data` - 数据库和数据文件
- `/app/logs` - 日志文件

## 🌐 端口

- `5000` - Web服务端口

## 🔗 相关链接

- [GitHub仓库](https://github.com/ljh740/relayMonitor)
- [使用文档](https://github.com/ljh740/relayMonitor/blob/main/README.md)
- [部署指南](https://github.com/ljh740/relayMonitor/blob/main/docs/docker-deployment.md)

## 📋 系统要求

- Docker 20.10+
- 内存: 最少128MB，推荐256MB
- CPU: 最少0.25核，推荐0.5核
- 存储: 最少100MB可用空间

## 🛠️ 构建信息

此镜像基于Alpine Linux构建，经过优化以减小体积并提高安全性。支持多架构：

- `linux/amd64`
- `linux/arm64`

## 📞 支持

如果您遇到问题或有建议，请：

1. 查看[文档](https://github.com/ljh740/relayMonitor/blob/main/README.md)
2. 提交[Issue](https://github.com/ljh740/relayMonitor/issues)
3. 参与[讨论](https://github.com/ljh740/relayMonitor/discussions)

## 📄 许可证

本项目采用MIT许可证 - 查看[LICENSE](https://github.com/ljh740/relayMonitor/blob/main/LICENSE)文件了解详情。

# 🤖 自动交易功能使用指南

## 📋 功能概述

Relay Monitor 的自动交易功能可以在价格监控触发告警时自动执行跨链交易。该功能基于 Relay 协议，支持多链间的自动化交易执行。

### ✨ 核心特性

- **🔐 安全私钥管理**：AES-256 加密存储，主密码保护
- **🎯 智能触发机制**：基于价格变化自动执行交易
- **🛡️ 风险控制**：交易限额、滑点控制、冷却时间
- **📊 完整监控**：交易历史记录、状态跟踪
- **🌐 多链支持**：支持 Relay 协议的所有区块链

## 🚀 快速开始

### 1. 启动系统

```bash
# 启动 Web 界面
python main.py web --debug

# 访问管理后台
open http://localhost:5000/admin
```

### 2. 配置交易主密码

1. 访问 **系统设置** → **交易设置**
2. 设置交易主密码（用于加密私钥）
3. 配置全局交易限制
4. 点击 **保存交易设置**

### 3. 添加钱包

1. 访问 **钱包管理**
2. 点击 **添加钱包**
3. 输入钱包名称和私钥
4. 使用交易主密码加密存储

### 4. 配置自动交易

1. 访问 **自动交易配置**
2. 为监控对启用自动交易
3. 选择执行钱包
4. 设置交易参数

## 🔧 详细配置

### 交易主密码设置

**位置**：系统设置 → 交易设置

| 参数 | 说明 | 默认值 |
|------|------|--------|
| 交易主密码 | 用于加密私钥的主密码 | 无 |
| 全局日交易限额 | 所有钱包的日交易总限额 (USD) | 10000 |
| 全局单次交易限额 | 单次交易的最大金额限制 (USD) | 1000 |
| 最大滑点 | 允许的最大滑点百分比 | 10% |
| 最小交易间隔 | 两次交易之间的最小时间间隔 (分钟) | 5 |

### 钱包管理

**位置**：钱包管理

- **添加钱包**：输入钱包名称和私钥（64位十六进制）
- **查看地址**：显示钱包的以太坊地址
- **删除钱包**：永久删除钱包（不可恢复）

**安全提示**：
- 私钥使用 AES-256-GCM 加密存储
- 主密码不会存储在系统中
- 每次操作都需要验证主密码

### 自动交易配置

**位置**：自动交易配置

| 参数 | 说明 | 默认值 |
|------|------|--------|
| 自动交易 | 是否启用自动交易 | 禁用 |
| 钱包 | 执行交易的钱包 | 无 |
| 交易金额 | 每次交易的金额 | 监控金额 |
| 最大滑点 | 该交易对的最大滑点 | 5% |
| 最小价格变化 | 触发交易的最小价格变化 | 5% |
| 冷却时间 | 交易后的冷却时间 | 60分钟 |
| 日交易限额 | 该交易对的日限额 | 无限制 |
| 单次交易限额 | 该交易对的单次限额 | 无限制 |

## 🛡️ 安全机制

### 私钥安全

1. **多层加密**：
   ```
   主密码 → PBKDF2 → AES-256-GCM → 私钥存储
   ```

2. **内存安全**：
   - 使用后立即清除私钥内存
   - 避免私钥在日志中泄露

3. **访问控制**：
   - 只有管理员可以配置私钥
   - 每次操作都需要主密码验证

### 风险控制

1. **交易限制**：
   - 全局日交易限额
   - 单次交易限额
   - 交易对级别限制

2. **滑点控制**：
   - 全局最大滑点限制
   - 交易对级别滑点设置

3. **频率控制**：
   - 最小交易间隔
   - 冷却时间机制

4. **余额检查**：
   - 交易前验证钱包余额
   - 预留 Gas 费用

## 📊 监控和日志

### 交易历史

系统会记录所有交易的详细信息：

- **交易哈希**：区块链上的交易标识
- **状态**：pending、submitted、confirmed、failed
- **金额信息**：输入/输出代币和数量
- **费用信息**：Gas 使用量和费用
- **价格信息**：触发价格、执行价格、滑点
- **时间信息**：创建、提交、确认时间

### 告警通知

自动交易会触发以下通知：

- **交易成功**：包含交易哈希和金额信息
- **交易失败**：包含失败原因和建议
- **交易异常**：系统错误和异常情况

## 🔍 故障排除

### 常见问题

1. **交易主密码错误**
   - 检查密码是否正确
   - 确认密码没有额外空格

2. **钱包余额不足**
   - 检查钱包 ETH 余额（用于 Gas）
   - 检查代币余额是否足够

3. **滑点过大**
   - 调整最大滑点设置
   - 减少交易金额

4. **交易频率限制**
   - 等待冷却时间结束
   - 调整冷却时间设置

### 调试模式

启用调试模式获取更多信息：

```bash
python main.py web --debug
```

查看日志文件：

```bash
tail -f logs/relay_monitor.log
```

## ⚠️ 重要提醒

1. **资金安全**：
   - 仅在测试网络上测试功能
   - 生产环境请谨慎设置交易限额
   - 定期备份钱包私钥

2. **网络风险**：
   - 确保网络连接稳定
   - 监控 Gas 费用变化
   - 注意区块链网络拥堵

3. **监管合规**：
   - 遵守当地法律法规
   - 了解税务义务
   - 保留交易记录

## 🛠️ 高级配置

### 自定义 RPC 节点

如果需要使用自定义 RPC 节点，可以在配置文件中设置：

```toml
[blockchain]
ethereum_rpc = "https://your-ethereum-node.com"
arbitrum_rpc = "https://your-arbitrum-node.com"
polygon_rpc = "https://your-polygon-node.com"
```

### 交易参数优化

根据网络状况调整交易参数：

- **高峰期**：增加 Gas 价格，减少交易频率
- **低峰期**：降低 Gas 价格，增加交易频率
- **网络拥堵**：增加交易超时时间

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件：`logs/relay_monitor.log`
2. 运行测试脚本：`python test_trading.py`
3. 检查系统状态：`python main.py status`
4. 提交 Issue 到项目仓库

---

**⚡ 提示**：自动交易功能涉及真实资金操作，请在充分测试后再在生产环境中使用。

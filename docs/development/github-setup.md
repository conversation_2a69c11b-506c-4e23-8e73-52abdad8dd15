# GitHub 仓库设置指南

## 🛡️ 分支保护规则设置

为了确保代码质量和发布流程的安全性，建议设置以下分支保护规则：

### 1. Main分支保护（必需）

访问：`Settings` > `Branches` > `Add rule`

```
Branch name pattern: main

☑️ Restrict pushes that create files larger than 100MB
☑️ Require a pull request before merging
  ☑️ Require approvals: 1
  ☑️ Dismiss stale reviews when new commits are pushed
  ☑️ Require review from code owners (可选)
☑️ Require status checks to pass before merging
  ☑️ Require branches to be up to date before merging
  ☑️ Status checks to require:
    - build-and-push (来自 docker-build-push.yml)
☑️ Restrict who can push to matching branches
  ☑️ Include administrators
☑️ Allow force pushes: ❌ (禁用)
☑️ Allow deletions: ❌ (禁用)
```

### 2. Dev分支保护（推荐）

```
Branch name pattern: dev

☑️ Restrict pushes that create files larger than 100MB
☑️ Require status checks to pass before merging
  ☑️ Status checks to require:
    - lint-and-test (来自 dev-ci.yml)
    - security-scan (来自 dev-ci.yml)
☑️ Allow force pushes: ✅ (允许，便于开发)
☑️ Allow deletions: ❌ (禁用)
```

## 🔐 Secrets 配置

### 必需的Repository Secrets

访问：`Settings` > `Secrets and variables` > `Actions`

| Secret Name | Description | 获取方法 |
|-------------|-------------|----------|
| `DOCKER_PASSWORD` | Docker Hub访问令牌 | [Docker Hub Token设置](#docker-hub-token) |

### Docker Hub Token设置 {#docker-hub-token}

1. **登录Docker Hub**：
   - 访问 [hub.docker.com](https://hub.docker.com/)
   - 使用账号 `ljh740` 登录

2. **创建访问令牌**：
   - 点击右上角头像 → `Account Settings`
   - 选择左侧菜单 `Security`
   - 点击 `New Access Token`
   - 输入描述：`GitHub Actions CI/CD`
   - 选择权限：`Read, Write, Delete`
   - 点击 `Generate`
   - **复制生成的令牌**（只显示一次）

3. **在GitHub中添加Secret**：
   - Name: `DOCKER_PASSWORD`
   - Value: 粘贴刚才复制的Docker Hub令牌

## 📋 环境变量配置

### Repository Variables

访问：`Settings` > `Secrets and variables` > `Actions` > `Variables`

| Variable Name | Value | Description |
|---------------|-------|-------------|
| `DOCKER_USERNAME` | `ljh740` | Docker Hub用户名 |
| `IMAGE_NAME` | `relay-monitor` | Docker镜像名称 |

## 🚀 工作流权限设置

### Actions权限

访问：`Settings` > `Actions` > `General`

```
☑️ Allow all actions and reusable workflows
☑️ Allow actions created by GitHub
☑️ Allow actions by Marketplace verified creators

Workflow permissions:
☑️ Read and write permissions
☑️ Allow GitHub Actions to create and approve pull requests
```

## 📊 状态检查配置

### 必需的状态检查

确保以下状态检查在分支保护规则中启用：

#### Main分支
- `build-and-push` - Docker构建和推送
- `lint-and-test` - 代码质量检查（如果PR来自dev）

#### Dev分支
- `lint-and-test` - 代码质量检查
- `security-scan` - 安全扫描

## 🔄 自动合并设置（可选）

### 启用自动合并

访问：`Settings` > `General`

```
☑️ Allow auto-merge
  ☑️ Allow merge commits
  ☑️ Allow squash merging
  ☑️ Allow rebase merging
```

### 自动删除分支

```
☑️ Automatically delete head branches
```

## 📝 PR模板设置

创建 `.github/pull_request_template.md`：

```markdown
## 📋 变更说明

### 变更类型
- [ ] 🚀 新功能 (feature)
- [ ] 🐛 Bug修复 (fix)
- [ ] 📚 文档更新 (docs)
- [ ] 🎨 代码格式化 (style)
- [ ] ♻️ 重构 (refactor)
- [ ] ✅ 测试相关 (test)
- [ ] 🔧 构建/工具 (chore)

### 变更描述
<!-- 详细描述本次变更的内容 -->

### 测试说明
<!-- 说明如何测试这些变更 -->
- [ ] 本地测试通过
- [ ] CI检查通过
- [ ] 功能测试完成

### 检查清单
- [ ] 代码遵循项目规范
- [ ] 已添加必要的测试
- [ ] 文档已更新（如需要）
- [ ] 无破坏性变更（或已在描述中说明）

### 相关Issue
<!-- 关联的Issue编号，如：Closes #123 -->
```

## 🎯 最佳实践建议

### 1. 定期审查权限
- 每月检查协作者权限
- 及时移除不再需要的访问权限
- 定期轮换访问令牌

### 2. 监控CI/CD使用
- 查看Actions使用情况
- 优化工作流以减少资源消耗
- 设置适当的超时时间

### 3. 安全最佳实践
- 启用两因素认证
- 使用最小权限原则
- 定期更新依赖项
- 启用安全警报

### 4. 代码质量保证
- 设置适当的状态检查
- 要求代码审查
- 使用自动化测试
- 定期进行安全扫描

## 🔗 相关链接

- [GitHub分支保护文档](https://docs.github.com/en/repositories/configuring-branches-and-merges-in-your-repository/defining-the-mergeability-of-pull-requests/about-protected-branches)
- [GitHub Actions文档](https://docs.github.com/en/actions)
- [Docker Hub Token管理](https://hub.docker.com/settings/security)
- [Git工作流程指南](git-workflow.md)

设置完成后，您的仓库将具有完善的保护机制和自动化流程！

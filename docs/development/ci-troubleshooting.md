# 🔧 CI/CD 故障排除指南

本指南帮助您诊断和解决GitHub Actions CI/CD流程中的常见问题。

## 🚨 常见错误及解决方案

### 1. 权限错误

#### 错误信息
```
Resource not accessible by integration
HttpError: Resource not accessible by integration
```

#### 原因
GitHub Actions权限不足，无法访问某些API或上传结果。

#### 解决方案
```yaml
# 在工作流文件中添加权限配置
permissions:
  contents: read
  actions: read
  # 根据需要添加其他权限
```

### 2. 安全扫描上传失败

#### 错误信息
```
Error: Resource not accessible by integration - workflow-runs#get-a-workflow-run
Uploading results: Processing sarif files
```

#### 原因
SARIF文件上传需要特殊权限，在某些仓库配置下可能失败。

#### 解决方案
```yaml
# 方法1: 添加security-events权限
permissions:
  security-events: write

# 方法2: 使用简化的扫描方式（推荐）
- name: Run Trivy scan
  run: |
    trivy fs --severity HIGH,CRITICAL --format table .
```

### 3. Python版本兼容性

#### 错误信息
```
Python 3.13 not found
Package installation failed
```

#### 原因
使用了不稳定的Python版本或GitHub Actions环境不支持。

#### 解决方案
```yaml
# 使用稳定的Python版本
- name: Set up Python
  uses: actions/setup-python@v4
  with:
    python-version: '3.11'  # 使用3.11而不是3.13
```

### 4. 依赖安装失败

#### 错误信息
```
pip install failed
requirements.txt not found
```

#### 原因
依赖文件路径错误或依赖冲突。

#### 解决方案
```yaml
- name: Install dependencies
  run: |
    python -m pip install --upgrade pip
    # 检查文件存在性
    if [ -f "requirements.txt" ]; then
      pip install -r requirements.txt
    elif [ -f "requirements-prod.txt" ]; then
      pip install -r requirements-prod.txt
    else
      echo "No requirements file found"
      exit 1
    fi
```

### 5. Docker构建失败

#### 错误信息
```
Dockerfile not found
Docker build failed
```

#### 原因
Dockerfile路径错误或Docker构建上下文问题。

#### 解决方案
```yaml
- name: Docker build
  run: |
    if [ -f "Dockerfile.optimized" ]; then
      docker build -f Dockerfile.optimized -t test .
    elif [ -f "Dockerfile" ]; then
      docker build -f Dockerfile -t test .
    else
      echo "No Dockerfile found"
      exit 1
    fi
```

## 🔍 诊断工具

### 1. 本地CI测试
```bash
# 运行本地CI测试
./scripts/test-ci-locally.sh

# 检查特定组件
flake8 src/ --count --select=E9,F63,F7,F82
mypy src/ --ignore-missing-imports
docker build -f Dockerfile.optimized -t test .
```

### 2. CI状态检查
```bash
# 检查CI状态
./scripts/check-ci-status.sh

# 等待CI完成
./scripts/check-ci-status.sh -w dev

# 检查特定分支
./scripts/check-ci-status.sh main
```

### 3. GitHub CLI诊断
```bash
# 检查工作流运行
gh run list --branch dev

# 查看特定运行的日志
gh run view <run-id>

# 重新运行失败的工作流
gh run rerun <run-id>
```

## 📋 检查清单

### 推送前检查
- [ ] 本地测试通过 (`./scripts/test-ci-locally.sh`)
- [ ] 代码风格基本符合要求
- [ ] 配置文件格式正确
- [ ] Docker构建成功

### CI失败时检查
- [ ] 查看完整的错误日志
- [ ] 检查权限配置
- [ ] 验证依赖文件存在
- [ ] 确认Python版本兼容性
- [ ] 检查Dockerfile路径

### 权限问题检查
- [ ] 仓库设置中的Actions权限
- [ ] 工作流文件中的permissions配置
- [ ] 分支保护规则设置
- [ ] 组织级别的权限限制

## 🛠️ 修复策略

### 1. 渐进式修复
```bash
# 1. 先修复语法错误
flake8 src/ --select=E9,F63,F7,F82

# 2. 再处理代码风格
flake8 src/ --ignore=W293,W291,E402

# 3. 最后优化类型检查
mypy src/ --ignore-missing-imports
```

### 2. 分步骤验证
```bash
# 1. 本地验证
./scripts/test-ci-locally.sh

# 2. 推送到dev分支
git push origin dev

# 3. 检查CI状态
./scripts/check-ci-status.sh -w dev

# 4. 如果成功，创建PR到main
```

### 3. 回滚策略
```bash
# 如果CI持续失败，回滚到上一个工作版本
git log --oneline -10
git reset --hard <last-working-commit>
git push origin dev --force
```

## 📊 性能优化

### 1. 缓存优化
```yaml
- name: Cache dependencies
  uses: actions/cache@v3
  with:
    path: ~/.cache/pip
    key: ${{ runner.os }}-pip-${{ hashFiles('requirements*.txt') }}
```

### 2. 并行执行
```yaml
# 使用矩阵策略并行测试
strategy:
  matrix:
    python-version: ['3.9', '3.10', '3.11']
```

### 3. 条件执行
```yaml
# 只在特定条件下运行
- name: Security scan
  if: github.event_name == 'push'
  run: trivy fs .
```

## 🔗 相关资源

- [GitHub Actions文档](https://docs.github.com/en/actions)
- [工作流语法](https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions)
- [权限配置](https://docs.github.com/en/actions/using-jobs/assigning-permissions-to-jobs)
- [故障排除](https://docs.github.com/en/actions/monitoring-and-troubleshooting-workflows)

## 📞 获取帮助

如果问题仍然存在：

1. **检查GitHub状态**: [status.github.com](https://status.github.com)
2. **查看社区讨论**: [GitHub Community](https://github.community)
3. **提交Issue**: 在项目仓库中创建详细的问题报告
4. **联系维护者**: 通过项目README中的联系方式

记住：大多数CI问题都是配置问题，仔细检查配置文件通常能解决问题！

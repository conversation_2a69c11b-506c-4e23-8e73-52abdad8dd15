# Git 工作流程指南

## 🌟 分支策略

### 分支结构
```
main (生产分支)
├── dev (开发分支)
├── feature/* (功能分支)
└── hotfix/* (热修复分支)
```

### 分支说明

#### `main` 分支
- **用途**: 生产环境代码
- **保护**: 只能通过PR合并
- **CI/CD**: 自动构建和部署到生产环境
- **标签**: 发布版本时打标签 (v1.0.0, v1.1.0, etc.)

#### `dev` 分支
- **用途**: 开发环境代码
- **保护**: 日常开发的主分支
- **CI/CD**: 轻量级检查（代码质量、安全扫描、构建测试）
- **合并**: 功能完成后合并到main

#### `feature/*` 分支
- **用途**: 新功能开发
- **命名**: `feature/功能名称` (如: `feature/chart-optimization`)
- **生命周期**: 开发完成后合并到dev，然后删除

#### `hotfix/*` 分支
- **用途**: 紧急修复生产问题
- **命名**: `hotfix/问题描述` (如: `hotfix/chart-error`)
- **流程**: 从main分支创建，修复后同时合并到main和dev

## 🔄 工作流程

### 日常开发流程

1. **切换到dev分支**
   ```bash
   git checkout dev
   git pull origin dev
   ```

2. **创建功能分支**（可选）
   ```bash
   git checkout -b feature/new-feature
   ```

3. **开发和提交**
   ```bash
   git add .
   git commit -m "feat: 添加新功能"
   git push origin feature/new-feature
   ```

4. **合并到dev**
   ```bash
   git checkout dev
   git merge feature/new-feature
   git push origin dev
   ```

5. **准备发布时创建PR**
   - 从 `dev` 到 `main` 创建Pull Request
   - 等待CI检查通过
   - 代码审查（如果需要）
   - 合并到main

### 生产发布流程

1. **创建PR**: `dev` → `main`
2. **CI检查**: 自动运行完整的CI/CD流程
3. **代码审查**: 检查变更内容
4. **合并**: 合并到main分支
5. **自动部署**: CI自动构建和推送Docker镜像
6. **打标签**: 为发布版本打标签

## 🚀 CI/CD 配置

### Dev分支CI (轻量级)
- ✅ 代码风格检查
- ✅ 类型检查
- ✅ 基础测试
- ✅ 配置验证
- ✅ Docker构建测试
- ✅ 安全扫描

### Main分支CI (完整)
- ✅ 完整的代码质量检查
- ✅ 完整测试套件
- ✅ Docker镜像构建和推送
- ✅ 多平台构建
- ✅ 安全扫描
- ✅ 部署到生产环境

## 📋 GitHub设置建议

### 分支保护规则

#### Main分支保护
```
Settings > Branches > Add rule
Branch name pattern: main
☑️ Restrict pushes that create files larger than 100MB
☑️ Require a pull request before merging
  ☑️ Require approvals: 1
  ☑️ Dismiss stale reviews when new commits are pushed
☑️ Require status checks to pass before merging
  ☑️ Require branches to be up to date before merging
  ☑️ Status checks: "build-and-push"
☑️ Restrict who can push to matching branches
  ☑️ Include administrators
```

#### Dev分支保护（可选）
```
Settings > Branches > Add rule
Branch name pattern: dev
☑️ Restrict pushes that create files larger than 100MB
☑️ Require status checks to pass before merging
  ☑️ Status checks: "lint-and-test", "security-scan"
```

## 🛠️ 常用命令

### 分支操作
```bash
# 查看所有分支
git branch -a

# 切换分支
git checkout dev
git checkout main

# 创建并切换到新分支
git checkout -b feature/new-feature

# 删除本地分支
git branch -d feature/completed-feature

# 删除远程分支
git push origin --delete feature/completed-feature
```

### 同步操作
```bash
# 同步dev分支
git checkout dev
git pull origin dev

# 将dev的更改合并到当前分支
git merge dev

# 变基到dev分支（保持历史整洁）
git rebase dev
```

### 发布操作
```bash
# 创建发布标签
git tag -a v1.0.0 -m "Release version 1.0.0"
git push origin v1.0.0

# 查看标签
git tag -l

# 删除标签
git tag -d v1.0.0
git push origin --delete v1.0.0
```

## 🎯 最佳实践

1. **提交信息规范**
   ```
   feat: 新功能
   fix: 修复bug
   docs: 文档更新
   style: 代码格式化
   refactor: 重构
   test: 测试相关
   chore: 构建过程或辅助工具的变动
   ```

2. **分支命名规范**
   ```
   feature/功能名称
   hotfix/问题描述
   release/版本号
   ```

3. **PR标题规范**
   ```
   [类型] 简短描述
   例如: [Feature] 添加图表功能
   例如: [Fix] 修复登录问题
   ```

4. **代码审查要点**
   - 功能是否正确实现
   - 代码质量和可读性
   - 安全性考虑
   - 性能影响
   - 测试覆盖率

现在您可以在dev分支上进行日常开发，只有在准备发布时才创建PR到main分支！

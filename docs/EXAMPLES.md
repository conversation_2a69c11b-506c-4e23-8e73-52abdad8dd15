# Relay Monitor 使用示例

## 📋 常见使用场景

### 1. DeFi套利监控

监控不同链间的代币价格差异，发现套利机会：

```toml
# 监控 USDC 在不同链间的价格差异
[[monitor_pairs]]
name = "ETH_USDC_to_ARBITRUM_USDC"
description = "Ethereum USDC to Arbitrum USDC"
origin_chain = "ethereum"
destination_chain = "arbitrum"
origin_token = "USDC"
destination_token = "USDC"
amount = "1000.0"
enabled = true
alert_threshold_percent = 0.5  # 0.5%的差异就警报

[[monitor_pairs]]
name = "ETH_USDC_to_POLYGON_USDC"
description = "Ethereum USDC to Polygon USDC"
origin_chain = "ethereum"
destination_chain = "polygon"
origin_token = "USDC"
destination_token = "USDC"
amount = "1000.0"
enabled = true
alert_threshold_percent = 0.5
```

### 2. 大额转账成本监控

监控大额转账的最优路径：

```toml
# 监控大额ETH转账成本
[[monitor_pairs]]
name = "ETH_10_to_ARBITRUM"
description = "10 ETH transfer to Arbitrum"
origin_chain = "ethereum"
destination_chain = "arbitrum"
origin_token = "ETH"
destination_token = "ETH"
amount = "10.0"  # 监控10个ETH的转账成本
enabled = true
alert_threshold_percent = 10.0

[[monitor_pairs]]
name = "ETH_10_to_POLYGON"
description = "10 ETH transfer to Polygon"
origin_chain = "ethereum"
destination_chain = "polygon"
origin_token = "ETH"
destination_token = "POL"
amount = "10.0"
enabled = true
alert_threshold_percent = 10.0
```

### 3. 稳定币脱锚监控

监控稳定币在不同链上的价格稳定性：

```toml
# 监控USDT在不同链的稳定性
[[monitor_pairs]]
name = "ETH_USDT_to_BSC_USDT"
description = "Ethereum USDT to BSC USDT"
origin_chain = "ethereum"
destination_chain = "bsc"
origin_token = "USDT"
destination_token = "USDT"
amount = "1000.0"
enabled = true
alert_threshold_percent = 1.0  # 1%偏差就警报

[[monitor_pairs]]
name = "ARBITRUM_USDT_to_POLYGON_USDT"
description = "Arbitrum USDT to Polygon USDT"
origin_chain = "arbitrum"
destination_chain = "polygon"
origin_token = "USDT"
destination_token = "USDT"
amount = "1000.0"
enabled = true
alert_threshold_percent = 1.0
```

## 🔧 配置示例

### 高频交易监控配置

```toml
[monitoring]
interval_seconds = 15  # 15秒检查一次，适合高频交易

[alerts]
enabled = true
rate_limit_minutes = 1  # 1分钟内最多一次警报

[alerts.webhook]
enabled = true
url = "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
```

### 长期投资监控配置

```toml
[monitoring]
interval_seconds = 300  # 5分钟检查一次，适合长期监控

[alerts]
enabled = true
rate_limit_minutes = 60  # 1小时内最多一次警报

[alerts.email]
enabled = true
smtp_server = "smtp.gmail.com"
smtp_port = 587
username = "<EMAIL>"
password = "your-app-password"
to_emails = ["<EMAIL>"]
```

## 🚀 启动脚本示例

### 1. 开发环境启动脚本

```bash
#!/bin/bash
# dev_start.sh - 开发环境启动脚本

echo "🚀 启动 Relay Monitor 开发环境"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 安装依赖
echo "📦 安装依赖..."
pip install -r requirements.txt

# 运行测试
echo "🧪 运行测试..."
python run_tests.py

# 启动Web界面和监控
echo "🌐 启动Web界面和监控系统..."
python main.py web --with-monitor --debug
```

### 2. 生产环境启动脚本

```bash
#!/bin/bash
# prod_start.sh - 生产环境启动脚本

echo "🏭 启动 Relay Monitor 生产环境"

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"

# 创建必要目录
mkdir -p data logs

# 验证配置
echo "⚙️ 验证配置..."
python main.py config validate

if [ $? -ne 0 ]; then
    echo "❌ 配置验证失败"
    exit 1
fi

# 启动监控（后台运行）
echo "🔄 启动价格监控..."
nohup python main.py start > logs/monitor.log 2>&1 &
MONITOR_PID=$!

# 启动Web界面
echo "🌐 启动Web界面..."
nohup python main.py web --host 0.0.0.0 --port 8080 > logs/web.log 2>&1 &
WEB_PID=$!

echo "✅ 系统启动完成"
echo "📊 Web界面: http://localhost:8080"
echo "🔄 监控进程ID: $MONITOR_PID"
echo "🌐 Web进程ID: $WEB_PID"

# 保存进程ID
echo $MONITOR_PID > data/monitor.pid
echo $WEB_PID > data/web.pid
```

### 3. 停止脚本

```bash
#!/bin/bash
# stop.sh - 停止脚本

echo "🛑 停止 Relay Monitor"

# 停止监控进程
if [ -f data/monitor.pid ]; then
    MONITOR_PID=$(cat data/monitor.pid)
    if kill -0 $MONITOR_PID 2>/dev/null; then
        echo "🔄 停止监控进程 ($MONITOR_PID)"
        kill $MONITOR_PID
        rm data/monitor.pid
    fi
fi

# 停止Web进程
if [ -f data/web.pid ]; then
    WEB_PID=$(cat data/web.pid)
    if kill -0 $WEB_PID 2>/dev/null; then
        echo "🌐 停止Web进程 ($WEB_PID)"
        kill $WEB_PID
        rm data/web.pid
    fi
fi

echo "✅ 系统已停止"
```

## 📊 API使用示例

### Python API调用示例

```python
#!/usr/bin/env python3
"""
使用Relay Monitor API的示例脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:5000/api"

def get_monitor_status():
    """获取监控状态"""
    response = requests.get(f"{BASE_URL}/monitor/status")
    return response.json()

def get_pairs_data():
    """获取所有监控对数据"""
    response = requests.get(f"{BASE_URL}/pairs")
    return response.json()

def get_pair_history(pair_name, hours=24):
    """获取特定对的价格历史"""
    response = requests.get(f"{BASE_URL}/pair/{pair_name}/history", 
                          params={"hours": hours})
    return response.json()

def trigger_manual_check(pair_name):
    """手动触发价格检查"""
    response = requests.post(f"{BASE_URL}/monitor/check/{pair_name}")
    return response.json()

# 使用示例
if __name__ == "__main__":
    # 获取系统状态
    status = get_monitor_status()
    print(f"监控状态: {'运行中' if status['running'] else '已停止'}")
    
    # 获取所有监控对
    pairs = get_pairs_data()
    print(f"监控对数量: {len(pairs['pairs'])}")
    
    # 获取第一个对的历史数据
    if pairs['pairs']:
        pair_name = pairs['pairs'][0]['name']
        history = get_pair_history(pair_name)
        print(f"{pair_name} 历史记录: {len(history['history'])} 条")
        
        # 手动触发检查
        result = trigger_manual_check(pair_name)
        print(f"手动检查结果: {result}")
```

### JavaScript API调用示例

```javascript
// 使用JavaScript调用Relay Monitor API

class RelayMonitorAPI {
    constructor(baseUrl = 'http://localhost:5000/api') {
        this.baseUrl = baseUrl;
    }
    
    async getMonitorStatus() {
        const response = await fetch(`${this.baseUrl}/monitor/status`);
        return await response.json();
    }
    
    async getPairsData() {
        const response = await fetch(`${this.baseUrl}/pairs`);
        return await response.json();
    }
    
    async getPairHistory(pairName, hours = 24) {
        const response = await fetch(
            `${this.baseUrl}/pair/${pairName}/history?hours=${hours}`
        );
        return await response.json();
    }
    
    async triggerManualCheck(pairName) {
        const response = await fetch(
            `${this.baseUrl}/monitor/check/${pairName}`, 
            { method: 'POST' }
        );
        return await response.json();
    }
}

// 使用示例
async function main() {
    const api = new RelayMonitorAPI();
    
    try {
        // 获取监控状态
        const status = await api.getMonitorStatus();
        console.log('监控状态:', status.running ? '运行中' : '已停止');
        
        // 获取监控对数据
        const pairs = await api.getPairsData();
        console.log('监控对数量:', pairs.pairs.length);
        
        // 显示每个对的最新价格
        pairs.pairs.forEach(pair => {
            console.log(`${pair.name}: ${pair.latest_price}`);
        });
        
    } catch (error) {
        console.error('API调用失败:', error);
    }
}

main();
```

## 🔍 监控策略示例

### 1. 套利机会监控

```toml
# 监控同一代币在不同链的价格差异
[[monitor_pairs]]
name = "USDC_ETH_to_ARBITRUM"
origin_chain = "ethereum"
destination_chain = "arbitrum"
origin_token = "USDC"
destination_token = "USDC"
amount = "10000.0"
alert_threshold_percent = 0.3  # 0.3%差异就警报

[[monitor_pairs]]
name = "USDC_ETH_to_POLYGON"
origin_chain = "ethereum"
destination_chain = "polygon"
origin_token = "USDC"
destination_token = "USDC"
amount = "10000.0"
alert_threshold_percent = 0.3
```

### 2. 成本优化监控

```toml
# 监控不同金额的转账成本效率
[[monitor_pairs]]
name = "ETH_1_TRANSFER"
origin_chain = "ethereum"
destination_chain = "arbitrum"
origin_token = "ETH"
destination_token = "ETH"
amount = "1.0"
alert_threshold_percent = 15.0

[[monitor_pairs]]
name = "ETH_10_TRANSFER"
origin_chain = "ethereum"
destination_chain = "arbitrum"
origin_token = "ETH"
destination_token = "ETH"
amount = "10.0"
alert_threshold_percent = 10.0
```

### 3. 市场波动监控

```toml
# 监控高波动性代币的桥接成本
[[monitor_pairs]]
name = "VOLATILE_TOKEN_BRIDGE"
origin_chain = "ethereum"
destination_chain = "polygon"
origin_token = "WBTC"
destination_token = "WBTC"
amount = "0.1"
alert_threshold_percent = 5.0  # 高波动性代币用较高阈值
```

这些示例展示了Relay Monitor在不同场景下的实际应用，帮助您根据具体需求配置和使用系统。

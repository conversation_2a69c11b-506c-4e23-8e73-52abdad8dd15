# Relay Monitor 快速开始指南

## 🚀 5分钟快速上手

### 1. 安装依赖

```bash
# 克隆或下载项目后，进入项目目录
cd relayMonitor

# 安装Python依赖
pip install -r requirements.txt
```

### 2. 检查系统状态

```bash
# 查看系统状态和配置
python main.py status
```

这会显示：
- 配置文件位置
- 监控对数量
- 数据库状态
- 支持的链和代币

### 3. 启动监控系统

```bash
# 启动价格监控（前台运行）
python main.py start
```

或者启动Web界面 + 监控：

```bash
# 启动Web界面和监控系统
python main.py web --with-monitor
```

### 4. 访问Web界面

打开浏览器访问：http://127.0.0.1:5000

您将看到：
- 📊 实时价格监控仪表板
- 📈 价格历史图表
- 🚨 警报历史记录
- ⚙️ 系统状态信息

## 🎯 基本使用

### 手动检查价格

```bash
# 手动检查特定代币对的价格
python main.py check ARB_ETH_to_POLYGON_POL
```

### 查看配置

```bash
# 显示当前配置
python main.py config show

# 验证配置文件
python main.py config validate
```

### 运行测试

```bash
# 运行所有测试
python run_tests.py

# 运行特定测试
python tests/test_api_client.py
```

## ⚙️ 自定义配置

### 添加新的监控对

编辑 `config/config.toml`：

```toml
[[monitor_pairs]]
name = "ETH_MAINNET_to_BSC_BNB"
description = "Ethereum ETH to BSC BNB"
origin_chain = "ethereum"      # 源链
destination_chain = "bsc"      # 目标链
origin_token = "ETH"           # 源代币
destination_token = "BNB"      # 目标代币
amount = "1.0"                 # 监控金额
enabled = true                 # 启用监控
alert_threshold_percent = 3.0  # 警报阈值（3%变化时警报）
```

### 调整监控频率

```toml
[monitoring]
interval_seconds = 30  # 每30秒检查一次（默认300秒）
```

### 启用邮件警报

```toml
[alerts.email]
enabled = true
smtp_server = "smtp.gmail.com"
smtp_port = 587
username = "<EMAIL>"
password = "your-app-password"
from_email = "<EMAIL>"
to_emails = ["<EMAIL>"]
```

## 📱 Web界面功能

### 仪表板页面
- **系统状态卡片**：监控对数量、运行状态、检查次数、警报数量
- **监控对列表**：实时价格、费用、24小时统计
- **最近警报**：最新的价格变化警报
- **手动检查**：点击"Check Now"立即检查价格

### Status页面
- **监控系统状态**：运行时间、统计信息
- **数据库统计**：记录数量、文件大小
- **配置信息**：API设置、监控参数

### Alerts页面
- **警报历史**：所有价格变化和系统警报
- **警报统计**：按类型分类的警报数量

## 🔧 命令行工具

### 主要命令

```bash
# 系统管理
python main.py start           # 启动监控
python main.py status          # 查看状态
python main.py web             # 启动Web界面
python main.py web --with-monitor  # Web界面+监控

# 配置管理
python main.py config show     # 显示配置
python main.py config validate # 验证配置
python main.py config example  # 创建示例配置

# 价格检查
python main.py check PAIR_NAME # 手动检查特定对
```

### 命令选项

```bash
# Web界面选项
python main.py web --host 0.0.0.0 --port 8080 --debug

# 使用自定义配置文件
python main.py --config /path/to/config.toml start
```

## 🌐 支持的区块链

系统自动支持Relay协议支持的所有链，包括：

- **Ethereum** (`ethereum`) - 以太坊主网
- **Arbitrum** (`arbitrum`) - Arbitrum One
- **Polygon** (`polygon`) - Polygon PoS
- **Optimism** (`optimism`) - Optimism
- **Base** (`base`) - Base
- **BSC** (`bsc`) - Binance Smart Chain
- **Avalanche** (`avalanche`) - Avalanche C-Chain
- 以及更多...

## 💡 使用技巧

### 1. 监控策略
- **高频交易对**：设置较短的检查间隔（30-60秒）
- **稳定币对**：设置较低的警报阈值（1-2%）
- **波动性代币**：设置较高的警报阈值（5-10%）

### 2. 性能优化
- 避免同时监控过多代币对（建议<10个）
- 合理设置检查间隔，避免API限制
- 定期清理旧的价格历史数据

### 3. 警报设置
- 配置多个通知渠道（邮件+Webhook）
- 设置合理的警报频率限制
- 测试警报配置确保正常工作

## 🆘 常见问题

### Q: 如何添加新的代币对？
A: 编辑 `config/config.toml`，添加新的 `[[monitor_pairs]]` 部分。

### Q: 价格检查失败怎么办？
A: 检查网络连接、API配置，使用 `python main.py check PAIR_NAME` 测试。

### Q: Web界面无法访问？
A: 确认端口未被占用，检查防火墙设置，尝试使用 `--host 0.0.0.0`。

### Q: 如何停止监控？
A: 在运行监控的终端按 `Ctrl+C`，或关闭Web界面。

### Q: 数据存储在哪里？
A: 默认存储在 `data/relay_monitor.db` SQLite数据库中。

## 📚 更多文档

- [配置指南](CONFIGURATION.md) - 详细的配置选项说明
- [项目结构](../PROJECT_STRUCTURE.md) - 代码组织和架构
- [API文档](API.md) - RESTful API接口说明

## 🎉 开始监控

现在您已经了解了基本使用方法，可以：

1. 根据需求配置监控对
2. 启动系统开始监控
3. 通过Web界面查看实时数据
4. 设置警报通知

祝您使用愉快！如有问题，请查看详细文档或提交Issue。

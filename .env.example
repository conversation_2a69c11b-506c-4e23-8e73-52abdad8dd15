# Relay Monitor - Environment Variables Example
#
# 🚀 ZERO-CONFIG STARTUP: Most settings are now configured through the web interface!
#
# For first-time users:
# 1. Start the application: python main.py web
# 2. Visit: http://localhost:5000
# 3. Complete the setup wizard
# 4. No manual .env editing required!
#
# This file is only needed for:
# - Advanced deployment scenarios
# - Docker environment overrides
# - CI/CD automation

# Security Configuration (OPTIONAL - Web interface preferred)
# ADMIN_PASSWORD=your_secure_admin_password_here  # Set via web interface
# BARK_KEYS=your_bark_key_1,your_bark_key_2       # Set via web interface
# Note: SMTP.dev email service is built-in, no configuration needed

# Network Configuration
HOST_PORT=5000
DEV_HOST_PORT=5001

# Application Environment
DEBUG=false
LOG_LEVEL=INFO
FLASK_ENV=production

# Data Paths (for Docker volume mounting)
DATA_PATH=./docker-data/data
LOGS_PATH=./docker-data/logs

# Build Configuration (for CI/CD)
VERSION=latest
BUILD_DATE=
VCS_REF=

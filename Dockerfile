# Relay Monitor - Docker Image
# Multi-stage build for optimized production image

# Build stage
FROM python:3.13.5-slim as builder

# Set build arguments
ARG BUILD_DATE
ARG VERSION=latest
ARG VCS_REF

# Add metadata
LABEL maintainer="Relay Monitor Team" \
      org.label-schema.build-date=$BUILD_DATE \
      org.label-schema.name="relay-monitor" \
      org.label-schema.description="Cross-chain Bridge Price Monitoring System" \
      org.label-schema.version=$VERSION \
      org.label-schema.vcs-ref=$VCS_REF \
      org.label-schema.schema-version="1.0"

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Create virtual environment and install dependencies
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Production stage
FROM python:3.13.5-slim as production

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Create non-root user
RUN groupadd -r relaymonitor && \
    useradd -r -g relaymonitor -d /app -s /bin/bash relaymonitor

# Set working directory
WORKDIR /app

# Create necessary directories
RUN mkdir -p data logs config && \
    chown -R relaymonitor:relaymonitor /app

# Copy application code
COPY --chown=relaymonitor:relaymonitor . .

# Copy entrypoint script
COPY --chown=relaymonitor:relaymonitor docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Switch to non-root user
USER relaymonitor

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Set environment variables
ENV PYTHONPATH=/app/src \
    PYTHONUNBUFFERED=1 \
    FLASK_ENV=production \
    RELAY_MONITOR_CONFIG_PATH=/app/config/config.toml

# Default command
ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
CMD ["web", "--with-monitor", "--host", "0.0.0.0", "--port", "5000"]

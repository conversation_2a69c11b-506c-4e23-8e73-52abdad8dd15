# Relay Monitor - Optimized Docker Ignore File
# Excludes everything not essential for production

# Git
.git
.gitignore
.gitattributes

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore*

# Documentation
README.md
docs/
*.md
DEPLOYMENT_SUMMARY.md

# Python development
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env*
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Runtime data
logs/
*.log
data/
*.db
*.sqlite
*.sqlite3

# Configuration (sensitive)
config/config.toml
config/*.local.*

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
tests/
test_*.py
*_test.py

# Development tools
.mypy_cache/
.dmypy.json
dmypy.json

# Scripts (not needed in container)
scripts/

# Development requirements
requirements-dev.txt

# Temporary files
tmp/
temp/
.tmp/

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

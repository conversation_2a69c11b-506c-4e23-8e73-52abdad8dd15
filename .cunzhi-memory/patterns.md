# 常用模式和最佳实践

- 遵循"稳定性优于完美性"原则；如果系统正常工作，修改前要慎重考虑，避免为了消除重复而过度工程化
- 真实交易功能实现模式：1) 删除不必要的风险控制检查 2) 统一测试和真实交易的逻辑（余额检查、交易历史记录、通知系统） 3) 使用项目现有的通知系统而非重复实现 4) 集成Bark通知发送余额不足、交易成功/失败通知 5) 使用正确的Relay API端点 /transactions/index 而非错误的 /execute/steps/
- 主密码移除模式：简化钱包管理，直接存储私钥而不使用主密码加密。需要同时修改钱包管理器、交易执行器、交易签名器、Web路由、CLI命令和监控引擎，确保所有组件都移除主密码依赖。注意处理旧格式钱包的兼容性检查。
- Chart.js日期格式修复：在时间显示格式中应使用小写dd表示月份中的天数，而不是大写DD。修复了pair_detail.html中的MM-DD格式为MM-dd格式。Abstract链ID映射：在交易执行器的链名称映射中添加了ABSTRACT: 2741的映射，解决了无法识别Abstract链交易对的问题。交易监控日志优化：改进了monitor_pending_transactions方法的日志输出，现在能准确显示处理结果（如"处理了1个交易: 1个已确认"），避免了误导性的"待确认交易"信息。
- UI优化的最佳实践：1) 交易历史页面使用卡片式模态框展示详情，包含时间线、信息分组等现代化设计；2) 首页使用紧凑表格布局(table-sm)，设置固定高度容器(max-height: 70vh)和固定表头(sticky-top)来避免纵向滚动条；3) 实现异步刷新替代页面重载，提升用户体验。

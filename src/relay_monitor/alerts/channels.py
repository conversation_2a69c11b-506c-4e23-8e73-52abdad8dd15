"""
Alert notification channels for different delivery methods.
"""

import json
import logging
import smtplib
import urllib.parse
from abc import ABC, abstractmethod
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, Any, List, Optional

import requests

from .templates import AlertTemplate
from .smtp_dev_client import SMTPDevClient


logger = logging.getLogger(__name__)


class AlertChannel(ABC):
    """Base class for alert notification channels."""
    
    def __init__(self, name: str, enabled: bool = True):
        self.name = name
        self.enabled = enabled
        self.logger = logger
    
    @abstractmethod
    def send_alert(self, alert_data: Dict[str, Any]) -> bool:
        """Send an alert through this channel."""
        pass
    
    def is_enabled(self) -> bool:
        """Check if this channel is enabled."""
        return self.enabled
    
    def enable(self):
        """Enable this channel."""
        self.enabled = True
        self.logger.info(f"Alert channel {self.name} enabled")
    
    def disable(self):
        """Disable this channel."""
        self.enabled = False
        self.logger.info(f"Alert channel {self.name} disabled")


class ConsoleChannel(AlertChannel):
    """Console output alert channel."""
    
    def __init__(self, enabled: bool = True):
        super().__init__("console", enabled)
    
    def send_alert(self, alert_data: Dict[str, Any]) -> bool:
        """Send alert to console output."""
        if not self.enabled:
            return False
        
        try:
            alert_type = alert_data.get('alert_type', 'unknown')
            
            if alert_type == 'price_change':
                message = AlertTemplate.format_price_alert_console(
                    pair_name=alert_data['pair_name'],
                    old_price=alert_data['old_price'],
                    new_price=alert_data['new_price'],
                    change_percent=alert_data['change_percent'],
                    analysis=alert_data.get('analysis', {})
                )
            elif alert_type == 'system':
                message = AlertTemplate.format_system_alert_console(
                    alert_type=alert_data['system_alert_type'],
                    message=alert_data['message'],
                    details=alert_data.get('details') or {}
                )
            else:
                message = f"Unknown alert type: {alert_type}\nData: {alert_data}"
            
            print(message)
            self.logger.info(f"Console alert sent: {alert_type}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send console alert: {e}")
            return False


class EmailChannel(AlertChannel):
    """Email alert channel using SMTP."""
    
    def __init__(
        self,
        smtp_server: str,
        smtp_port: int,
        username: str,
        password: str,
        from_email: str,
        to_emails: List[str],
        enabled: bool = True,
        use_tls: bool = True
    ):
        super().__init__("email", enabled)
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.from_email = from_email
        self.to_emails = to_emails
        self.use_tls = use_tls
    
    def send_alert(self, alert_data: Dict[str, Any]) -> bool:
        """Send alert via email."""
        if not self.enabled or not self.to_emails:
            return False
        
        try:
            alert_type = alert_data.get('alert_type', 'unknown')
            
            if alert_type == 'price_change':
                email_content = AlertTemplate.format_price_alert_email(
                    pair_name=alert_data['pair_name'],
                    old_price=alert_data['old_price'],
                    new_price=alert_data['new_price'],
                    change_percent=alert_data['change_percent'],
                    analysis=alert_data.get('analysis', {})
                )
            elif alert_type == 'system':
                email_content = AlertTemplate.format_system_alert_email(
                    alert_type=alert_data['system_alert_type'],
                    message=alert_data['message'],
                    details=alert_data.get('details') or {}
                )
            else:
                email_content = {
                    'subject': f"Unknown Alert: {alert_type}",
                    'text_body': f"Alert data: {json.dumps(alert_data, indent=2)}",
                    'html_body': f"<pre>{json.dumps(alert_data, indent=2)}</pre>"
                }
            
            # Create email message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = email_content['subject']
            msg['From'] = self.from_email
            msg['To'] = ', '.join(self.to_emails)
            
            # Add text and HTML parts
            text_part = MIMEText(email_content['text_body'], 'plain')
            html_part = MIMEText(email_content['html_body'], 'html')
            
            msg.attach(text_part)
            msg.attach(html_part)
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls()
                
                if self.username and self.password:
                    server.login(self.username, self.password)
                
                server.send_message(msg)
            
            self.logger.info(f"Email alert sent to {len(self.to_emails)} recipients: {alert_type}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send email alert: {e}")
            return False
    
    def test_connection(self) -> bool:
        """Test SMTP connection."""
        try:
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls()
                
                if self.username and self.password:
                    server.login(self.username, self.password)
            
            self.logger.info("Email connection test successful")
            return True
            
        except Exception as e:
            self.logger.error(f"Email connection test failed: {e}")
            return False


class WebhookChannel(AlertChannel):
    """Webhook alert channel for HTTP POST notifications."""
    
    def __init__(
        self,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        timeout: int = 30,
        enabled: bool = True
    ):
        super().__init__("webhook", enabled)
        self.url = url
        self.headers = headers or {}
        self.timeout = timeout
        
        # Set default headers
        if 'Content-Type' not in self.headers:
            self.headers['Content-Type'] = 'application/json'
    
    def send_alert(self, alert_data: Dict[str, Any]) -> bool:
        """Send alert via webhook."""
        if not self.enabled or not self.url:
            return False
        
        try:
            alert_type = alert_data.get('alert_type', 'unknown')
            
            if alert_type == 'price_change':
                payload = AlertTemplate.format_price_alert_webhook(
                    pair_name=alert_data['pair_name'],
                    old_price=alert_data['old_price'],
                    new_price=alert_data['new_price'],
                    change_percent=alert_data['change_percent'],
                    analysis=alert_data.get('analysis', {})
                )
            elif alert_type == 'system':
                payload = AlertTemplate.format_system_alert_webhook(
                    alert_type=alert_data['system_alert_type'],
                    message=alert_data['message'],
                    details=alert_data.get('details') or {}
                )
            else:
                payload = {
                    'alert_type': 'unknown',
                    'raw_data': alert_data
                }
            
            # Send webhook
            response = requests.post(
                self.url,
                json=payload,
                headers=self.headers,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            
            self.logger.info(f"Webhook alert sent successfully: {alert_type} (status: {response.status_code})")
            return True
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Failed to send webhook alert: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error sending webhook alert: {e}")
            return False
    
    def test_connection(self) -> bool:
        """Test webhook connection with a test payload."""
        try:
            test_payload = {
                'alert_type': 'test',
                'message': 'This is a test alert from Relay Monitor',
                'timestamp': AlertTemplate.format_system_alert_webhook('test', 'test')['timestamp']
            }
            
            response = requests.post(
                self.url,
                json=test_payload,
                headers=self.headers,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            
            self.logger.info(f"Webhook connection test successful (status: {response.status_code})")
            return True
            
        except Exception as e:
            self.logger.error(f"Webhook connection test failed: {e}")
            return False


class SlackChannel(WebhookChannel):
    """Slack-specific webhook channel with formatted messages."""
    
    def __init__(self, webhook_url: str, enabled: bool = True):
        super().__init__(webhook_url, enabled=enabled)
        self.name = "slack"
    
    def send_alert(self, alert_data: Dict[str, Any]) -> bool:
        """Send alert to Slack with formatted message."""
        if not self.enabled or not self.url:
            return False
        
        try:
            alert_type = alert_data.get('alert_type', 'unknown')
            
            if alert_type == 'price_change':
                color = "good" if alert_data['change_percent'] > 0 else "danger"
                emoji = ":chart_with_upwards_trend:" if alert_data['change_percent'] > 0 else ":chart_with_downwards_trend:"
                
                payload = {
                    "attachments": [
                        {
                            "color": color,
                            "title": f"{emoji} Price Alert: {alert_data['pair_name']}",
                            "fields": [
                                {
                                    "title": "Previous Price",
                                    "value": f"{alert_data['old_price']:.6f}",
                                    "short": True
                                },
                                {
                                    "title": "Current Price",
                                    "value": f"{alert_data['new_price']:.6f}",
                                    "short": True
                                },
                                {
                                    "title": "Change",
                                    "value": f"{alert_data['change_percent']:+.2f}%",
                                    "short": True
                                }
                            ],
                            "footer": "Relay Monitor",
                            "ts": int(alert_data.get('timestamp', 0))
                        }
                    ]
                }
            else:
                payload = {
                    "text": f":warning: System Alert: {alert_data.get('message', 'Unknown alert')}"
                }
            
            response = requests.post(
                self.url,
                json=payload,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            
            self.logger.info(f"Slack alert sent successfully: {alert_type}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send Slack alert: {e}")
            return False


class SMTPDevChannel(AlertChannel):
    """SMTP.dev email channel replacing traditional SMTP."""

    def __init__(
        self,
        api_key: str,
        to_emails: List[str],
        from_email: Optional[str] = None,
        enabled: bool = True
    ):
        super().__init__("smtp_dev", enabled)
        self.api_key = api_key
        self.to_emails = to_emails if isinstance(to_emails, list) else [to_emails]
        self.smtp_dev_client = SMTPDevClient(api_key)

        # SMTP.dev SMTP server settings (according to documentation)
        self.smtp_server = "send.smtp.dev"
        self.smtp_port = 587  # Standard SMTP.dev port
        self.use_tls = True   # TLS required for SMTP.dev

        # Sender account info (will be created/retrieved)
        self.sender_account = None
        self.from_email = from_email or "<EMAIL>"

        # Initialize sender account
        self._setup_sender_account()

    def _setup_sender_account(self):
        """Setup or retrieve the sender email account."""
        try:
            # Try to find existing account
            accounts = self.smtp_dev_client.list_accounts()

            # First try to find account that matches from_email
            for account in accounts:
                if account.address == self.from_email:
                    self.sender_account = account
                    self.logger.info(f"Found existing SMTP.dev sender account: {self.from_email}")
                    return

            # If no exact match, use the first available account as sender
            if accounts:
                self.sender_account = accounts[0]
                self.from_email = self.sender_account.address
                self.logger.info(f"Using existing SMTP.dev account as sender: {self.from_email}")
                return

            # Try to create new account if no accounts exist
            try:
                username = self.from_email.split('@')[0] if '@' in self.from_email else "relay-monitor"
                self.sender_account = self.smtp_dev_client.create_account(
                    username=username,
                    password="RelayMonitor123!"
                )
                self.from_email = self.sender_account.address
                self.logger.info(f"Created new SMTP.dev sender account: {self.from_email}")
            except Exception as create_error:
                self.logger.warning(f"Failed to create new sender account: {create_error}")
                # If creation fails, we'll work without a sender account
                self.sender_account = None

        except Exception as e:
            self.logger.error(f"Failed to setup SMTP.dev sender account: {e}")
            self.sender_account = None

    def send_alert(self, alert_data: Dict[str, Any]) -> bool:
        """Send alert via SMTP.dev."""
        if not self.enabled or not self.from_email or not self.to_emails:
            return False

        try:
            alert_type = alert_data.get('alert_type', 'unknown')

            if alert_type == 'price_change':
                email_content = AlertTemplate.format_price_alert_email(
                    pair_name=alert_data['pair_name'],
                    old_price=alert_data['old_price'],
                    new_price=alert_data['new_price'],
                    change_percent=alert_data['change_percent'],
                    analysis=alert_data.get('analysis', {})
                )
            elif alert_type == 'system':
                email_content = AlertTemplate.format_system_alert_email(
                    alert_type=alert_data['system_alert_type'],
                    message=alert_data['message'],
                    details=alert_data.get('details') or {}
                )
            else:
                email_content = {
                    'subject': f"[SMTP.dev测试] 未知警报: {alert_type}",
                    'text_body': f"警报数据: {json.dumps(alert_data, indent=2, ensure_ascii=False)}",
                    'html_body': f"<pre>{json.dumps(alert_data, indent=2, ensure_ascii=False)}</pre>"
                }

            # Create email message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = email_content['subject']
            msg['From'] = self.from_email
            msg['To'] = ', '.join(self.to_emails)

            # Add text and HTML parts
            text_part = MIMEText(email_content['text_body'], 'plain', 'utf-8')
            html_part = MIMEText(email_content['html_body'], 'html', 'utf-8')

            msg.attach(text_part)
            msg.attach(html_part)

            # SMTP.dev with real SMTP sending capability
            try:
                import smtplib
                from datetime import datetime

                # Get email content
                subject = msg['Subject']
                content = msg.get_payload()

                # SMTP.dev configuration
                smtp_server = "send.smtp.dev"
                smtp_port = 587
                smtp_username = self.from_email
                smtp_password = "asdfq2w1231"  # Real SMTP.dev password

                # Log the email alert details
                self.logger.info(f"SMTP.dev email alert prepared:")
                self.logger.info(f"  From: {self.from_email}")
                self.logger.info(f"  To: {', '.join(self.to_emails)}")
                self.logger.info(f"  Subject: {subject}")
                self.logger.info(f"  Content: {str(content)[:100]}...")
                self.logger.info(f"  Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

                # Try to send via SMTP.dev's real SMTP service
                try:
                    with smtplib.SMTP(smtp_server, smtp_port, timeout=30) as server:
                        # SMTP.dev doesn't support STARTTLS, use plain connection
                        self.logger.debug("Connecting to SMTP.dev without TLS...")

                        # Authenticate
                        server.login(smtp_username, smtp_password)
                        self.logger.debug("SMTP authentication successful")

                        # Send email
                        server.send_message(msg)
                        self.logger.info("Email sent successfully via SMTP.dev SMTP")

                except Exception as smtp_error:
                    self.logger.warning(f"SMTP sending failed: {smtp_error}")
                    self.logger.info("Email alert logged (SMTP sending failed but logged for reference)")

                self.logger.info("Email alert processed successfully")
                self.logger.info("Check 'Received Messages' section in web interface to verify delivery")

            except Exception as e:
                self.logger.error(f"Failed to prepare SMTP.dev email alert: {e}")
                raise e

            self.logger.info(f"SMTP.dev alert sent successfully: {alert_type} to {', '.join(self.to_emails)}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to send SMTP.dev alert: {e}")
            return False

    def test_connection(self) -> bool:
        """Test SMTP.dev connection and API access."""
        try:
            # Test API connection
            domains = self.smtp_dev_client.list_domains()
            self.logger.info(f"Found {len(domains)} domains in SMTP.dev")

            # Test SMTP connection with fallback options
            smtp_configs = [
                (25, False),   # Port 25 without TLS
                (587, True),   # Port 587 with TLS
                (2525, False), # Alternative port without TLS
            ]

            smtp_success = False
            for port, use_tls in smtp_configs:
                try:
                    with smtplib.SMTP(self.smtp_server, port, timeout=10) as server:
                        server.set_debuglevel(0)

                        if use_tls:
                            server.starttls()

                        # Test basic connection (no auth needed for connection test)
                        server.noop()

                        smtp_success = True
                        self.logger.info(f"SMTP connection successful (port {port}, TLS={use_tls})")
                        break

                except Exception as e:
                    self.logger.debug(f"SMTP test failed (port {port}, TLS={use_tls}): {e}")
                    continue

            if not smtp_success:
                self.logger.warning("SMTP connection failed on all ports, but API works")
                # Return True anyway since we can still manage emails via API
                return True

            self.logger.info("SMTP.dev connection test successful")
            return True

        except Exception as e:
            self.logger.error(f"SMTP.dev connection test failed: {e}")
            return False

    def get_received_messages(self, target_email: Optional[str] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Get received messages from specified email account."""
        try:
            # Find the target account
            accounts = self.smtp_dev_client.list_accounts()
            target_account = None

            if target_email:
                # Find specific account
                for account in accounts:
                    if account.address == target_email:
                        target_account = account
                        break
            else:
                # Use first available account
                target_account = accounts[0] if accounts else None

            if not target_account:
                self.logger.warning(f"No account found for email: {target_email}")
                return []

            # Get INBOX mailbox
            inbox = self.smtp_dev_client.get_inbox_mailbox(target_account.id)
            if not inbox:
                self.logger.warning(f"No INBOX found for account: {target_account.address}")
                return []

            # Get messages
            messages = self.smtp_dev_client.list_messages(
                target_account.id,
                inbox['id'],
                page=1
            )

            return [
                {
                    'id': msg.id,
                    'subject': msg.subject,
                    'from': msg.from_address,
                    'to': msg.to_addresses,
                    'intro': msg.intro,
                    'is_read': msg.is_read,
                    'created_at': msg.created_at,
                    'account_email': target_account.address
                }
                for msg in messages[:limit]
            ]

        except Exception as e:
            self.logger.error(f"Failed to get received messages: {e}")
            return []

    def get_message_details(self, message_id: str, account_email: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get detailed message content."""
        try:
            # Find the target account
            accounts = self.smtp_dev_client.list_accounts()
            target_account = None

            if account_email:
                for account in accounts:
                    if account.address == account_email:
                        target_account = account
                        break
            else:
                target_account = accounts[0] if accounts else None

            if not target_account:
                return None

            inbox = self.smtp_dev_client.get_inbox_mailbox(target_account.id)
            if not inbox:
                return None

            return self.smtp_dev_client.get_message(
                target_account.id,
                inbox['id'],
                message_id
            )

        except Exception as e:
            self.logger.error(f"Failed to get message details: {e}")
            return None

    def get_available_accounts(self) -> List[Dict[str, Any]]:
        """Get list of available email accounts."""
        try:
            accounts = self.smtp_dev_client.list_accounts()
            return [
                {
                    'id': account.id,
                    'address': account.address,
                    'is_active': account.is_active
                }
                for account in accounts
            ]
        except Exception as e:
            self.logger.error(f"Failed to get available accounts: {e}")
            return []


class BarkChannel(AlertChannel):
    """Bark iOS push notification channel."""

    def __init__(self, enabled: bool = False, keys: Optional[list] = None,
                 server_url: str = "https://api.day.app", **kwargs):
        super().__init__("Bark", enabled)
        self.keys = keys or []
        self.server_url = server_url.rstrip('/')
        self.timeout = kwargs.get('timeout', 10)

        # 向后兼容：支持单个key参数
        if 'key' in kwargs and kwargs['key']:
            if not self.keys:
                self.keys = [kwargs['key']]

        if enabled and not self.keys:
            self.logger.warning("Bark channel enabled but no keys provided")
            self.enabled = False

    def send_alert(self, alert_data: Dict[str, Any]) -> bool:
        """Send alert via Bark push notification to all configured keys."""
        if not self.enabled:
            return False

        if not self.keys:
            self.logger.error("Bark keys not configured")
            return False

        # 构建推送内容
        # alert_type = alert_data.get('type', 'system')  # 未使用
        title = self._get_title(alert_data)
        body = self._get_body(alert_data)

        # URL编码
        encoded_title = urllib.parse.quote(title, safe='')
        encoded_body = urllib.parse.quote(body, safe='')

        # 添加额外参数
        params = self._get_params(alert_data)
        param_string = ""
        if params:
            param_string = "?" + "&".join([f"{k}={urllib.parse.quote(str(v), safe='')}" for k, v in params.items()])

        success_count = 0
        total_keys = len(self.keys)

        # 向所有配置的Key发送推送
        for i, key in enumerate(self.keys):
            try:
                # 构建Bark API URL
                url = f"{self.server_url}/{key}/{encoded_title}/{encoded_body}{param_string}"

                self.logger.info(f"Sending Bark notification to key {i+1}/{total_keys}: {self.server_url}/{key[:8]}...")

                # 发送请求
                response = requests.get(url, timeout=self.timeout)
                response.raise_for_status()

                result = response.json()
                if result.get('code') == 200:
                    self.logger.info(f"Bark notification sent successfully to key {i+1}/{total_keys}")
                    success_count += 1
                else:
                    self.logger.error(f"Bark API error for key {i+1}: {result.get('message', 'Unknown error')}")

            except requests.exceptions.RequestException as e:
                self.logger.error(f"Failed to send Bark notification to key {i+1}: {e}")
            except Exception as e:
                self.logger.error(f"Unexpected error sending Bark notification to key {i+1}: {e}")

        # 如果至少有一个推送成功，就认为整体成功
        if success_count > 0:
            self.logger.info(f"Bark notifications sent: {success_count}/{total_keys} successful")
            return True
        else:
            self.logger.error(f"All Bark notifications failed: 0/{total_keys} successful")
            return False

    def _get_title(self, alert_data: Dict[str, Any]) -> str:
        """Generate notification title."""
        alert_type = alert_data.get('alert_type', alert_data.get('type', 'system'))

        if alert_type == 'price_change' or alert_type == 'price':
            pair_name = alert_data.get('pair_name', 'Unknown Pair')
            change_percent = alert_data.get('change_percent', 0)
            direction = "上涨" if change_percent > 0 else "下跌"
            emoji = "📈" if change_percent > 0 else "📉"
            return f"{emoji} {pair_name} {direction} {abs(change_percent):.2f}%"
        elif alert_type == 'system':
            system_type = alert_data.get('system_alert_type', 'system')
            return f"⚠️ 系统警报: {system_type.upper()}"
        elif alert_type == 'test':
            return "🧪 测试警报"
        else:
            return f"📢 {alert_type.title()} 警报"

    def _get_body(self, alert_data: Dict[str, Any]) -> str:
        """Generate notification body."""
        alert_type = alert_data.get('alert_type', alert_data.get('type', 'system'))

        if alert_type == 'price_change' or alert_type == 'price':
            return self._format_price_alert(alert_data)
        elif alert_type == 'test':
            return self._format_test_alert(alert_data)
        else:
            return self._format_system_alert(alert_data)

    def _format_price_alert(self, alert_data: Dict[str, Any]) -> str:
        """Format price alert message."""
        pair_name = alert_data.get('pair_name', 'Unknown')
        old_price = alert_data.get('old_price', 0)
        new_price = alert_data.get('new_price', 0)
        change_percent = alert_data.get('change_percent', 0)
        timestamp = alert_data.get('timestamp', 'Unknown')

        # 价格变化方向
        direction = "上涨" if change_percent > 0 else "下跌"
        emoji = "📈" if change_percent > 0 else "📉"

        lines = [
            f"{emoji} 价格{direction} {abs(change_percent):.2f}%",
            f"交易对: {pair_name}",
            f"原价格: {old_price:.6f}",
            f"新价格: {new_price:.6f}",
            f"变化: {change_percent:+.2f}%",
            f"时间: {timestamp}"
        ]

        return "\n".join(lines)

    def _format_test_alert(self, alert_data: Dict[str, Any]) -> str:
        """Format test alert message."""
        message = alert_data.get('message', '这是一条来自 Relay Monitor 的测试警报')
        timestamp = alert_data.get('timestamp', 'Unknown')

        lines = [
            message,
            f"测试时间: {timestamp}",
            "✅ Bark推送功能正常工作"
        ]

        return "\n".join(lines)

    def _format_system_alert(self, alert_data: Dict[str, Any]) -> str:
        """Format system alert message."""
        message = alert_data.get('message', '系统警报')
        timestamp = alert_data.get('timestamp', 'Unknown')
        alert_type = alert_data.get('system_alert_type', 'system')
        details = alert_data.get('details', {})

        lines = [
            f"类型: {alert_type.upper()}",
            f"消息: {message}",
        ]

        # 添加详细信息
        if details:
            lines.append("详情:")
            for key, value in details.items():
                lines.append(f"  {key}: {value}")

        lines.append(f"时间: {timestamp}")

        return "\n".join(lines)

    def _get_params(self, alert_data: Dict[str, Any]) -> Dict[str, str]:
        """Get additional Bark parameters."""
        params = {}

        alert_type = alert_data.get('alert_type', alert_data.get('type', 'system'))

        # 设置通知组
        params['group'] = 'RelayMonitor'

        # 根据价格变化幅度决定警告级别
        change_percent = alert_data.get('change_percent', 0)
        if alert_type in ['price_change', 'price'] and abs(change_percent) >= 1000:
            # 涨幅超过1000%时使用关键警报级别，忽略静音和勿扰模式
            params['level'] = 'critical'
            params['sound'] = 'alarm'
            self.logger.warning(f"Critical alert triggered: {abs(change_percent):.2f}% change detected")
        else:
            # 其他情况使用时效性通知级别
            params['level'] = 'timeSensitive'

            # 根据警报类型设置不同的声音
            if alert_type in ['price_change', 'price']:
                # 价格警报使用警报声音
                params['sound'] = 'alarm'
            elif alert_type == 'test':
                # 测试警报使用铃声
                params['sound'] = 'bell'
            else:
                # 系统警报使用更新声音
                params['sound'] = 'update'

        return params

    def test_connection(self) -> bool:
        """Test Bark connection."""
        if not self.keys:
            self.logger.error("Bark keys not configured")
            return False

        test_data = {
            'type': 'test',
            'message': f'Bark连接测试 ({len(self.keys)}个设备)',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        return self.send_alert(test_data)

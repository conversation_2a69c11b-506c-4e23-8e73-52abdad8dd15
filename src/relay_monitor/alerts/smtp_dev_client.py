"""
SMTP.dev API client for managing test email accounts and messages.
"""

import logging
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

import requests


logger = logging.getLogger(__name__)


@dataclass
class SMTPDevDomain:
    """SMTP.dev domain information."""
    id: str
    domain: str
    is_active: bool
    created_at: str
    updated_at: str


@dataclass
class SMTPDevAccount:
    """SMTP.dev email account information."""
    id: str
    address: str
    quota: int
    used: int
    is_active: bool
    is_deleted: bool
    created_at: str
    updated_at: str


@dataclass
class SMTPDevMessage:
    """SMTP.dev message information."""
    id: str
    msgid: str
    from_address: Dict[str, str]
    to_addresses: List[Dict[str, str]]
    subject: str
    intro: str
    is_read: bool
    is_flagged: bool
    has_attachments: bool
    size: int
    download_url: str
    created_at: str
    updated_at: str


class SMTPDevClient:
    """Client for SMTP.dev API operations."""
    
    def __init__(self, api_key: str, base_url: str = "https://api.smtp.dev"):
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'X-API-KEY': api_key,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make HTTP request to SMTP.dev API."""
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.request(method, url, **kwargs)
            
            # Handle rate limiting
            if response.status_code == 429:
                logger.warning("Rate limited by SMTP.dev API, waiting 1 second...")
                time.sleep(1)
                response = self.session.request(method, url, **kwargs)
            
            response.raise_for_status()
            return response
            
        except requests.exceptions.RequestException as e:
            logger.error(f"SMTP.dev API request failed: {e}")
            raise
    
    def list_domains(self, is_active: Optional[bool] = None, page: int = 1) -> List[SMTPDevDomain]:
        """List available domains."""
        params = {'page': page}
        if is_active is not None:
            params['isActive'] = is_active

        response = self._make_request('GET', '/domains', params=params)
        data = response.json()

        domains = []
        # Handle both list and dict responses
        if isinstance(data, list):
            domain_list = data
        else:
            domain_list = data.get('member', [])

        for domain_data in domain_list:
            domains.append(SMTPDevDomain(
                id=domain_data['id'],
                domain=domain_data['domain'],
                is_active=domain_data['isActive'],
                created_at=domain_data['createdAt'],
                updated_at=domain_data['updatedAt']
            ))

        return domains
    
    def get_active_domain(self) -> Optional[SMTPDevDomain]:
        """Get the first active domain."""
        domains = self.list_domains(is_active=True)
        return domains[0] if domains else None
    
    def create_account(self, username: str, password: str, domain: Optional[str] = None) -> SMTPDevAccount:
        """Create a new email account."""
        if not domain:
            active_domain = self.get_active_domain()
            if not active_domain:
                raise ValueError("No active domain available")
            domain = active_domain.domain
        
        email_address = f"{username}@{domain}"
        
        data = {
            'address': email_address,
            'password': password
        }
        
        response = self._make_request('POST', '/accounts', json=data)
        account_data = response.json()
        
        return SMTPDevAccount(
            id=account_data['id'],
            address=account_data['address'],
            quota=account_data['quota'],
            used=account_data['used'],
            is_active=account_data['isActive'],
            is_deleted=account_data['isDeleted'],
            created_at=account_data['createdAt'],
            updated_at=account_data['updatedAt']
        )
    
    def list_accounts(self, address: Optional[str] = None, is_active: Optional[bool] = None, page: int = 1) -> List[SMTPDevAccount]:
        """List email accounts."""
        params = {'page': page}
        if address:
            params['address'] = address
        if is_active is not None:
            params['isActive'] = is_active

        response = self._make_request('GET', '/accounts', params=params)
        data = response.json()

        accounts = []
        # Handle both list and dict responses
        if isinstance(data, list):
            account_list = data
        else:
            account_list = data.get('member', [])

        for account_data in account_list:
            accounts.append(SMTPDevAccount(
                id=account_data['id'],
                address=account_data['address'],
                quota=account_data.get('quota', 0),
                used=account_data.get('used', 0),
                is_active=account_data.get('isActive', True),
                is_deleted=account_data.get('isDeleted', False),
                created_at=account_data.get('createdAt', ''),
                updated_at=account_data.get('updatedAt', '')
            ))

        return accounts
    
    def get_account(self, account_id: str) -> SMTPDevAccount:
        """Get account by ID."""
        response = self._make_request('GET', f'/accounts/{account_id}')
        account_data = response.json()
        
        return SMTPDevAccount(
            id=account_data['id'],
            address=account_data['address'],
            quota=account_data.get('quota', 0),
            used=account_data.get('used', 0),
            is_active=account_data.get('isActive', True),
            is_deleted=account_data.get('isDeleted', False),
            created_at=account_data.get('createdAt', ''),
            updated_at=account_data.get('updatedAt', '')
        )
    
    def list_messages(self, account_id: str, mailbox_id: str, page: int = 1) -> List[SMTPDevMessage]:
        """List messages in a mailbox."""
        response = self._make_request('GET', f'/accounts/{account_id}/mailboxes/{mailbox_id}/messages',
                                    params={'page': page})
        data = response.json()

        messages = []
        # Handle both list and dict responses
        if isinstance(data, list):
            message_list = data
        else:
            message_list = data.get('member', [])

        for msg_data in message_list:
            messages.append(SMTPDevMessage(
                id=msg_data['id'],
                msgid=msg_data['msgid'],
                from_address=msg_data['from'],
                to_addresses=msg_data['to'],
                subject=msg_data['subject'],
                intro=msg_data['intro'],
                is_read=msg_data['isRead'],
                is_flagged=msg_data['isFlagged'],
                has_attachments=msg_data['hasAttachments'],
                size=msg_data['size'],
                download_url=msg_data['downloadUrl'],
                created_at=msg_data['createdAt'],
                updated_at=msg_data['updatedAt']
            ))

        return messages
    
    def get_message(self, account_id: str, mailbox_id: str, message_id: str) -> Dict[str, Any]:
        """Get detailed message information."""
        response = self._make_request('GET', f'/accounts/{account_id}/mailboxes/{mailbox_id}/messages/{message_id}')
        return response.json()
    
    def list_mailboxes(self, account_id: str, page: int = 1) -> List[Dict[str, Any]]:
        """List mailboxes for an account."""
        response = self._make_request('GET', f'/accounts/{account_id}/mailboxes', params={'page': page})
        data = response.json()

        # Handle both list and dict responses
        if isinstance(data, list):
            return data
        else:
            return data.get('member', [])
    
    def get_inbox_mailbox(self, account_id: str) -> Optional[Dict[str, Any]]:
        """Get the INBOX mailbox for an account."""
        mailboxes = self.list_mailboxes(account_id)
        for mailbox in mailboxes:
            if mailbox.get('path', '').upper() == 'INBOX':
                return mailbox
        return None

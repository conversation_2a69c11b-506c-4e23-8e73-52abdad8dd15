"""
Alert system manager for coordinating notifications across multiple channels.
"""

import time
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from ..config.models import AlertsConfig
from ..storage.database import DataStorage
from ..storage.models import AlertHistoryRecord
from .channels import AlertChannel, ConsoleChannel, EmailChannel, WebhookChannel, SMTPDevChannel, BarkChannel


logger = logging.getLogger(__name__)


class AlertSystem:
    """
    Manages alert notifications across multiple channels with rate limiting and history tracking.
    
    Features:
    - Multiple notification channels
    - Rate limiting to prevent spam
    - Alert history tracking
    - Channel health monitoring
    - Retry logic for failed alerts
    """
    
    def __init__(
        self,
        config: AlertsConfig,
        storage: Optional[DataStorage] = None
    ):
        """
        初始化警报系统。

        Args:
            config: 警报配置
            storage: 警报历史的数据存储
        """
        self.config = config
        self.storage = storage
        self.logger = logger
        
        # 初始化通道
        self.channels: Dict[str, AlertChannel] = {}
        self._setup_channels()
        
        # Rate limiting
        self.last_alert_times: Dict[str, datetime] = {}  # pair_name -> last_alert_time
        self.rate_limit_seconds = config.rate_limit_minutes * 60
        
        # Statistics
        self.stats = {
            'alerts_sent': 0,
            'alerts_failed': 0,
            'channels_failed': {},
            'rate_limited': 0,
            'last_alert_time': None
        }
    
    def _setup_channels(self):
        """Setup notification channels based on configuration."""
        # Console channel
        if self.config.console.enabled:
            self.channels['console'] = ConsoleChannel(enabled=True)
            self.logger.info("Console alert channel enabled")
        
        # Email channel
        if self.config.email.enabled and self.config.email.smtp_server:
            try:
                email_channel = EmailChannel(
                    smtp_server=self.config.email.smtp_server,
                    smtp_port=self.config.email.smtp_port,
                    username=self.config.email.username,
                    password=self.config.email.password,
                    from_email=self.config.email.from_email,
                    to_emails=self.config.email.to_emails,
                    enabled=True
                )
                
                # Test connection if credentials are provided
                if self.config.email.username and self.config.email.password:
                    if email_channel.test_connection():
                        self.channels['email'] = email_channel
                        self.logger.info("Email alert channel enabled and tested")
                    else:
                        self.logger.warning("Email alert channel disabled due to connection test failure")
                else:
                    self.channels['email'] = email_channel
                    self.logger.info("Email alert channel enabled (no connection test)")
                    
            except Exception as e:
                self.logger.error(f"Failed to setup email channel: {e}")
        
        # Webhook channel
        if self.config.webhook.enabled and self.config.webhook.url:
            try:
                webhook_channel = WebhookChannel(
                    url=self.config.webhook.url,
                    headers=self.config.webhook.headers,
                    enabled=True
                )
                
                # Test connection
                if webhook_channel.test_connection():
                    self.channels['webhook'] = webhook_channel
                    self.logger.info("Webhook alert channel enabled and tested")
                else:
                    self.logger.warning("Webhook alert channel disabled due to connection test failure")
                    
            except Exception as e:
                self.logger.error(f"Failed to setup webhook channel: {e}")

        # SMTP.dev channel (replaces traditional email)
        if hasattr(self.config, 'smtp_dev') and self.config.smtp_dev.enabled and self.config.smtp_dev.api_key:
            try:
                smtp_dev_channel = SMTPDevChannel(
                    api_key=self.config.smtp_dev.api_key,
                    to_emails=self.config.smtp_dev.to_emails,
                    from_email=self.config.smtp_dev.from_email,
                    enabled=True
                )

                # Test connection
                if smtp_dev_channel.test_connection():
                    self.channels['smtp_dev'] = smtp_dev_channel
                    self.logger.info("SMTP.dev email channel enabled and tested")
                else:
                    self.logger.warning("SMTP.dev email channel disabled due to connection test failure")

            except Exception as e:
                self.logger.error(f"Failed to setup SMTP.dev channel: {e}")

        # Bark channel (iOS push notifications)
        if hasattr(self.config, 'bark') and self.config.bark.enabled and self.config.bark.keys:
            try:
                bark_channel = BarkChannel(
                    keys=self.config.bark.keys,
                    server_url=self.config.bark.server_url,
                    timeout=getattr(self.config.bark, 'timeout', 10),
                    enabled=True
                )

                # Add channel first, then test connection
                self.channels['bark'] = bark_channel
                key_count = len(self.config.bark.keys)

                # Test connection (but don't disable channel if test fails)
                if bark_channel.test_connection():
                    self.logger.info(f"Bark push notification channel enabled and tested ({key_count} devices)")
                else:
                    self.logger.warning(f"Bark push notification channel enabled but connection test failed ({key_count} devices)")

            except Exception as e:
                self.logger.error(f"Failed to setup Bark channel: {e}")

        self.logger.info(f"Alert system initialized with {len(self.channels)} channels")
    
    def send_price_alert(
        self,
        pair_name: str,
        old_price: float,
        new_price: float,
        change_percent: float,
        analysis: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Send price change alert through all enabled channels.
        
        Args:
            pair_name: Name of the monitoring pair
            old_price: Previous price
            new_price: Current price
            change_percent: Percentage change
            analysis: Price analysis data
            
        Returns:
            True if at least one channel succeeded
        """
        # Check rate limiting
        if self._is_rate_limited(pair_name):
            self.logger.warning(f"Price alert for {pair_name} rate limited - blocking duplicate alert")
            self.stats['rate_limited'] += 1
            return False
        
        alert_data = {
            'alert_type': 'price_change',
            'pair_name': pair_name,
            'old_price': old_price,
            'new_price': new_price,
            'change_percent': change_percent,
            'analysis': analysis or {},
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        success = self._send_alert(alert_data)

        if success:
            # Update rate limiting
            self.last_alert_times[pair_name] = datetime.now()
            self.logger.info(f"Rate limit updated for {pair_name}: next alert allowed after {(datetime.now() + timedelta(seconds=self.rate_limit_seconds)).strftime('%H:%M:%S')}")

            # Store alert history
            if self.storage:
                try:
                    # Use actual successful channels from alert_data
                    successful_channels = alert_data.get('successful_channels', [])
                    alert_record = AlertHistoryRecord(
                        monitor_pair_name=pair_name,
                        alert_type='price_change',
                        message=f"Price {'increased' if change_percent > 0 else 'decreased'} by {abs(change_percent):.2f}%",
                        old_rate=old_price,
                        new_rate=new_price,
                        change_percent=change_percent,
                        channels_sent=str(successful_channels)
                    )
                    self.storage.store_alert(alert_record)
                except Exception as e:
                    self.logger.error(f"Failed to store alert history: {e}")

        return success
    
    def send_system_alert(
        self,
        alert_type: str,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Send system alert through all enabled channels.
        
        Args:
            alert_type: Type of system alert (error, warning, info)
            message: Alert message
            details: Additional details
            
        Returns:
            True if at least one channel succeeded
        """
        alert_data = {
            'alert_type': 'system',
            'system_alert_type': alert_type,
            'message': message,
            'details': details or {},
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        success = self._send_alert(alert_data)
        
        if success and self.storage:
            try:
                alert_record = AlertHistoryRecord(
                    monitor_pair_name='SYSTEM',
                    alert_type=alert_type,
                    message=message,
                    channels_sent=str(list(self.channels.keys()))
                )
                self.storage.store_alert(alert_record)
            except Exception as e:
                self.logger.error(f"Failed to store system alert history: {e}")
        
        return success
    
    def _send_alert(self, alert_data: Dict[str, Any]) -> bool:
        """Send alert through all enabled channels."""
        if not self.channels:
            self.logger.warning("No alert channels configured")
            return False

        success_count = 0
        failed_channels = []
        successful_channels = []

        for channel_name, channel in self.channels.items():
            if not channel.is_enabled():
                continue

            try:
                if channel.send_alert(alert_data):
                    success_count += 1
                    successful_channels.append(channel_name)
                    self.logger.debug(f"Alert sent successfully via {channel_name}")
                else:
                    failed_channels.append(channel_name)
                    self.logger.warning(f"Alert failed to send via {channel_name}")

            except Exception as e:
                failed_channels.append(channel_name)
                self.logger.error(f"Error sending alert via {channel_name}: {e}")

        # Store successful channels in alert_data for later use
        alert_data['successful_channels'] = successful_channels

        # Update statistics
        if success_count > 0:
            self.stats['alerts_sent'] += 1
            self.stats['last_alert_time'] = datetime.now()
        else:
            self.stats['alerts_failed'] += 1

        for channel_name in failed_channels:
            self.stats['channels_failed'][channel_name] = self.stats['channels_failed'].get(channel_name, 0) + 1

        return success_count > 0
    
    def _is_rate_limited(self, pair_name: str) -> bool:
        """Check if alerts for a pair are rate limited."""
        if pair_name not in self.last_alert_times:
            self.logger.debug(f"No previous alert for {pair_name}, not rate limited")
            return False

        time_since_last = datetime.now() - self.last_alert_times[pair_name]
        seconds_since_last = time_since_last.total_seconds()
        is_limited = seconds_since_last < self.rate_limit_seconds

        self.logger.debug(f"Rate limit check for {pair_name}: {seconds_since_last:.1f}s since last alert, limit: {self.rate_limit_seconds}s, limited: {is_limited}")

        return is_limited
    
    def test_channels(self) -> Dict[str, bool]:
        """Test all configured channels."""
        results = {}
        
        for channel_name, channel in self.channels.items():
            try:
                if hasattr(channel, 'test_connection'):
                    results[channel_name] = channel.test_connection()  # type: ignore
                else:
                    # For channels without test_connection, try sending a test alert
                    test_alert = {
                        'alert_type': 'system',
                        'system_alert_type': 'test',
                        'message': 'This is a test alert from Relay Monitor',
                        'details': {'test': True},
                        'timestamp': time.time()
                    }
                    results[channel_name] = channel.send_alert(test_alert)
                    
            except Exception as e:
                self.logger.error(f"Channel test failed for {channel_name}: {e}")
                results[channel_name] = False
        
        return results
    
    def get_channel_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all channels."""
        status = {}
        
        for channel_name, channel in self.channels.items():
            status[channel_name] = {
                'enabled': channel.is_enabled(),
                'type': channel.__class__.__name__,
                'failures': self.stats['channels_failed'].get(channel_name, 0)
            }
        
        return status
    
    def get_alert_stats(self) -> Dict[str, Any]:
        """Get alert system statistics."""
        stats = self.stats.copy()
        
        # Add rate limiting info
        stats['rate_limit_seconds'] = self.rate_limit_seconds
        stats['active_rate_limits'] = len([
            pair for pair, last_time in self.last_alert_times.items()
            if (datetime.now() - last_time).total_seconds() < self.rate_limit_seconds
        ])
        
        # Add channel info
        stats['channels_configured'] = len(self.channels)
        stats['channels_enabled'] = len([c for c in self.channels.values() if c.is_enabled()])
        
        return stats
    
    def enable_channel(self, channel_name: str) -> bool:
        """Enable a specific channel."""
        if channel_name in self.channels:
            self.channels[channel_name].enable()
            return True
        return False
    
    def disable_channel(self, channel_name: str) -> bool:
        """Disable a specific channel."""
        if channel_name in self.channels:
            self.channels[channel_name].disable()
            return True
        return False
    
    def clear_rate_limits(self):
        """Clear all rate limiting data."""
        self.last_alert_times.clear()
        self.logger.info("Alert rate limits cleared")
    
    def get_recent_alerts(self, hours: int = 24) -> List[AlertHistoryRecord]:
        """Get recent alerts from storage."""
        if not self.storage:
            return []
        
        try:
            return self.storage.get_alert_history(hours=hours)
        except Exception as e:
            self.logger.error(f"Failed to get recent alerts: {e}")
            return []

"""
Alert message templates for different notification channels.
"""

from typing import Dict, Any
from datetime import datetime


class AlertTemplate:
    """
    Generates formatted alert messages for different channels.
    """
    
    @staticmethod
    def format_price_alert_console(
        pair_name: str,
        old_price: float,
        new_price: float,
        change_percent: float,
        analysis: Dict[str, Any]
    ) -> str:
        """Format price alert for console output."""
        direction = "📈" if change_percent > 0 else "📉"
        
        message = f"\n{'='*60}\n"
        message += f"🚨 PRICE ALERT {direction}\n"
        message += f"{'='*60}\n"
        message += f"Pair: {pair_name}\n"
        message += f"Previous Price: {old_price:.6f}\n"
        message += f"Current Price:  {new_price:.6f}\n"
        message += f"Change: {change_percent:+.2f}%\n"
        
        if analysis.get('trend'):
            message += f"Trend: {analysis['trend'].title()}\n"
        
        if analysis.get('volatility'):
            message += f"24h Volatility: {analysis['volatility']:.2f}%\n"
        
        message += f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        message += f"{'='*60}\n"
        
        return message
    
    @staticmethod
    def format_price_alert_email(
        pair_name: str,
        old_price: float,
        new_price: float,
        change_percent: float,
        analysis: Dict[str, Any]
    ) -> Dict[str, str]:
        """Format price alert for email."""
        direction = "Increased" if change_percent > 0 else "Decreased"
        emoji = "📈" if change_percent > 0 else "📉"
        
        subject = f"🚨 Price Alert: {pair_name} {direction} by {abs(change_percent):.2f}%"
        
        html_body = f"""
        <html>
        <body style="font-family: Arial, sans-serif; margin: 20px;">
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff;">
                <h2 style="color: #007bff; margin-top: 0;">{emoji} Price Alert</h2>
                
                <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                    <tr>
                        <td style="padding: 8px; font-weight: bold; border-bottom: 1px solid #dee2e6;">Pair:</td>
                        <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">{pair_name}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; font-weight: bold; border-bottom: 1px solid #dee2e6;">Previous Price:</td>
                        <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">{old_price:.6f}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; font-weight: bold; border-bottom: 1px solid #dee2e6;">Current Price:</td>
                        <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">{new_price:.6f}</td>
                    </tr>
                    <tr style="background-color: {'#d4edda' if change_percent > 0 else '#f8d7da'};">
                        <td style="padding: 8px; font-weight: bold; border-bottom: 1px solid #dee2e6;">Change:</td>
                        <td style="padding: 8px; font-weight: bold; border-bottom: 1px solid #dee2e6; color: {'#155724' if change_percent > 0 else '#721c24'};">
                            {change_percent:+.2f}%
                        </td>
                    </tr>
        """
        
        if analysis.get('trend'):
            html_body += f"""
                    <tr>
                        <td style="padding: 8px; font-weight: bold; border-bottom: 1px solid #dee2e6;">Trend:</td>
                        <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">{analysis['trend'].title()}</td>
                    </tr>
            """
        
        if analysis.get('volatility'):
            html_body += f"""
                    <tr>
                        <td style="padding: 8px; font-weight: bold; border-bottom: 1px solid #dee2e6;">24h Volatility:</td>
                        <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">{analysis['volatility']:.2f}%</td>
                    </tr>
            """
        
        html_body += f"""
                    <tr>
                        <td style="padding: 8px; font-weight: bold;">Alert Time:</td>
                        <td style="padding: 8px;">{datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}</td>
                    </tr>
                </table>
                
                <div style="margin-top: 20px; padding: 15px; background-color: #e9ecef; border-radius: 4px;">
                    <p style="margin: 0; font-size: 14px; color: #6c757d;">
                        This alert was generated by Relay Monitor. 
                        Configure your alert thresholds in the system settings.
                    </p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Plain text version
        text_body = f"""
Price Alert: {pair_name}

Pair: {pair_name}
Previous Price: {old_price:.6f}
Current Price: {new_price:.6f}
Change: {change_percent:+.2f}%
"""
        
        if analysis.get('trend'):
            text_body += f"Trend: {analysis['trend'].title()}\n"
        
        if analysis.get('volatility'):
            text_body += f"24h Volatility: {analysis['volatility']:.2f}%\n"
        
        text_body += f"\nAlert Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}\n"
        text_body += "\nThis alert was generated by Relay Monitor."
        
        return {
            'subject': subject,
            'html_body': html_body,
            'text_body': text_body
        }
    
    @staticmethod
    def format_price_alert_webhook(
        pair_name: str,
        old_price: float,
        new_price: float,
        change_percent: float,
        analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Format price alert for webhook (JSON payload)."""
        return {
            'alert_type': 'price_change',
            'timestamp': datetime.now().isoformat(),
            'pair_name': pair_name,
            'price_data': {
                'previous_price': old_price,
                'current_price': new_price,
                'absolute_change': new_price - old_price,
                'percentage_change': change_percent
            },
            'analysis': analysis,
            'severity': 'high' if abs(change_percent) > 10 else 'medium' if abs(change_percent) > 5 else 'low',
            'message': f"{pair_name} price {'increased' if change_percent > 0 else 'decreased'} by {abs(change_percent):.2f}%"
        }
    
    @staticmethod
    def format_system_alert_console(
        alert_type: str,
        message: str,
        details: Dict[str, Any] = None
    ) -> str:
        """格式化控制台输出的系统警报。"""
        alert_message = f"\n{'='*60}\n"
        alert_message += f"⚠️  SYSTEM ALERT\n"
        alert_message += f"{'='*60}\n"
        alert_message += f"Type: {alert_type.upper()}\n"
        alert_message += f"Message: {message}\n"
        
        if details:
            alert_message += "Details:\n"
            for key, value in details.items():
                alert_message += f"  {key}: {value}\n"
        
        alert_message += f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        alert_message += f"{'='*60}\n"
        
        return alert_message
    
    @staticmethod
    def format_system_alert_email(
        alert_type: str,
        message: str,
        details: Dict[str, Any] = None
    ) -> Dict[str, str]:
        """Format system alert for email."""
        subject = f"⚠️ System Alert: {alert_type.title()}"
        
        html_body = f"""
        <html>
        <body style="font-family: Arial, sans-serif; margin: 20px;">
            <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107;">
                <h2 style="color: #856404; margin-top: 0;">⚠️ System Alert</h2>
                
                <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                    <tr>
                        <td style="padding: 8px; font-weight: bold; border-bottom: 1px solid #dee2e6;">Alert Type:</td>
                        <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">{alert_type.title()}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; font-weight: bold; border-bottom: 1px solid #dee2e6;">Message:</td>
                        <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">{message}</td>
                    </tr>
        """
        
        if details:
            for key, value in details.items():
                html_body += f"""
                    <tr>
                        <td style="padding: 8px; font-weight: bold; border-bottom: 1px solid #dee2e6;">{key.title()}:</td>
                        <td style="padding: 8px; border-bottom: 1px solid #dee2e6;">{value}</td>
                    </tr>
                """
        
        html_body += f"""
                    <tr>
                        <td style="padding: 8px; font-weight: bold;">Alert Time:</td>
                        <td style="padding: 8px;">{datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}</td>
                    </tr>
                </table>
            </div>
        </body>
        </html>
        """
        
        text_body = f"""
System Alert: {alert_type.title()}

Message: {message}
"""
        
        if details:
            text_body += "\nDetails:\n"
            for key, value in details.items():
                text_body += f"{key.title()}: {value}\n"
        
        text_body += f"\nAlert Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}"
        
        return {
            'subject': subject,
            'html_body': html_body,
            'text_body': text_body
        }
    
    @staticmethod
    def format_system_alert_webhook(
        alert_type: str,
        message: str,
        details: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Format system alert for webhook."""
        return {
            'alert_type': 'system',
            'system_alert_type': alert_type,
            'timestamp': datetime.now().isoformat(),
            'message': message,
            'details': details or {},
            'severity': 'high' if alert_type in ['error', 'critical'] else 'medium'
        }

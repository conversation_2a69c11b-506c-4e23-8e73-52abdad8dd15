"""
Relay Monitor 的数据库操作。

提供使用 SQLite 存储和检索监控数据的高级接口，
具有自动模式管理和迁移功能。
"""

import os
import json
import sqlite3
import logging
import threading
import time
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta, timezone
from contextlib import contextmanager
from queue import Queue, Empty

from .models import (
    ChainRecord, TokenRecord, PriceHistoryRecord,
    MonitorConfigRecord, AlertHistoryRecord,
    RuntimeConfigRecord
)

# Import for type annotations
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from ..config.models import MonitorPair


logger = logging.getLogger(__name__)


class SQLiteConnectionPool:
    """SQLite连接池，用于管理数据库连接的复用。"""

    def __init__(self, db_path: str, max_connections: int = 10, timeout: float = 30.0):
        """
        初始化连接池。

        Args:
            db_path: 数据库文件路径
            max_connections: 最大连接数
            timeout: 获取连接的超时时间（秒）
        """
        self.db_path = db_path
        self.max_connections = max_connections
        self.timeout = timeout
        self._pool = Queue(maxsize=max_connections)
        self._created_connections = 0
        self._lock = threading.Lock()
        self._closed = False

        # 预创建一些连接
        self._initialize_pool()

    def _initialize_pool(self):
        """预创建一些连接到池中。"""
        initial_connections = min(3, self.max_connections)  # 预创建3个连接
        for _ in range(initial_connections):
            conn = self._create_connection()
            if conn:
                self._pool.put(conn)

    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """创建新的数据库连接。"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                check_same_thread=False,  # 允许多线程使用
                timeout=20.0  # 数据库锁超时
            )
            conn.row_factory = sqlite3.Row
            # 设置一些性能优化选项
            conn.execute("PRAGMA journal_mode=WAL")  # 使用WAL模式提高并发性能
            conn.execute("PRAGMA synchronous=NORMAL")  # 平衡性能和安全性
            conn.execute("PRAGMA cache_size=10000")  # 增加缓存大小
            conn.execute("PRAGMA temp_store=MEMORY")  # 临时表存储在内存中

            with self._lock:
                self._created_connections += 1

            logger.debug(f"Created new database connection (total: {self._created_connections})")
            return conn
        except Exception as e:
            logger.error(f"Failed to create database connection: {e}")
            return None

    def get_connection(self) -> Optional[sqlite3.Connection]:
        """从池中获取连接。"""
        if self._closed:
            raise RuntimeError("Connection pool is closed")

        try:
            # 尝试从池中获取现有连接
            conn = self._pool.get(timeout=self.timeout)

            # 检查连接是否仍然有效
            try:
                conn.execute("SELECT 1")
                return conn
            except sqlite3.Error:
                # 连接已失效，创建新连接
                logger.debug("Connection invalid, creating new one")
                conn.close()
                return self._create_connection()

        except Empty:
            # 池中没有可用连接，尝试创建新连接
            with self._lock:
                if self._created_connections < self.max_connections:
                    return self._create_connection()

            # 达到最大连接数，等待可用连接
            logger.warning(f"Connection pool exhausted, waiting for available connection")
            try:
                return self._pool.get(timeout=self.timeout)
            except Empty:
                raise RuntimeError(f"Failed to get database connection within {self.timeout} seconds")

    def return_connection(self, conn: sqlite3.Connection):
        """将连接返回到池中。"""
        if self._closed:
            conn.close()
            return

        try:
            # 检查连接是否仍然有效
            conn.execute("SELECT 1")
            self._pool.put_nowait(conn)
        except (sqlite3.Error, Exception):
            # 连接有问题，关闭它
            conn.close()
            with self._lock:
                self._created_connections -= 1

    def close(self):
        """关闭连接池和所有连接。"""
        self._closed = True

        # 关闭池中的所有连接
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                conn.close()
            except Empty:
                break

        logger.info(f"Connection pool closed, {self._created_connections} connections were active")

    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息。"""
        return {
            'max_connections': self.max_connections,
            'created_connections': self._created_connections,
            'available_connections': self._pool.qsize(),
            'active_connections': self._created_connections - self._pool.qsize(),
            'pool_closed': self._closed
        }


class DataStorage:
    """
    Relay Monitor 的高级数据库接口。

    功能特性：
    - 自动模式创建和迁移
    - 事务支持
    - 连接池
    - 数据验证
    - 备份和清理操作
    """
    
    def __init__(self, db_path: str = "data/relay_monitor.db", max_connections: int = 10):
        """
        初始化数据库存储。

        Args:
            db_path: SQLite 数据库文件路径
            max_connections: 最大数据库连接数
        """
        self.db_path = db_path
        self.schema_version = 2  # Updated to include runtime_config table

        # 确保数据库目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        # 初始化连接池
        self._connection_pool = SQLiteConnectionPool(db_path, max_connections)

        # 初始化数据库
        self._init_database()
    
    def _init_database(self):
        """Initialize database schema."""
        logger.info(f"Initializing database at {self.db_path}")
        
        with self.get_connection() as conn:
            # Create schema version table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS schema_version (
                    version INTEGER PRIMARY KEY,
                    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Check current schema version
            cursor = conn.execute("SELECT MAX(version) FROM schema_version")
            current_version = cursor.fetchone()[0] or 0
            
            if current_version < self.schema_version:
                self._create_tables(conn)
                conn.execute(
                    "INSERT INTO schema_version (version) VALUES (?)",
                    (self.schema_version,)
                )
                logger.info(f"Database schema updated to version {self.schema_version}")
    
    def _create_tables(self, conn: sqlite3.Connection):
        """创建所有数据库表。"""
        
        # 区块链表
        conn.execute("""
            CREATE TABLE IF NOT EXISTS chains (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                chain_id INTEGER UNIQUE NOT NULL,
                name TEXT NOT NULL,
                display_name TEXT NOT NULL,
                http_rpc_url TEXT NOT NULL,
                explorer_url TEXT NOT NULL,
                native_currency_symbol TEXT NOT NULL,
                native_currency_address TEXT NOT NULL,
                native_currency_decimals INTEGER NOT NULL,
                deposit_enabled BOOLEAN NOT NULL,
                disabled BOOLEAN NOT NULL DEFAULT 0,
                icon_url TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Tokens table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                chain_id INTEGER NOT NULL,
                symbol TEXT NOT NULL,
                name TEXT NOT NULL,
                address TEXT NOT NULL,
                decimals INTEGER NOT NULL,
                supports_bridging BOOLEAN NOT NULL DEFAULT 1,
                is_native BOOLEAN NOT NULL DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(chain_id, address),
                FOREIGN KEY (chain_id) REFERENCES chains (chain_id)
            )
        """)
        
        # Price history table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS price_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                monitor_pair_name TEXT NOT NULL,
                origin_chain_id INTEGER NOT NULL,
                destination_chain_id INTEGER NOT NULL,
                origin_token_symbol TEXT NOT NULL,
                destination_token_symbol TEXT NOT NULL,
                origin_amount TEXT NOT NULL,
                exchange_rate REAL NOT NULL,
                total_fee_usd REAL NOT NULL,
                time_estimate_seconds INTEGER NOT NULL,
                quote_data TEXT NOT NULL,  -- JSON
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (origin_chain_id) REFERENCES chains (chain_id),
                FOREIGN KEY (destination_chain_id) REFERENCES chains (chain_id)
            )
        """)
        
        # Monitor configurations table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS monitor_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT NOT NULL,
                origin_chain TEXT NOT NULL,
                destination_chain TEXT NOT NULL,
                origin_token TEXT NOT NULL,
                destination_token TEXT NOT NULL,
                amount TEXT NOT NULL,
                enabled BOOLEAN NOT NULL DEFAULT 1,
                alert_threshold_percent REAL NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_checked TIMESTAMP
            )
        """)
        
        # Alert history table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS alert_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                monitor_pair_name TEXT NOT NULL,
                alert_type TEXT NOT NULL,
                message TEXT NOT NULL,
                old_rate REAL,
                new_rate REAL,
                change_percent REAL,
                channels_sent TEXT NOT NULL,  -- JSON array
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # System statistics table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS system_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                metric_name TEXT NOT NULL,
                metric_value REAL NOT NULL,
                metadata TEXT,  -- JSON
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Wallets table for crypto functionality
        conn.execute("""
            CREATE TABLE IF NOT EXISTS wallets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                address TEXT NOT NULL,
                encrypted_private_key TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Trading configs table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS trading_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                monitor_pair_name TEXT UNIQUE NOT NULL,
                wallet_name TEXT NOT NULL,
                enabled BOOLEAN DEFAULT 1,
                trading_amount TEXT NOT NULL,
                max_slippage_percent REAL DEFAULT 5.0,
                daily_limit_usd REAL,
                single_trade_limit_usd REAL,
                min_price_change_percent REAL DEFAULT 5.0,
                cooldown_minutes INTEGER DEFAULT 60,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Transaction history table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS transaction_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                monitor_pair_name TEXT NOT NULL,
                wallet_name TEXT NOT NULL,
                wallet_address TEXT NOT NULL,
                tx_hash TEXT NOT NULL,
                request_id TEXT,
                status TEXT NOT NULL,
                amount_in TEXT NOT NULL,
                token_in_symbol TEXT NOT NULL,
                amount_out TEXT NOT NULL,
                token_out_symbol TEXT NOT NULL,
                gas_used INTEGER,
                gas_price TEXT,
                total_fee_usd REAL,
                trigger_price REAL NOT NULL,
                execution_price REAL,
                slippage_percent REAL,
                error_message TEXT,
                retry_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                submitted_at TIMESTAMP,
                confirmed_at TIMESTAMP,
                quote_data TEXT,
                transaction_data TEXT
            )
        """)

        # Runtime configuration table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS runtime_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_key TEXT UNIQUE NOT NULL,
                config_value TEXT,
                config_type TEXT NOT NULL DEFAULT 'string',
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create indexes for better performance
        conn.execute("CREATE INDEX IF NOT EXISTS idx_price_history_pair ON price_history (monitor_pair_name)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_price_history_timestamp ON price_history (timestamp)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_alert_history_pair ON alert_history (monitor_pair_name)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_alert_history_timestamp ON alert_history (timestamp)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_tokens_chain_symbol ON tokens (chain_id, symbol)")
        
        logger.info("Database tables created successfully")
    
    @contextmanager
    def get_connection(self):
        """Get database connection from pool with automatic cleanup."""
        conn = self._connection_pool.get_connection()
        if conn is None:
            raise RuntimeError("Failed to get database connection from pool")

        try:
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
        finally:
            # 返回连接到池中而不是关闭
            self._connection_pool.return_connection(conn)

    def close(self):
        """关闭数据库连接池。"""
        if hasattr(self, '_connection_pool'):
            self._connection_pool.close()
            logger.info("Database connection pool closed")

    def get_connection_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息。"""
        if hasattr(self, '_connection_pool'):
            return self._connection_pool.get_stats()
        return {}

    def __del__(self):
        """析构函数，确保连接池被正确关闭。"""
        try:
            self.close()
        except Exception:
            pass  # 忽略析构时的错误

    def store_chain(self, chain_record: ChainRecord) -> int:
        """存储或更新区块链信息。"""
        with self.get_connection() as conn:
            # Try to update existing record first
            cursor = conn.execute("""
                UPDATE chains SET
                    name = ?, display_name = ?, http_rpc_url = ?, explorer_url = ?,
                    native_currency_symbol = ?, native_currency_address = ?,
                    native_currency_decimals = ?, deposit_enabled = ?, disabled = ?,
                    icon_url = ?, updated_at = CURRENT_TIMESTAMP
                WHERE chain_id = ?
            """, (
                chain_record.name, chain_record.display_name, chain_record.http_rpc_url,
                chain_record.explorer_url, chain_record.native_currency_symbol,
                chain_record.native_currency_address, chain_record.native_currency_decimals,
                chain_record.deposit_enabled, chain_record.disabled, chain_record.icon_url,
                chain_record.chain_id
            ))
            
            if cursor.rowcount == 0:
                # Insert new record
                cursor = conn.execute("""
                    INSERT INTO chains (
                        chain_id, name, display_name, http_rpc_url, explorer_url,
                        native_currency_symbol, native_currency_address, native_currency_decimals,
                        deposit_enabled, disabled, icon_url
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    chain_record.chain_id, chain_record.name, chain_record.display_name,
                    chain_record.http_rpc_url, chain_record.explorer_url,
                    chain_record.native_currency_symbol, chain_record.native_currency_address,
                    chain_record.native_currency_decimals, chain_record.deposit_enabled,
                    chain_record.disabled, chain_record.icon_url
                ))
            
            return cursor.lastrowid or 0

    def store_token(self, token_record: TokenRecord) -> int:
        """Store or update token information."""
        with self.get_connection() as conn:
            # Try to update existing record first
            cursor = conn.execute("""
                UPDATE tokens SET
                    symbol = ?, name = ?, decimals = ?, supports_bridging = ?,
                    is_native = ?, updated_at = CURRENT_TIMESTAMP
                WHERE chain_id = ? AND address = ?
            """, (
                token_record.symbol, token_record.name, token_record.decimals,
                token_record.supports_bridging, token_record.is_native,
                token_record.chain_id, token_record.address
            ))

            if cursor.rowcount == 0:
                # Insert new record
                cursor = conn.execute("""
                    INSERT INTO tokens (
                        chain_id, symbol, name, address, decimals,
                        supports_bridging, is_native
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    token_record.chain_id, token_record.symbol, token_record.name,
                    token_record.address, token_record.decimals,
                    token_record.supports_bridging, token_record.is_native
                ))

            return cursor.lastrowid or 0

    def store_price_history(self, price_record: PriceHistoryRecord) -> int:
        """Store price history record."""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                INSERT INTO price_history (
                    monitor_pair_name, origin_chain_id, destination_chain_id,
                    origin_token_symbol, destination_token_symbol, origin_amount,
                    exchange_rate, total_fee_usd, time_estimate_seconds, quote_data
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                price_record.monitor_pair_name, price_record.origin_chain_id,
                price_record.destination_chain_id, price_record.origin_token_symbol,
                price_record.destination_token_symbol, price_record.origin_amount,
                price_record.exchange_rate, price_record.total_fee_usd,
                price_record.time_estimate_seconds, json.dumps(price_record.quote_data)
            ))

            return cursor.lastrowid or 0

    def get_price_history(
        self,
        monitor_pair_name: str,
        hours: int = 24,
        limit: Optional[int] = None
    ) -> List[PriceHistoryRecord]:
        """Get price history for a monitor pair."""
        with self.get_connection() as conn:
            query = """
                SELECT * FROM price_history
                WHERE monitor_pair_name = ? AND timestamp >= datetime('now', '-{} hours')
                ORDER BY timestamp DESC
            """.format(hours)

            if limit:
                query += f" LIMIT {limit}"

            cursor = conn.execute(query, (monitor_pair_name,))
            records = []

            for row in cursor.fetchall():
                record = PriceHistoryRecord(
                    id=row['id'],
                    monitor_pair_name=row['monitor_pair_name'],
                    origin_chain_id=row['origin_chain_id'],
                    destination_chain_id=row['destination_chain_id'],
                    origin_token_symbol=row['origin_token_symbol'],
                    destination_token_symbol=row['destination_token_symbol'],
                    origin_amount=row['origin_amount'],
                    exchange_rate=row['exchange_rate'],
                    total_fee_usd=row['total_fee_usd'],
                    time_estimate_seconds=row['time_estimate_seconds'],
                    quote_data=json.loads(row['quote_data']),
                    timestamp=datetime.fromisoformat(row['timestamp'])
                )
                records.append(record)

            return records

    def get_latest_price(self, monitor_pair_name: str) -> Optional[PriceHistoryRecord]:
        """Get the latest price record for a monitor pair."""
        history = self.get_price_history(monitor_pair_name, hours=24, limit=1)
        return history[0] if history else None

    def store_alert(self, alert_record: AlertHistoryRecord) -> int:
        """Store alert history record."""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                INSERT INTO alert_history (
                    monitor_pair_name, alert_type, message, old_rate, new_rate,
                    change_percent, channels_sent
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                alert_record.monitor_pair_name, alert_record.alert_type,
                alert_record.message, alert_record.old_rate, alert_record.new_rate,
                alert_record.change_percent, alert_record.channels_sent
            ))

            return cursor.lastrowid or 0

    def get_alert_history(
        self,
        monitor_pair_name: Optional[str] = None,
        hours: int = 24,
        limit: Optional[int] = None
    ) -> List[AlertHistoryRecord]:
        """Get alert history."""
        with self.get_connection() as conn:
            if monitor_pair_name:
                query = """
                    SELECT * FROM alert_history
                    WHERE monitor_pair_name = ? AND timestamp >= datetime('now', '-{} hours')
                    ORDER BY timestamp DESC
                """.format(hours)
                params = (monitor_pair_name,)
            else:
                query = """
                    SELECT * FROM alert_history
                    WHERE timestamp >= datetime('now', '-{} hours')
                    ORDER BY timestamp DESC
                """.format(hours)
                params = ()

            if limit:
                query += f" LIMIT {limit}"

            cursor = conn.execute(query, params)
            records = []

            for row in cursor.fetchall():
                record = AlertHistoryRecord(
                    id=row['id'],
                    monitor_pair_name=row['monitor_pair_name'],
                    alert_type=row['alert_type'],
                    message=row['message'],
                    old_rate=row['old_rate'],
                    new_rate=row['new_rate'],
                    change_percent=row['change_percent'],
                    channels_sent=row['channels_sent'],
                    timestamp=datetime.fromisoformat(row['timestamp'])
                )
                records.append(record)

            return records

    def cleanup_old_data(self, days: int = 30):
        """Clean up old data beyond the specified number of days."""
        with self.get_connection() as conn:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)

            # Clean up old price history
            cursor = conn.execute("""
                DELETE FROM price_history
                WHERE timestamp < ?
            """, (cutoff_date,))
            price_deleted = cursor.rowcount

            # Clean up old alert history
            cursor = conn.execute("""
                DELETE FROM alert_history
                WHERE timestamp < ?
            """, (cutoff_date,))
            alert_deleted = cursor.rowcount

            # Clean up old system stats
            cursor = conn.execute("""
                DELETE FROM system_stats
                WHERE timestamp < ?
            """, (cutoff_date,))
            stats_deleted = cursor.rowcount

            logger.info(f"Cleaned up old data: {price_deleted} price records, "
                       f"{alert_deleted} alert records, {stats_deleted} stats records")

    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        with self.get_connection() as conn:
            stats = {}

            # Table row counts
            for table in ['chains', 'tokens', 'price_history', 'alert_history', 'system_stats', 'runtime_config']:
                cursor = conn.execute(f"SELECT COUNT(*) FROM {table}")
                stats[f"{table}_count"] = cursor.fetchone()[0]

            # Database file size
            stats['db_size_bytes'] = os.path.getsize(self.db_path)
            stats['db_size_mb'] = round(stats['db_size_bytes'] / (1024 * 1024), 2)

            # Latest records
            cursor = conn.execute("SELECT MAX(timestamp) FROM price_history")
            latest_price = cursor.fetchone()[0]
            stats['latest_price_timestamp'] = latest_price

            cursor = conn.execute("SELECT MAX(timestamp) FROM alert_history")
            latest_alert = cursor.fetchone()[0]
            stats['latest_alert_timestamp'] = latest_alert

            return stats

    def backup_database(self, backup_path: Optional[str] = None) -> str:
        """Create a backup of the database."""
        if backup_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{self.db_path}.backup_{timestamp}"

        # Ensure backup directory exists
        os.makedirs(os.path.dirname(backup_path), exist_ok=True)

        # Create backup using SQLite backup API
        with sqlite3.connect(self.db_path) as source:
            with sqlite3.connect(backup_path) as backup:
                source.backup(backup)

        logger.info(f"Database backed up to {backup_path}")
        return backup_path

    def get_price_statistics(self, monitor_pair_name: str, hours: int = 24) -> Dict[str, float]:
        """Get price statistics for a monitor pair."""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                SELECT
                    MIN(exchange_rate) as min_rate,
                    MAX(exchange_rate) as max_rate,
                    AVG(exchange_rate) as avg_rate,
                    MIN(total_fee_usd) as min_fee,
                    MAX(total_fee_usd) as max_fee,
                    AVG(total_fee_usd) as avg_fee,
                    COUNT(*) as sample_count
                FROM price_history
                WHERE monitor_pair_name = ? AND timestamp >= datetime('now', '-{} hours')
            """.format(hours), (monitor_pair_name,))

            row = cursor.fetchone()
            if row and row['sample_count'] > 0:
                return {
                    'min_rate': row['min_rate'],
                    'max_rate': row['max_rate'],
                    'avg_rate': row['avg_rate'],
                    'min_fee': row['min_fee'],
                    'max_fee': row['max_fee'],
                    'avg_fee': row['avg_fee'],
                    'sample_count': row['sample_count'],
                    'rate_volatility': (row['max_rate'] - row['min_rate']) / row['avg_rate'] * 100 if row['avg_rate'] > 0 else 0
                }
            else:
                return {
                    'min_rate': 0, 'max_rate': 0, 'avg_rate': 0,
                    'min_fee': 0, 'max_fee': 0, 'avg_fee': 0,
                    'sample_count': 0, 'rate_volatility': 0
                }

    def get_monitor_pairs_summary(self) -> List[Dict[str, Any]]:
        """Get summary of all monitor pairs with latest data."""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                SELECT
                    monitor_pair_name,
                    COUNT(*) as total_records,
                    MAX(timestamp) as latest_timestamp,
                    AVG(exchange_rate) as avg_rate_24h,
                    AVG(total_fee_usd) as avg_fee_24h
                FROM price_history
                WHERE timestamp >= datetime('now', '-24 hours')
                GROUP BY monitor_pair_name
                ORDER BY latest_timestamp DESC
            """)

            summaries = []
            for row in cursor.fetchall():
                # Convert timestamp string to datetime object
                latest_timestamp = None
                if row['latest_timestamp']:
                    try:
                        latest_timestamp = datetime.fromisoformat(row['latest_timestamp'].replace('Z', '+00:00'))
                    except:
                        latest_timestamp = None

                summaries.append({
                    'pair_name': row['monitor_pair_name'],
                    'total_records': row['total_records'],
                    'latest_timestamp': latest_timestamp,
                    'avg_rate_24h': row['avg_rate_24h'],
                    'avg_fee_24h': row['avg_fee_24h']
                })

            return summaries

    # =============================================================================
    # Monitor Configuration Operations
    # =============================================================================

    def store_monitor_config(self, config: MonitorConfigRecord) -> bool:
        """Store or update monitor configuration."""
        with self.get_connection() as conn:
            # Try to update existing record first
            cursor = conn.execute("""
                UPDATE monitor_configs SET
                    description = ?, origin_chain = ?, destination_chain = ?,
                    origin_token = ?, destination_token = ?, amount = ?,
                    enabled = ?, alert_threshold_percent = ?, updated_at = CURRENT_TIMESTAMP
                WHERE name = ?
            """, (
                config.description, config.origin_chain, config.destination_chain,
                config.origin_token, config.destination_token, config.amount,
                config.enabled, config.alert_threshold_percent, config.name
            ))

            if cursor.rowcount == 0:
                # Insert new record
                cursor = conn.execute("""
                    INSERT INTO monitor_configs (
                        name, description, origin_chain, destination_chain,
                        origin_token, destination_token, amount, enabled, alert_threshold_percent
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    config.name, config.description, config.origin_chain, config.destination_chain,
                    config.origin_token, config.destination_token, config.amount,
                    config.enabled, config.alert_threshold_percent
                ))
                return cursor.lastrowid is not None
            else:
                return True  # Update successful

    def get_monitor_config(self, name: str) -> Optional[MonitorConfigRecord]:
        """Get monitor configuration by name."""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                SELECT * FROM monitor_configs WHERE name = ?
            """, (name,))

            row = cursor.fetchone()
            if row:
                return MonitorConfigRecord(**dict(row))
            return None

    def get_all_monitor_configs(self) -> List[MonitorConfigRecord]:
        """Get all monitor configurations."""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                SELECT * FROM monitor_configs ORDER BY name
            """)

            return [MonitorConfigRecord(**dict(row)) for row in cursor.fetchall()]

    def get_enabled_monitor_pairs(self) -> List['MonitorPair']:
        """Get enabled monitor configurations as MonitorPair objects."""
        from ..config.models import MonitorPair

        with self.get_connection() as conn:
            cursor = conn.execute("""
                SELECT * FROM monitor_configs WHERE enabled = 1 ORDER BY name
            """)

            pairs = []
            for row in cursor.fetchall():
                config = MonitorConfigRecord(**dict(row))
                row_dict = dict(row)

                # Convert MonitorConfigRecord to MonitorPair
                pair = MonitorPair(
                    name=config.name,
                    description=config.description,
                    origin_chain=config.origin_chain,
                    destination_chain=config.destination_chain,
                    origin_token=config.origin_token,
                    destination_token=config.destination_token,
                    amount=config.amount,
                    enabled=config.enabled,
                    alert_threshold_percent=config.alert_threshold_percent,
                    # 添加交易配置字段
                    auto_trading_enabled=row_dict.get('auto_trading_enabled', False),
                    wallet_name=row_dict.get('wallet_name'),
                    trading_amount=row_dict.get('trading_amount'),
                    max_slippage_percent=row_dict.get('max_slippage_percent', 5.0),
                    min_price_change_percent=row_dict.get('min_price_change_percent', 5.0),
                    cooldown_minutes=row_dict.get('cooldown_minutes', 60),
                    daily_limit_usd=row_dict.get('daily_limit_usd'),
                    single_trade_limit_usd=row_dict.get('single_trade_limit_usd')
                )
                pairs.append(pair)

            return pairs

    def delete_monitor_config(self, name: str) -> bool:
        """Delete monitor configuration by name."""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                DELETE FROM monitor_configs WHERE name = ?
            """, (name,))

            return cursor.rowcount > 0

    # =============================================================================
    # Runtime Configuration Operations
    # =============================================================================

    def store_runtime_config(self, config_record: RuntimeConfigRecord) -> int:
        """Store or update runtime configuration."""
        with self.get_connection() as conn:
            # Try to update existing record first
            cursor = conn.execute("""
                UPDATE runtime_config SET
                    config_value = ?, config_type = ?, description = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE config_key = ?
            """, (
                config_record.config_value, config_record.config_type,
                config_record.description, config_record.config_key
            ))

            if cursor.rowcount == 0:
                # Insert new record
                cursor = conn.execute("""
                    INSERT INTO runtime_config (
                        config_key, config_value, config_type, description
                    ) VALUES (?, ?, ?, ?)
                """, (
                    config_record.config_key, config_record.config_value,
                    config_record.config_type, config_record.description
                ))

            return cursor.lastrowid or 0

    def get_runtime_config(self, config_key: str) -> Optional[RuntimeConfigRecord]:
        """Get runtime configuration by key."""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                SELECT * FROM runtime_config WHERE config_key = ?
            """, (config_key,))

            row = cursor.fetchone()
            if row:
                return RuntimeConfigRecord(**dict(row))
            return None

    def get_all_runtime_configs(self) -> List[RuntimeConfigRecord]:
        """Get all runtime configurations."""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                SELECT * FROM runtime_config ORDER BY config_key
            """)

            return [RuntimeConfigRecord(**dict(row)) for row in cursor.fetchall()]

    def delete_runtime_config(self, config_key: str) -> bool:
        """Delete runtime configuration by key."""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                DELETE FROM runtime_config WHERE config_key = ?
            """, (config_key,))

            return cursor.rowcount > 0

    def get_runtime_config_value(self, config_key: str, default_value: Any = None) -> Any:
        """Get runtime configuration value with type conversion."""
        config = self.get_runtime_config(config_key)
        if not config or config.config_value is None:
            return default_value

        # Convert based on type
        try:
            if config.config_type == 'boolean':
                return config.config_value.lower() in ('true', '1', 'yes', 'on')
            elif config.config_type == 'number':
                return float(config.config_value)
            elif config.config_type == 'json':
                import json
                return json.loads(config.config_value)
            else:  # string
                return config.config_value
        except (ValueError, json.JSONDecodeError):
            logger.warning(f"Failed to convert config value for {config_key}, returning default")
            return default_value

    def set_runtime_config_value(self, config_key: str, value: Any, config_type: str = 'string', description: Optional[str] = None) -> bool:
        """Set runtime configuration value with type conversion."""
        try:
            # Convert value to string based on type
            if config_type == 'boolean':
                config_value = 'true' if value else 'false'
            elif config_type == 'number':
                config_value = str(float(value))
            elif config_type == 'json':
                import json
                config_value = json.dumps(value)
            else:  # string
                config_value = str(value) if value is not None else None

            config_record = RuntimeConfigRecord(
                config_key=config_key,
                config_value=config_value,
                config_type=config_type,
                description=description
            )

            self.store_runtime_config(config_record)
            return True
        except Exception as e:
            logger.error(f"Failed to set runtime config {config_key}: {e}")
            return False

    def update_monitor_config_trading(self, pair_name: str, trading_fields: Dict[str, Any]) -> bool:
        """更新监控对的自动交易配置。"""
        try:
            with self.get_connection() as conn:
                # 首先检查 monitor_configs 表是否有需要添加的列
                cursor = conn.execute("PRAGMA table_info(monitor_configs)")
                columns = [row[1] for row in cursor.fetchall()]

                # 需要添加的交易相关列
                trading_columns = {
                    'auto_trading_enabled': 'BOOLEAN DEFAULT 0',
                    'wallet_name': 'TEXT',
                    'trading_amount': 'TEXT',
                    'max_slippage_percent': 'REAL DEFAULT 5.0',
                    'min_price_change_percent': 'REAL DEFAULT 5.0',
                    'cooldown_minutes': 'INTEGER DEFAULT 60',
                    'daily_limit_usd': 'REAL',
                    'single_trade_limit_usd': 'REAL'
                }

                # 添加缺失的列
                for column_name, column_def in trading_columns.items():
                    if column_name not in columns:
                        try:
                            conn.execute(f"ALTER TABLE monitor_configs ADD COLUMN {column_name} {column_def}")
                            logger.info(f"Added column {column_name} to monitor_configs table")
                        except sqlite3.OperationalError as e:
                            if "duplicate column name" not in str(e).lower():
                                logger.error(f"Failed to add column {column_name}: {e}")

                # 构建更新语句
                set_clauses = []
                values = []

                for field, value in trading_fields.items():
                    if field in trading_columns:  # 只更新有效的交易配置字段
                        set_clauses.append(f"{field} = ?")
                        values.append(value)

                if not set_clauses:
                    return True  # 没有字段需要更新

                # 添加更新时间
                set_clauses.append("updated_at = CURRENT_TIMESTAMP")

                # 执行更新
                query = f"""
                    UPDATE monitor_configs
                    SET {', '.join(set_clauses)}
                    WHERE name = ?
                """
                values.append(pair_name)

                cursor = conn.execute(query, values)
                conn.commit()

                if cursor.rowcount > 0:
                    logger.info(f"Updated trading config for monitor pair: {pair_name}")
                    return True
                else:
                    logger.warning(f"No monitor pair found with name: {pair_name}")
                    return False

        except Exception as e:
            logger.error(f"Error updating trading config: {e}")
            return False

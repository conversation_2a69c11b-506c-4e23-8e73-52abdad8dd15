"""
用于存储监控数据的数据库模型。
"""

from typing import Optional, Dict, Any
from datetime import datetime, timezone
from pydantic import BaseModel, Field


class ChainRecord(BaseModel):
    """区块链信息的数据库记录。"""
    
    id: Optional[int] = None
    chain_id: int
    name: str
    display_name: str
    http_rpc_url: str
    explorer_url: str
    native_currency_symbol: str
    native_currency_address: str
    native_currency_decimals: int
    deposit_enabled: bool
    disabled: bool
    icon_url: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class TokenRecord(BaseModel):
    """代币信息的数据库记录。"""
    
    id: Optional[int] = None
    chain_id: int
    symbol: str
    name: str
    address: str
    decimals: int
    supports_bridging: bool = True
    is_native: bool = False
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class PriceHistoryRecord(BaseModel):
    """价格历史的数据库记录。"""
    
    id: Optional[int] = None
    monitor_pair_name: str
    origin_chain_id: int
    destination_chain_id: int
    origin_token_symbol: str
    destination_token_symbol: str
    origin_amount: str
    exchange_rate: float
    total_fee_usd: float
    time_estimate_seconds: float
    quote_data: Dict[str, Any]  # Full quote response as JSON
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class MonitorConfigRecord(BaseModel):
    """Database record for monitor configuration."""

    id: Optional[int] = None
    name: str
    description: str
    origin_chain: str
    destination_chain: str
    origin_token: str
    destination_token: str
    amount: str
    enabled: bool
    alert_threshold_percent: float
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    last_checked: Optional[datetime] = None

    # 自动交易相关字段
    auto_trading_enabled: Optional[bool] = False
    wallet_name: Optional[str] = None
    trading_amount: Optional[float] = None
    max_slippage_percent: Optional[float] = 5.0
    min_price_change_percent: Optional[float] = 5.0
    cooldown_minutes: Optional[int] = 60
    daily_limit_usd: Optional[float] = None
    single_trade_limit_usd: Optional[float] = None


class AlertHistoryRecord(BaseModel):
    """警报历史的数据库记录。"""
    
    id: Optional[int] = None
    monitor_pair_name: str
    alert_type: str  # 'price_change', 'error', 'system'
    message: str
    old_rate: Optional[float] = None
    new_rate: Optional[float] = None
    change_percent: Optional[float] = None
    channels_sent: str  # JSON array of channels
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class SystemStatsRecord(BaseModel):
    """Database record for system statistics."""

    id: Optional[int] = None
    metric_name: str
    metric_value: float
    metadata: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class RuntimeConfigRecord(BaseModel):
    """Database record for runtime configuration."""

    id: Optional[int] = None
    config_key: str
    config_value: Optional[str] = None
    config_type: str = "string"  # 'string', 'json', 'boolean', 'number'
    description: Optional[str] = None
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

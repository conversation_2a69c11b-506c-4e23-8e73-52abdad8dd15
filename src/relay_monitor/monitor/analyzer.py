"""
用于检测显著变化和趋势的价格分析工具。
"""

import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta

from ..storage.models import PriceHistoryRecord


logger = logging.getLogger(__name__)


class PriceAnalyzer:
    """
    分析价格数据以检测显著变化和趋势。

    功能特性：
    - 价格变化检测（百分比和绝对值）
    - 趋势分析
    - 波动率计算
    - 异常检测
    """
    
    def __init__(self):
        self.logger = logger
    
    def calculate_price_change(
        self,
        old_price: float,
        new_price: float
    ) -> Tuple[float, float]:
        """
        Calculate price change in absolute and percentage terms.

        使用公式：|new_price / old_price - 1| * 100 来计算百分比变化
        这个公式更适合汇率变化的计算

        Args:
            old_price: Previous price (currency_out_usd / currency_in_usd)
            new_price: Current price (currency_out_usd / currency_in_usd)

        Returns:
            Tuple of (absolute_change, percentage_change)
        """
        if old_price == 0:
            return new_price, float('inf') if new_price > 0 else 0

        # 绝对变化
        absolute_change = new_price - old_price

        # 百分比变化：(new_price / old_price - 1) * 100
        ratio = new_price / old_price
        percentage_change = (ratio - 1) * 100

        return absolute_change, percentage_change
    
    def is_significant_change(
        self,
        old_price: float,
        new_price: float,
        threshold_percent: float = 5.0,
        threshold_absolute: Optional[float] = None
    ) -> bool:
        """
        基于阈值检查价格变化是否显著。

        使用公式：|currency_out_usd / currency_in_usd - 1| * 100 >= threshold_percent

        Args:
            old_price: 之前的价格 (currency_out_usd / currency_in_usd)
            new_price: 当前价格 (currency_out_usd / currency_in_usd)
            threshold_percent: 百分比变化阈值
            threshold_absolute: 绝对变化阈值（可选）

        Returns:
            如果变化显著返回 True
        """
        abs_change, pct_change = self.calculate_price_change(old_price, new_price)

        # Check percentage threshold using absolute value
        # |new_price / old_price - 1| * 100 >= threshold_percent
        if abs(pct_change) >= threshold_percent:
            return True

        # Check absolute threshold if provided
        if threshold_absolute and abs(abs_change) >= threshold_absolute:
            return True

        return False
    
    def analyze_price_history(
        self,
        history: List[PriceHistoryRecord],
        hours: int = 24
    ) -> Dict[str, Any]:
        """
        Analyze price history to extract trends and statistics.
        
        Args:
            history: List of price history records (should be sorted by timestamp desc)
            hours: Number of hours to analyze
            
        Returns:
            Dictionary with analysis results
        """
        if not history:
            return {
                'trend': 'no_data',
                'volatility': 0,
                'price_range': {'min': 0, 'max': 0},
                'average_price': 0,
                'sample_count': 0,
                'time_span_hours': 0
            }
        
        # Filter by time window
        cutoff_time = datetime.now() - timedelta(hours=hours)
        filtered_history = [
            record for record in history
            if record.timestamp >= cutoff_time
        ]
        
        if not filtered_history:
            return {
                'trend': 'no_recent_data',
                'volatility': 0,
                'price_range': {'min': 0, 'max': 0},
                'average_price': 0,
                'sample_count': 0,
                'time_span_hours': 0
            }
        
        # Extract prices and timestamps
        prices = [record.exchange_rate for record in filtered_history]
        timestamps = [record.timestamp for record in filtered_history]
        
        # Basic statistics
        min_price = min(prices)
        max_price = max(prices)
        avg_price = sum(prices) / len(prices)
        
        # Calculate volatility (coefficient of variation)
        if avg_price > 0:
            variance = sum((p - avg_price) ** 2 for p in prices) / len(prices)
            std_dev = variance ** 0.5
            volatility = (std_dev / avg_price) * 100
        else:
            volatility = 0
        
        # Determine trend
        trend = self._determine_trend(prices, timestamps)
        
        # Time span
        time_span = (max(timestamps) - min(timestamps)).total_seconds() / 3600
        
        return {
            'trend': trend,
            'volatility': volatility,
            'price_range': {'min': min_price, 'max': max_price},
            'average_price': avg_price,
            'sample_count': len(filtered_history),
            'time_span_hours': time_span,
            'latest_price': prices[0] if prices else 0,  # Assuming sorted desc
            'price_change_24h': self._calculate_24h_change(filtered_history)
        }
    
    def _determine_trend(self, prices: List[float], timestamps: List[datetime]) -> str:
        """Determine price trend using simple linear regression."""
        if len(prices) < 2:
            return 'insufficient_data'
        
        # Convert timestamps to numeric values (hours since first timestamp)
        base_time = min(timestamps)
        x_values = [(ts - base_time).total_seconds() / 3600 for ts in timestamps]
        y_values = prices
        
        # Simple linear regression
        n = len(x_values)
        sum_x = sum(x_values)
        sum_y = sum(y_values)
        sum_xy = sum(x * y for x, y in zip(x_values, y_values))
        sum_x2 = sum(x * x for x in x_values)
        
        # Calculate slope
        denominator = n * sum_x2 - sum_x * sum_x
        if denominator == 0:
            return 'stable'
        
        slope = (n * sum_xy - sum_x * sum_y) / denominator
        
        # Determine trend based on slope
        if slope > 0.1:  # Arbitrary threshold for "significant" trend
            return 'increasing'
        elif slope < -0.1:
            return 'decreasing'
        else:
            return 'stable'
    
    def _calculate_24h_change(self, history: List[PriceHistoryRecord]) -> Dict[str, float]:
        """Calculate 24-hour price change."""
        if len(history) < 2:
            return {'absolute': 0, 'percentage': 0}
        
        # Get latest and oldest prices in the dataset
        latest_price = history[0].exchange_rate  # Assuming sorted desc
        oldest_price = history[-1].exchange_rate
        
        abs_change, pct_change = self.calculate_price_change(oldest_price, latest_price)
        
        return {
            'absolute': abs_change,
            'percentage': pct_change
        }
    
    def detect_anomalies(
        self,
        history: List[PriceHistoryRecord],
        sensitivity: float = 2.0
    ) -> List[Dict[str, Any]]:
        """
        Detect price anomalies using statistical methods.
        
        Args:
            history: Price history records
            sensitivity: Standard deviation multiplier for anomaly detection
            
        Returns:
            List of detected anomalies
        """
        if len(history) < 10:  # Need sufficient data for anomaly detection
            return []
        
        prices = [record.exchange_rate for record in history]
        
        # Calculate mean and standard deviation
        mean_price = sum(prices) / len(prices)
        variance = sum((p - mean_price) ** 2 for p in prices) / len(prices)
        std_dev = variance ** 0.5
        
        # Detect anomalies
        anomalies = []
        threshold = sensitivity * std_dev
        
        for i, record in enumerate(history):
            deviation = abs(record.exchange_rate - mean_price)
            if deviation > threshold:
                anomalies.append({
                    'timestamp': record.timestamp,
                    'price': record.exchange_rate,
                    'deviation': deviation,
                    'severity': 'high' if deviation > 3 * std_dev else 'medium',
                    'type': 'spike' if record.exchange_rate > mean_price else 'dip'
                })
        
        return anomalies
    
    def generate_price_alert_message(
        self,
        pair_name: str,
        old_price: float,
        new_price: float,
        analysis: Dict[str, Any]
    ) -> str:
        """Generate a human-readable alert message for price changes."""
        abs_change, pct_change = self.calculate_price_change(old_price, new_price)
        
        direction = "increased" if pct_change > 0 else "decreased"
        
        message = f"🚨 {pair_name} price {direction} by {abs(pct_change):.2f}%\n"
        message += f"Previous: {old_price:.4f}\n"
        message += f"Current: {new_price:.4f}\n"
        message += f"Change: {abs_change:+.4f} ({pct_change:+.2f}%)\n"
        
        if analysis.get('trend'):
            message += f"Trend: {analysis['trend']}\n"
        
        if analysis.get('volatility'):
            message += f"24h Volatility: {analysis['volatility']:.2f}%"
        
        return message

"""
Relay Monitor 的核心价格监控引擎。

协调配置的桥接对的价格监控、变化检测和警报触发。
"""

import logging
import signal
import sys
from typing import Dict, List, Optional, Any, TYPE_CHECKING
from datetime import datetime

if TYPE_CHECKING:
    from ..alerts.system import AlertSystem
    from ..trading.executor import TradingExecutor

from ..api.client import RelayAPIClient
from ..storage.database import DataStorage
from ..storage.models import PriceHistoryRecord, AlertHistoryRecord
from ..config.manager import ConfigManager
from ..config.models import MonitorPair
from ..maintenance.tasks import MaintenanceManager
from .scheduler import MonitorScheduler
from .analyzer import PriceAnalyzer


logger = logging.getLogger(__name__)


class PriceMonitor:
    """
    Core price monitoring engine.
    
    Features:
    - Automated price monitoring for configured pairs
    - Price change detection and analysis
    - Alert triggering
    - Data persistence
    - Graceful shutdown handling
    """
    
    def __init__(
        self,
        config_manager: Optional[ConfigManager] = None,
        api_client: Optional[RelayAPIClient] = None,
        storage: Optional[DataStorage] = None,
        alert_system: Optional['AlertSystem'] = None,
        trading_executor: Optional['TradingExecutor'] = None
    ):
        """
        Initialize the price monitor.

        Args:
            config_manager: Configuration manager instance
            api_client: Relay API client instance
            storage: Data storage instance
            alert_system: Alert system instance for sending notifications
            trading_executor: Trading executor for automatic trade execution
        """
        self.logger = logger
        
        # Initialize components
        self.config_manager = config_manager or ConfigManager()
        self.config = self.config_manager.get_config()
        
        self.api_client = api_client or RelayAPIClient(
            base_url=self.config.api.base_url,
            timeout=self.config.api.timeout,
            retry_attempts=self.config.api.retry_attempts,
            cache_ttl=self.config.api.cache_ttl
        )
        
        self.storage = storage or DataStorage(self.config.database.path)
        self.alert_system = alert_system
        self.trading_executor = trading_executor

        # 初始化监控组件
        self.scheduler = MonitorScheduler()
        self.analyzer = PriceAnalyzer()
        self.maintenance_manager = MaintenanceManager(self.config, self.storage)

        # 初始化交易监控服务
        self.transaction_monitor = None
        if self.trading_executor:
            from ..trading.monitor import TransactionMonitor
            self.transaction_monitor = TransactionMonitor(
                trading_executor=self.trading_executor,
                check_interval=300  # 5分钟检查一次
            )

        # 状态跟踪
        self.running = False
        self.last_prices: Dict[str, float] = {}  # pair_name -> last_price
        self.last_alert_times: Dict[str, datetime] = {}  # pair_name -> last_alert_time

        # 内存管理配置
        self.max_cache_size = 1000  # 最大缓存条目数
        self.cache_cleanup_interval = 3600  # 清理间隔（秒）
        self.last_cleanup_time = datetime.now()
        
        # 统计信息
        self.stats = {
            'monitoring_started': None,
            'total_checks': 0,
            'alerts_triggered': 0,
            'errors_encountered': 0,
            'pairs_monitored': 0,
            'trades_executed': 0
        }
        
        # 设置信号处理器以优雅关闭
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        # frame parameter is required by signal handler interface
        _ = frame  # Suppress unused variable warning
        self.logger.info(f"Received signal {signum}, shutting down...")
        self.stop()
        sys.exit(0)
    
    def start(self):
        """Start the price monitoring system."""
        if self.running:
            self.logger.warning("Price monitor is already running")
            return

        self.logger.info(f"🚀 MONITOR ENGINE STARTING - Instance ID: {id(self)}")
        
        self.logger.info("Starting Relay Price Monitor")
        
        try:
            # Load configuration
            self.config = self.config_manager.get_config()

            # Get enabled pairs from database instead of config file
            enabled_pairs = self.storage.get_enabled_monitor_pairs()

            if not enabled_pairs:
                self.logger.error("No enabled monitor pairs found in database")
                return
            
            self.logger.info(f"Found {len(enabled_pairs)} enabled monitor pairs")
            
            # Initialize API client cache
            self.logger.info("Initializing API client cache...")
            self.api_client.get_chains()  # Pre-load chains
            
            # Setup monitoring tasks for each pair
            for pair in enabled_pairs:
                self._setup_pair_monitoring(pair)

            # Setup maintenance tasks
            self._setup_maintenance_tasks()

            # Setup automatic database monitoring
            self._setup_database_monitoring()

            # Start the scheduler
            self.scheduler.start()

            # Start transaction monitor if available
            if self.transaction_monitor:
                self.transaction_monitor.start()
                self.logger.info("交易状态监控服务已启动")

            # Update state
            self.running = True
            self.stats['monitoring_started'] = datetime.now()
            self.stats['pairs_monitored'] = len(enabled_pairs)

            self.logger.info("Price monitoring started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start price monitoring: {e}")
            raise
    
    def _setup_pair_monitoring(self, pair: MonitorPair):
        """Setup monitoring for a specific pair."""
        self.logger.info(f"Setting up monitoring for {pair.name}")
        
        def monitor_pair():
            """Monitor function for a specific pair."""
            try:
                self._check_pair_price(pair)
            except Exception as e:
                self.logger.error(f"Error monitoring pair {pair.name}: {e}")
                self.stats['errors_encountered'] += 1
        
        # Add to scheduler
        self.scheduler.add_monitoring_task(
            task_name=f"monitor_{pair.name}",
            task_func=monitor_pair,
            interval_seconds=self.config.monitoring.interval_seconds
        )
        
        self.logger.info(f"Monitoring task added for {pair.name} "
                        f"(interval: {self.config.monitoring.interval_seconds}s)")

    def _setup_maintenance_tasks(self):
        """Setup maintenance tasks."""
        self.logger.info("Setting up maintenance tasks...")

        # Database cleanup task
        if self.config.database.cleanup_enabled:
            def database_cleanup():
                """Database cleanup task."""
                try:
                    if self.maintenance_manager.should_run_database_cleanup():
                        result = self.maintenance_manager.run_database_cleanup()
                        if result['success']:
                            self.logger.info(f"Database cleanup completed: "
                                           f"saved {result.get('space_saved_mb', 0):.2f}MB")
                        else:
                            self.logger.error(f"Database cleanup failed: {result.get('error', 'Unknown error')}")
                except Exception as e:
                    self.logger.error(f"Database cleanup task error: {e}")

            self.scheduler.add_monitoring_task(
                task_name="database_cleanup",
                task_func=database_cleanup,
                interval_seconds=self.config.database.cleanup_interval_hours * 3600
            )
            self.logger.info(f"Database cleanup task added "
                           f"(interval: {self.config.database.cleanup_interval_hours}h)")



        # Log cleanup task
        if self.config.logging.cleanup_enabled:
            def log_cleanup():
                """Log cleanup task."""
                try:
                    if self.maintenance_manager.should_run_log_cleanup():
                        result = self.maintenance_manager.run_log_cleanup()
                        if result['success']:
                            self.logger.info(f"Log cleanup completed: "
                                           f"removed {result.get('files_removed', 0)} files, "
                                           f"freed {result.get('total_size_cleaned_mb', 0):.2f}MB")
                        else:
                            self.logger.error(f"Log cleanup failed: {result.get('error', 'Unknown error')}")
                except Exception as e:
                    self.logger.error(f"Log cleanup task error: {e}")

            self.scheduler.add_monitoring_task(
                task_name="log_cleanup",
                task_func=log_cleanup,
                interval_seconds=self.config.logging.cleanup_interval_hours * 3600
            )
            self.logger.info(f"Log cleanup task added "
                           f"(interval: {self.config.logging.cleanup_interval_hours}h)")

        self.logger.info("Maintenance tasks setup completed")

    def _setup_database_monitoring(self):
        """Setup automatic database monitoring for configuration changes."""
        def check_database_changes():
            """Periodically check for database configuration changes."""
            try:
                if self._check_database_pairs_changed():
                    self.logger.info("Database configuration changes detected, performing hot reload...")
                    self._hot_reload_monitor_pairs()
            except Exception as e:
                self.logger.error(f"Database monitoring check failed: {e}")

        # Check for database changes every 30 seconds
        self.scheduler.add_monitoring_task(
            task_name="database_config_monitor",
            task_func=check_database_changes,
            interval_seconds=30
        )

        self.logger.info("Database configuration monitoring enabled (interval: 30s)")

    def _cleanup_memory_cache(self):
        """清理内存缓存，防止无限增长。"""
        try:
            current_time = datetime.now()

            # 检查是否需要清理
            if (current_time - self.last_cleanup_time).total_seconds() < self.cache_cleanup_interval:
                return

            # 获取当前启用的交易对名称
            enabled_pairs = self.storage.get_enabled_monitor_pairs()
            enabled_pair_names = {pair.name for pair in enabled_pairs}

            # 清理 last_prices 中不再使用的条目
            old_price_keys = set(self.last_prices.keys()) - enabled_pair_names
            for key in old_price_keys:
                del self.last_prices[key]

            # 清理 last_alert_times 中不再使用的条目
            old_alert_keys = set(self.last_alert_times.keys()) - enabled_pair_names
            for key in old_alert_keys:
                del self.last_alert_times[key]

            # 如果缓存仍然过大，清理最旧的条目
            if len(self.last_prices) > self.max_cache_size:
                # 按时间排序，保留最新的条目
                sorted_prices = sorted(
                    self.last_prices.items(),
                    key=lambda x: self.last_alert_times.get(x[0], datetime.min),
                    reverse=True
                )
                self.last_prices = dict(sorted_prices[:self.max_cache_size])

            if len(self.last_alert_times) > self.max_cache_size:
                # 按时间排序，保留最新的条目
                sorted_alerts = sorted(
                    self.last_alert_times.items(),
                    key=lambda x: x[1],
                    reverse=True
                )
                self.last_alert_times = dict(sorted_alerts[:self.max_cache_size])

            # 更新清理时间
            self.last_cleanup_time = current_time

            # 记录清理结果
            removed_prices = len(old_price_keys)
            removed_alerts = len(old_alert_keys)
            if removed_prices > 0 or removed_alerts > 0:
                self.logger.info(f"Memory cache cleanup: removed {removed_prices} price entries, "
                               f"{removed_alerts} alert entries. "
                               f"Current cache sizes: prices={len(self.last_prices)}, "
                               f"alerts={len(self.last_alert_times)}")

        except Exception as e:
            self.logger.error(f"Error during memory cache cleanup: {e}")

    def _check_pair_price(self, pair: MonitorPair):
        """Check price for a specific monitoring pair."""
        import traceback
        call_stack = ''.join(traceback.format_stack()[-3:-1])  # Get caller info
        self.logger.info(f"🔍 PRICE CHECK STARTED for {pair.name}")
        self.logger.debug(f"Called from: {call_stack.strip()}")

        try:
            # 定期清理内存缓存
            self._cleanup_memory_cache()
            # Get current quote
            quote_response = self.api_client.get_quote(
                origin_chain=pair.origin_chain,
                destination_chain=pair.destination_chain,
                origin_token=pair.origin_token,
                destination_token=pair.destination_token,
                amount=pair.amount
            )
            
            current_price = quote_response.quote.exchange_rate
            current_fee = quote_response.quote.total_fee_usd
            
            self.logger.debug(f"{pair.name}: Rate={current_price:.4f}, Fee=${current_fee:.4f}")
            
            # Store price history
            price_record = PriceHistoryRecord(
                monitor_pair_name=pair.name,
                origin_chain_id=self.api_client.get_chain_by_name(pair.origin_chain).id,
                destination_chain_id=self.api_client.get_chain_by_name(pair.destination_chain).id,
                origin_token_symbol=pair.origin_token,
                destination_token_symbol=pair.destination_token,
                origin_amount=pair.amount,
                exchange_rate=current_price,
                total_fee_usd=current_fee,
                time_estimate_seconds=quote_response.quote.details.time_estimate,
                quote_data=quote_response.quote.model_dump()
            )
            
            self.storage.store_price_history(price_record)
            
            # Check for significant price changes
            if pair.name in self.last_prices:
                last_price = self.last_prices[pair.name]
                
                if self.analyzer.is_significant_change(
                    last_price, current_price, pair.alert_threshold_percent
                ):
                    # Check if exchange rate > 1 (only alert when rate is favorable)
                    if current_price <= 1.0:
                        self.logger.info(f"Price alert for {pair.name} skipped: exchange rate {current_price:.6f} <= 1.0")
                    # Check if enough time has passed since last alert for this pair
                    elif self._should_send_alert(pair.name):
                        self.logger.warning(f"🚨 PRICE ALERT TRIGGERED for {pair.name}: {last_price:.6f} → {current_price:.6f}")
                        self._trigger_price_alert(pair, last_price, current_price, quote_response.quote)
                        self.last_alert_times[pair.name] = datetime.now()
                    else:
                        self.logger.info(f"Price alert for {pair.name} skipped due to rate limiting (monitor level)")
            
            # Update last price
            self.last_prices[pair.name] = current_price
            
            # Update statistics
            self.stats['total_checks'] += 1
            
            self.logger.debug(f"Price check completed for {pair.name}")
            
        except Exception as e:
            self.logger.error(f"Failed to check price for {pair.name}: {e}")
            self.stats['errors_encountered'] += 1
            raise

    def _should_send_alert(self, pair_name: str) -> bool:
        """Check if enough time has passed since last alert for this pair."""
        if pair_name not in self.last_alert_times:
            return True

        # Use the same rate limit as the alert system (5 minutes)
        rate_limit_seconds = 300  # 5 minutes
        time_since_last = datetime.now() - self.last_alert_times[pair_name]

        should_send = time_since_last.total_seconds() >= rate_limit_seconds

        if not should_send:
            remaining_time = rate_limit_seconds - time_since_last.total_seconds()
            self.logger.debug(f"Rate limit check for {pair_name}: {remaining_time:.1f}s remaining")

        return should_send

    def _trigger_price_alert(self, pair: MonitorPair, old_price: float, new_price: float, quote=None):
        """Trigger price change alert."""
        self.logger.info(f"Price alert triggered for {pair.name}")
        
        try:
            # Get price history for analysis
            history = self.storage.get_price_history(pair.name, hours=24, limit=100)
            analysis = self.analyzer.analyze_price_history(history)
            
            # Generate alert message
            alert_message = self.analyzer.generate_price_alert_message(
                pair.name, old_price, new_price, analysis
            )
            
            # Calculate change
            _, pct_change = self.analyzer.calculate_price_change(old_price, new_price)
            
            # Store alert history
            alert_record = AlertHistoryRecord(
                monitor_pair_name=pair.name,
                alert_type="price_change",
                message=alert_message,
                old_rate=old_price,
                new_rate=new_price,
                change_percent=pct_change,
                channels_sent='["console"]'  # Will be updated by alert system
            )
            
            self.storage.store_alert(alert_record)

            # Send alert through alert system
            if hasattr(self, 'alert_system') and self.alert_system:
                try:
                    alert_sent = self.alert_system.send_price_alert(
                        pair_name=pair.name,
                        old_price=old_price,
                        new_price=new_price,
                        change_percent=pct_change,
                        analysis=analysis
                    )
                    if alert_sent:
                        self.logger.info(f"Price alert sent successfully for {pair.name}")
                    else:
                        self.logger.warning(f"Failed to send price alert for {pair.name}")
                except Exception as e:
                    self.logger.error(f"Error sending price alert for {pair.name}: {e}")
            else:
                # Fallback to logging if alert system not available
                self.logger.warning(f"PRICE ALERT: {alert_message}")

            # Update statistics
            self.stats['alerts_triggered'] += 1

            # 检查是否启用自动交易
            if pair.auto_trading_enabled and self.trading_executor and quote:
                try:
                    self.logger.info(f"自动交易已启用，开始执行交易: {pair.name}")
                    self.logger.info(f"复用监控过程中的 quote，避免重新获取")

                    # 执行自动交易（复用监控过程中的 quote）
                    trading_result = self.trading_executor.execute_price_alert_trade(
                        pair, old_price, new_price, quote
                    )

                    if trading_result.success:
                        self.logger.info(f"自动交易执行成功: {pair.name}, 交易哈希: {trading_result.tx_hash}")
                        self.stats['trades_executed'] += 1

                        # 发送交易成功通知
                        if self.alert_system:
                            self.alert_system.send_system_alert(
                                alert_type="info",
                                message=f"自动交易执行成功: {pair.name}",
                                details={
                                    'pair_name': pair.name,
                                    'tx_hash': trading_result.tx_hash,
                                    'amount_in': trading_result.amount_in,
                                    'amount_out': trading_result.amount_out
                                }
                            )
                    else:
                        self.logger.warning(f"自动交易执行失败: {pair.name}, 原因: {trading_result.error_message}")

                        # 发送交易失败通知
                        if self.alert_system:
                            self.alert_system.send_system_alert(
                                alert_type="warning",
                                message=f"自动交易执行失败: {pair.name}",
                                details={
                                    'pair_name': pair.name,
                                    'error': trading_result.error_message,
                                    'price_change': f"{old_price:.6f} → {new_price:.6f}"
                                }
                            )

                except Exception as e:
                    self.logger.error(f"自动交易执行异常: {pair.name}, 错误: {e}")

                    # 发送交易异常通知
                    if self.alert_system:
                        self.alert_system.send_system_alert(
                            alert_type="error",
                            message=f"自动交易执行异常: {pair.name}",
                            details={
                                'pair_name': pair.name,
                                'error': str(e),
                                'price_change': f"{old_price:.6f} → {new_price:.6f}"
                            }
                        )

        except Exception as e:
            self.logger.error(f"Failed to trigger alert for {pair.name}: {e}")
    
    def stop(self):
        """Stop the price monitoring system."""
        if not self.running:
            self.logger.warning("Price monitor is not running")
            return
        
        self.logger.info("Stopping price monitoring...")
        
        try:
            # Stop scheduler
            self.scheduler.stop()

            # Stop transaction monitor if available
            if self.transaction_monitor:
                self.transaction_monitor.stop()
                self.logger.info("交易状态监控服务已停止")

            # Update state
            self.running = False

            self.logger.info("Price monitoring stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping price monitoring: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取当前监控状态。"""
        status = {
            'running': self.running,
            'stats': self.stats.copy(),
            'scheduler_stats': self.scheduler.get_scheduler_stats(),
            'task_status': self.scheduler.get_task_status(),
            'last_prices': self.last_prices.copy(),
            'config_pairs': len(self.storage.get_enabled_monitor_pairs())
        }
        
        # 如果正在运行则添加运行时间
        if self.stats['monitoring_started']:
            uptime = datetime.now() - self.stats['monitoring_started']
            status['uptime_seconds'] = uptime.total_seconds()
            status['uptime_formatted'] = str(uptime).split('.')[0]
        
        return status

    def get_memory_stats(self) -> Dict[str, Any]:
        """获取内存使用统计信息。"""
        return {
            'last_prices_count': len(self.last_prices),
            'last_alert_times_count': len(self.last_alert_times),
            'max_cache_size': self.max_cache_size,
            'cache_cleanup_interval': self.cache_cleanup_interval,
            'last_cleanup_time': self.last_cleanup_time.isoformat(),
            'memory_usage_percentage': {
                'prices': (len(self.last_prices) / self.max_cache_size) * 100,
                'alerts': (len(self.last_alert_times) / self.max_cache_size) * 100
            }
        }

    def force_memory_cleanup(self) -> Dict[str, int]:
        """强制执行内存清理并返回清理结果。"""
        old_prices_count = len(self.last_prices)
        old_alerts_count = len(self.last_alert_times)

        # 临时重置清理时间以强制执行清理
        original_cleanup_time = self.last_cleanup_time
        self.last_cleanup_time = datetime.min

        try:
            self._cleanup_memory_cache()
        finally:
            # 如果清理失败，恢复原始时间
            if self.last_cleanup_time == datetime.min:
                self.last_cleanup_time = original_cleanup_time

        return {
            'prices_before': old_prices_count,
            'prices_after': len(self.last_prices),
            'prices_removed': old_prices_count - len(self.last_prices),
            'alerts_before': old_alerts_count,
            'alerts_after': len(self.last_alert_times),
            'alerts_removed': old_alerts_count - len(self.last_alert_times)
        }

    def check_pair_now(self, pair_name: str) -> Dict[str, Any]:
        """
        Manually trigger a price check for a specific pair.
        
        Args:
            pair_name: Name of the pair to check
            
        Returns:
            Check result information
        """
        # Find the pair configuration
        enabled_pairs = self.storage.get_enabled_monitor_pairs()
        pair = next((p for p in enabled_pairs if p.name == pair_name), None)
        
        if not pair:
            raise ValueError(f"Pair not found or not enabled: {pair_name}")
        
        self.logger.info(f"Manual price check for {pair_name}")
        
        try:
            self._check_pair_price(pair)
            
            # Get latest price data
            latest_record = self.storage.get_latest_price(pair_name)
            
            return {
                'pair_name': pair_name,
                'success': True,
                'timestamp': datetime.now(),
                'latest_price': latest_record.exchange_rate if latest_record else None,
                'latest_fee': latest_record.total_fee_usd if latest_record else None
            }
            
        except Exception as e:
            self.logger.error(f"Manual price check failed for {pair_name}: {e}")
            return {
                'pair_name': pair_name,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now()
            }
    
    def reload_config(self):
        """Reload configuration and restart monitoring if needed."""
        self.logger.info("Reloading configuration...")

        try:
            # Check if TOML config file has changed (for system settings)
            config_file_changed = self.config_manager.reload_if_changed()

            # Always check if database monitor pairs have changed
            # since this is now the primary source of truth for trading pairs
            database_pairs_changed = self._check_database_pairs_changed()

            if config_file_changed or database_pairs_changed:
                if config_file_changed:
                    self.logger.info("Configuration file changed, restarting monitoring...")
                    # Full restart for config file changes (system settings)
                    if self.running:
                        self.stop()
                        self.start()
                    else:
                        self.config = self.config_manager.get_config()
                elif database_pairs_changed:
                    self.logger.info("Database monitor pairs changed, performing hot reload...")
                    # Hot reload for database pairs changes only
                    if self.running:
                        self._hot_reload_monitor_pairs()

            else:
                self.logger.info("Configuration unchanged")

        except Exception as e:
            self.logger.error(f"Failed to reload configuration: {e}")

    def _check_database_pairs_changed(self) -> bool:
        """Check if database monitor pairs have changed since last load."""
        try:
            current_pairs = self.storage.get_enabled_monitor_pairs()
            current_pair_names = {pair.name for pair in current_pairs}

            # Get currently monitored pair names from scheduler
            current_tasks = self.scheduler.get_task_status()
            monitored_pair_names = {
                task_name.replace('monitor_', '')
                for task_name in current_tasks.keys()
                if task_name.startswith('monitor_') and not task_name.endswith('_cleanup')
            }

            # Check if pairs have been added or removed
            if current_pair_names != monitored_pair_names:
                self.logger.info(f"Database pairs changed: current={current_pair_names}, monitored={monitored_pair_names}")
                return True

            return False

        except Exception as e:
            self.logger.error(f"Failed to check database pairs changes: {e}")
            return False

    def _hot_reload_monitor_pairs(self):
        """Hot reload monitor pairs without full restart."""
        try:
            self.logger.info("Performing hot reload of monitor pairs...")

            # Get current pairs from database
            current_pairs = self.storage.get_enabled_monitor_pairs()
            current_pair_names = {pair.name for pair in current_pairs}

            # Get currently monitored tasks
            current_tasks = self.scheduler.get_task_status()
            monitored_task_names = {
                task_name for task_name in current_tasks.keys()
                if task_name.startswith('monitor_') and not task_name.endswith('_cleanup')
            }
            monitored_pair_names = {
                task_name.replace('monitor_', '') for task_name in monitored_task_names
            }

            # Remove tasks for pairs that no longer exist
            pairs_to_remove = monitored_pair_names - current_pair_names
            for pair_name in pairs_to_remove:
                task_name = f"monitor_{pair_name}"
                if self.scheduler.remove_task(task_name):
                    self.logger.info(f"Removed monitoring task for {pair_name}")
                    # Clean up cached data
                    self.last_prices.pop(pair_name, None)
                    self.last_alert_times.pop(pair_name, None)

            # Add tasks for new pairs
            pairs_to_add = current_pair_names - monitored_pair_names
            for pair_name in pairs_to_add:
                # Find the pair object
                pair = next((p for p in current_pairs if p.name == pair_name), None)
                if pair:
                    self._setup_pair_monitoring(pair)
                    self.logger.info(f"Added monitoring task for {pair_name}")

            # Update stats
            self.stats['pairs_monitored'] = len(current_pairs)

            self.logger.info(f"Hot reload completed: {len(pairs_to_remove)} removed, {len(pairs_to_add)} added")

        except Exception as e:
            self.logger.error(f"Failed to hot reload monitor pairs: {e}")
            raise
    
    def is_running(self) -> bool:
        """Check if the monitor is running."""
        return self.running

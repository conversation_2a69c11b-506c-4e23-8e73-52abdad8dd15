"""
价格监控操作的任务调度器。
"""

import time
import logging
import threading
from typing import Callable, Dict, Any, Optional
from datetime import datetime, timedelta, timezone

import schedule


logger = logging.getLogger(__name__)


class MonitorScheduler:
    """
    管理具有线程安全执行的定时监控任务。

    功能特性：
    - 可配置的监控间隔
    - 线程安全的任务执行
    - 错误处理和恢复
    - 任务状态跟踪
    - 优雅关闭
    """
    
    def __init__(self):
        self.logger = logger
        self.running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.tasks: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'tasks_executed': 0,
            'tasks_failed': 0,
            'last_execution': None,
            'uptime_start': None
        }
    
    def add_monitoring_task(
        self,
        task_name: str,
        task_func: Callable,
        interval_seconds: int,
        *args,
        **kwargs
    ):
        """
        向调度器添加监控任务。

        Args:
            task_name: 任务的唯一名称
            task_func: 要执行的函数
            interval_seconds: 执行间隔（秒）
            *args: 传递给函数的参数
            **kwargs: 传递给函数的关键字参数
        """
        with self._lock:
            # 检查任务是否已存在
            if task_name in self.tasks:
                self.logger.warning(f"Task {task_name} already exists, skipping duplicate")
                return

            # Wrap the task function with error handling
            def wrapped_task():
                try:
                    self.logger.debug(f"Executing task: {task_name}")
                    start_time = time.time()
                    
                    result = task_func(*args, **kwargs)
                    
                    execution_time = time.time() - start_time
                    
                    # Update task statistics
                    self.tasks[task_name].update({
                        'last_execution': datetime.now(timezone.utc),
                        'last_result': result,
                        'last_error': None,
                        'execution_count': self.tasks[task_name].get('execution_count', 0) + 1,
                        'last_execution_time': execution_time
                    })
                    
                    self.stats['tasks_executed'] += 1
                    self.stats['last_execution'] = datetime.now()
                    
                    self.logger.debug(f"Task {task_name} completed in {execution_time:.2f}s")
                    
                except Exception as e:
                    self.logger.error(f"Task {task_name} failed: {e}")
                    
                    # Update error statistics
                    self.tasks[task_name].update({
                        'last_error': str(e),
                        'error_count': self.tasks[task_name].get('error_count', 0) + 1,
                        'last_error_time': datetime.now()
                    })
                    
                    self.stats['tasks_failed'] += 1
            
            # Schedule the task and store the job object for later removal
            if interval_seconds < 60:
                # For intervals less than 1 minute, use seconds
                job = schedule.every(interval_seconds).seconds.do(wrapped_task)
            elif interval_seconds < 3600:
                # For intervals less than 1 hour, use minutes
                job = schedule.every(interval_seconds // 60).minutes.do(wrapped_task)
            else:
                # For longer intervals, use hours
                job = schedule.every(interval_seconds // 3600).hours.do(wrapped_task)

            # Store task information including the job object
            self.tasks[task_name] = {
                'function': task_func,
                'interval_seconds': interval_seconds,
                'args': args,
                'kwargs': kwargs,
                'job': job,  # Store the schedule job object for removal
                'created_at': datetime.now(timezone.utc),
                'execution_count': 0,
                'error_count': 0,
                'last_execution': None,
                'last_error': None
            }
            
            self.logger.info(f"Added monitoring task: {task_name} (interval: {interval_seconds}s)")
    
    def start(self):
        """在单独的线程中启动调度器。"""
        if self.running:
            self.logger.warning("Scheduler is already running")
            return
        
        self.running = True
        self.stats['uptime_start'] = datetime.now()
        
        def run_scheduler():
            self.logger.info("Scheduler started")
            
            while self.running:
                try:
                    schedule.run_pending()
                    time.sleep(1)  # 每秒检查一次
                except Exception as e:
                    self.logger.error(f"Scheduler error: {e}")
                    time.sleep(5)  # 重试前等待
            
            self.logger.info("Scheduler stopped")
        
        self.scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        self.logger.info("Scheduler thread started")
    
    def stop(self):
        """优雅地停止调度器。"""
        if not self.running:
            self.logger.warning("Scheduler is not running")
            return
        
        self.logger.info("Stopping scheduler...")
        self.running = False
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=10)
            
            if self.scheduler_thread.is_alive():
                self.logger.warning("Scheduler thread did not stop gracefully")
            else:
                self.logger.info("Scheduler stopped successfully")
    
    def get_task_status(self, task_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Get status information for tasks.
        
        Args:
            task_name: Specific task name, or None for all tasks
            
        Returns:
            Task status information
        """
        with self._lock:
            if task_name:
                if task_name in self.tasks:
                    task_info = self.tasks[task_name].copy()
                    # Remove non-serializable job object
                    task_info.pop('job', None)
                    return {task_name: task_info}
                else:
                    return {}
            else:
                # Return all tasks without job objects
                result = {}
                for name, info in self.tasks.items():
                    task_info = info.copy()
                    task_info.pop('job', None)  # Remove non-serializable job object
                    result[name] = task_info
                return result
    
    def get_scheduler_stats(self) -> Dict[str, Any]:
        """Get scheduler statistics."""
        stats = self.stats.copy()
        
        if stats['uptime_start']:
            uptime = datetime.now() - stats['uptime_start']
            stats['uptime_seconds'] = uptime.total_seconds()
            stats['uptime_formatted'] = str(uptime).split('.')[0]  # Remove microseconds
        
        stats['running'] = self.running
        stats['task_count'] = len(self.tasks)
        
        return stats
    
    def remove_task(self, task_name: str) -> bool:
        """
        Remove a task from the scheduler.

        Args:
            task_name: Name of the task to remove

        Returns:
            True if task was removed, False if not found
        """
        with self._lock:
            if task_name in self.tasks:
                # Get the job object and cancel it
                task_info = self.tasks[task_name]
                job = task_info.get('job')

                if job:
                    # Cancel the scheduled job
                    schedule.cancel_job(job)
                    self.logger.debug(f"Cancelled scheduled job for task: {task_name}")

                # Remove from our task tracking
                del self.tasks[task_name]
                self.logger.info(f"Removed task: {task_name}")
                return True
            else:
                self.logger.warning(f"Task not found: {task_name}")
                return False
    
    def clear_all_tasks(self):
        """Clear all scheduled tasks."""
        with self._lock:
            schedule.clear()
            self.tasks.clear()
            self.logger.info("All tasks cleared")
    
    def execute_task_now(self, task_name: str) -> Any:
        """
        Execute a specific task immediately (outside of schedule).
        
        Args:
            task_name: Name of the task to execute
            
        Returns:
            Task result
        """
        with self._lock:
            if task_name not in self.tasks:
                raise ValueError(f"Task not found: {task_name}")
            
            task_info = self.tasks[task_name]
            task_func = task_info['function']
            args = task_info['args']
            kwargs = task_info['kwargs']
        
        self.logger.info(f"Executing task immediately: {task_name}")
        
        try:
            result = task_func(*args, **kwargs)
            self.logger.info(f"Task {task_name} executed successfully")
            return result
        except Exception as e:
            self.logger.error(f"Task {task_name} failed during immediate execution: {e}")
            raise
    
    def is_running(self) -> bool:
        """Check if the scheduler is running."""
        return self.running
    
    def get_next_run_times(self) -> Dict[str, Optional[datetime]]:
        """Get next scheduled run times for all tasks."""
        # Note: This is a simplified implementation
        # The schedule library doesn't provide easy access to next run times
        next_runs = {}
        
        for task_name, task_info in self.tasks.items():
            last_execution = task_info.get('last_execution')
            interval = task_info['interval_seconds']
            
            if last_execution:
                next_run = last_execution + timedelta(seconds=interval)
            else:
                # If never executed, assume it will run soon
                next_run = datetime.now(timezone.utc) + timedelta(seconds=interval)
            
            next_runs[task_name] = next_run
        
        return next_runs

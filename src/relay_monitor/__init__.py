"""
Relay Monitor - Cross-chain Bridge Price Monitoring System

A Python application for monitoring cross-chain bridge prices using the Relay API.
Supports configurable chains and tokens, real-time price monitoring, alerts, and web interface.
"""

__version__ = "0.1.0"
__author__ = "Relay Monitor Team"
__email__ = "<EMAIL>"

# Package metadata
__title__ = "relay-monitor"
__description__ = "Cross-chain bridge price monitoring system"
__url__ = "https://github.com/yourusername/relay-monitor"
__license__ = "MIT"

# Version info
VERSION = __version__
VERSION_INFO = tuple(map(int, __version__.split(".")))

# Import main components for easy access
from .api.client import RelayAPIClient
from .config.manager import ConfigManager
from .monitor.engine import PriceMonitor
from .storage.database import DataStorage
from .alerts.system import AlertSystem
from .web.app import create_app
from .cli.commands import main as cli_main

__all__ = [
    "RelayAPIClient",
    "ConfigManager",
    "PriceMonitor",
    "DataStorage",
    "AlertSystem",
    "create_app",
    "cli_main",
    "__version__",
    "VERSION",
    "VERSION_INFO",
]

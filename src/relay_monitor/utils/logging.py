"""
日志配置和工具。
"""

import os
import logging
import logging.handlers
from typing import Optional

from ..config.models import LoggingConfig


def setup_logging(config: LoggingConfig, debug_mode: bool = False) -> None:
    """
    Setup logging configuration based on config.

    Args:
        config: Logging configuration
        debug_mode: If True, override log level to DEBUG regardless of config
    """
    # Create logs directory if it doesn't exist
    log_dir = os.path.dirname(config.file)
    if log_dir:
        os.makedirs(log_dir, exist_ok=True)

    # Determine effective log level
    if debug_mode:
        effective_level = "DEBUG"
        print(f"🔧 Debug mode enabled - Log level automatically set to DEBUG (overriding config: {config.level})")
    else:
        effective_level = config.level

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, effective_level.upper()))

    # Clear existing handlers
    root_logger.handlers.clear()

    # Create formatter
    formatter = logging.Formatter(config.format)

    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, effective_level.upper()))
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # File handler with rotation
    if config.file:
        file_handler = logging.handlers.RotatingFileHandler(
            config.file,
            maxBytes=config.max_file_size_mb * 1024 * 1024,
            backupCount=config.backup_count
        )
        file_handler.setLevel(getattr(logging, effective_level.upper()))
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

    # Set specific logger levels
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)

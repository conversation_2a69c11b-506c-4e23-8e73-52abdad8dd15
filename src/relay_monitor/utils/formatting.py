"""
CLI 输出的格式化工具。
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timezone, timedelta
from tabulate import tabulate
import colorama
from colorama import Fore, Style

# Initialize colorama for cross-platform colored output
colorama.init()


def format_table(data: List[Dict[str, Any]], headers: Optional[List[str]] = None) -> str:
    """
    Format data as a table.
    
    Args:
        data: List of dictionaries to format
        headers: Optional list of headers
        
    Returns:
        Formatted table string
    """
    if not data:
        return "No data available"
    
    if headers is None:
        headers = list(data[0].keys())
    
    # Extract values in header order
    table_data = []
    for row in data:
        table_data.append([row.get(header, 'N/A') for header in headers])
    
    return tabulate(table_data, headers=headers, tablefmt='grid')


def format_status(running: bool, uptime: Optional[str] = None) -> str:
    """
    使用颜色格式化状态。

    Args:
        running: 系统是否正在运行
        uptime: 可选的运行时间字符串

    Returns:
        格式化的状态字符串
    """
    if running:
        status = f"{Fore.GREEN}●{Style.RESET_ALL} 运行中"
        if uptime:
            status += f" (运行时间: {uptime})"
    else:
        status = f"{Fore.RED}●{Style.RESET_ALL} 已停止"
    
    return status


def format_duration(seconds: float) -> str:
    """
    将持续时间格式化为人类可读的格式。

    Args:
        seconds: 持续时间（秒）

    Returns:
        格式化的持续时间字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    elif seconds < 86400:
        hours = seconds / 3600
        return f"{hours:.1f}h"
    else:
        days = seconds / 86400
        return f"{days:.1f}d"


def format_number(value: Optional[float], decimals: int = 4) -> str:
    """
    使用指定小数位数格式化数字。

    Args:
        value: 要格式化的数字
        decimals: 小数位数

    Returns:
        格式化的数字字符串
    """
    if value is None:
        return "不可用"
    return f"{value:.{decimals}f}"


def format_percentage(value: Optional[float], decimals: int = 2) -> str:
    """
    使用颜色编码格式化百分比。

    Args:
        value: 百分比值
        decimals: 小数位数

    Returns:
        带颜色的格式化百分比字符串
    """
    if value is None:
        return "不可用"
    
    formatted = f"{value:+.{decimals}f}%"
    
    if value > 0:
        return f"{Fore.GREEN}{formatted}{Style.RESET_ALL}"
    elif value < 0:
        return f"{Fore.RED}{formatted}{Style.RESET_ALL}"
    else:
        return formatted


def format_currency(value: Optional[float], decimals: int = 4) -> str:
    """
    格式化货币值。

    Args:
        value: 货币值
        decimals: 小数位数

    Returns:
        格式化的货币字符串
    """
    if value is None:
        return "不可用"
    return f"${value:.{decimals}f}"


def format_timestamp(timestamp: Optional[datetime]) -> str:
    """
    Format timestamp in human-readable format.
    
    Args:
        timestamp: Datetime object
        
    Returns:
        Formatted timestamp string
    """
    if timestamp is None:
        return "Never"
    
    # Convert timestamp to UTC if no timezone info
    if timestamp.tzinfo is None:
        timestamp = timestamp.replace(tzinfo=timezone.utc)

    now = datetime.now(timezone.utc)
    diff = now - timestamp
    
    if diff.total_seconds() < 60:
        return "Just now"
    elif diff.total_seconds() < 3600:
        minutes = int(diff.total_seconds() / 60)
        return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
    elif diff.total_seconds() < 86400:
        hours = int(diff.total_seconds() / 3600)
        return f"{hours} hour{'s' if hours != 1 else ''} ago"
    else:
        return timestamp.strftime("%Y-%m-%d %H:%M:%S")


def print_header(title: str) -> None:
    """
    打印格式化的标题。

    Args:
        title: 标题文本
    """
    print(f"\n{Fore.CYAN}{Style.BRIGHT}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{Style.BRIGHT}{title.center(60)}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{Style.BRIGHT}{'='*60}{Style.RESET_ALL}\n")


def print_success(message: str) -> None:
    """
    Print a success message.
    
    Args:
        message: Success message
    """
    print(f"{Fore.GREEN}✅ {message}{Style.RESET_ALL}")


def print_error(message: str) -> None:
    """
    Print an error message.
    
    Args:
        message: Error message
    """
    print(f"{Fore.RED}❌ {message}{Style.RESET_ALL}")


def print_warning(message: str) -> None:
    """
    Print a warning message.
    
    Args:
        message: Warning message
    """
    print(f"{Fore.YELLOW}⚠️  {message}{Style.RESET_ALL}")


def print_info(message: str) -> None:
    """
    Print an info message.
    
    Args:
        message: Info message
    """
    print(f"{Fore.BLUE}ℹ️  {message}{Style.RESET_ALL}")

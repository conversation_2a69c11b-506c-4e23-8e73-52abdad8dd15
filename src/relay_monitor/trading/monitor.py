"""
交易状态监控服务。

负责监控已提交交易的状态，更新交易记录，处理失败重试等。
"""

import time
import logging
import threading
from typing import Optional
from datetime import datetime, timezone, timedelta

from ..storage.database import DataStorage
from .executor import TradingExecutor


logger = logging.getLogger(__name__)


class TransactionMonitor:
    """
    交易状态监控服务。
    
    定期检查已提交交易的状态，更新数据库记录。
    """
    
    def __init__(self, trading_executor: TradingExecutor, check_interval: int = 60):
        """
        初始化交易监控服务。
        
        Args:
            trading_executor: 交易执行器实例
            check_interval: 检查间隔（秒）
        """
        self.trading_executor = trading_executor
        self.check_interval = check_interval
        self.running = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.logger = logger
        
        # 统计信息
        self.stats = {
            'checks_performed': 0,
            'transactions_confirmed': 0,
            'transactions_failed': 0,
            'last_check_time': None,
            'monitor_started': None
        }
    
    def start(self):
        """启动交易监控服务。"""
        if self.running:
            self.logger.warning("交易监控服务已在运行")
            return
        
        self.running = True
        self.stats['monitor_started'] = datetime.now(timezone.utc)
        
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            name="TransactionMonitor",
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info(f"交易监控服务已启动，检查间隔: {self.check_interval}秒")
    
    def stop(self):
        """停止交易监控服务。"""
        if not self.running:
            return
        
        self.running = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("交易监控服务已停止")
    
    def _monitor_loop(self):
        """监控循环。"""
        while self.running:
            try:
                self._check_pending_transactions()
                self.stats['checks_performed'] += 1
                self.stats['last_check_time'] = datetime.now(timezone.utc)
                
            except Exception as e:
                self.logger.error(f"交易监控检查失败: {e}")
            
            # 等待下次检查
            for _ in range(self.check_interval):
                if not self.running:
                    break
                time.sleep(1)
    
    def _check_pending_transactions(self):
        """检查待确认的交易。"""
        try:
            # 调用交易执行器的监控方法
            self.trading_executor.monitor_pending_transactions()
            
        except Exception as e:
            self.logger.error(f"检查待确认交易失败: {e}")
            raise
    
    def get_stats(self) -> dict:
        """获取监控统计信息。"""
        stats = self.stats.copy()
        stats['running'] = self.running
        
        if stats['monitor_started']:
            uptime = datetime.now(timezone.utc) - stats['monitor_started']
            stats['uptime_seconds'] = uptime.total_seconds()
            stats['uptime_formatted'] = str(uptime).split('.')[0]
        
        return stats
    
    def force_check(self):
        """强制执行一次检查。"""
        try:
            self.logger.info("执行强制交易状态检查")
            self._check_pending_transactions()
            self.logger.info("强制检查完成")
            
        except Exception as e:
            self.logger.error(f"强制检查失败: {e}")
            raise


class TransactionRetryService:
    """
    交易重试服务。
    
    处理失败交易的重试逻辑。
    """
    
    def __init__(self, trading_executor: TradingExecutor, max_retries: int = 3):
        """
        初始化重试服务。
        
        Args:
            trading_executor: 交易执行器实例
            max_retries: 最大重试次数
        """
        self.trading_executor = trading_executor
        self.max_retries = max_retries
        self.logger = logger
    
    def retry_failed_transactions(self):
        """重试失败的交易。"""
        try:
            storage = self.trading_executor.storage
            
            with storage.get_connection() as conn:
                # 获取可重试的失败交易
                cursor = conn.execute("""
                    SELECT id, monitor_pair_name, retry_count, error_message, created_at
                    FROM transaction_history 
                    WHERE status = 'failed' 
                    AND retry_count < ?
                    AND created_at > datetime('now', '-24 hours')
                    ORDER BY created_at DESC
                    LIMIT 10
                """, (self.max_retries,))
                
                failed_transactions = cursor.fetchall()
                
                for tx_record in failed_transactions:
                    tx_id, pair_name, retry_count, error_message, created_at = tx_record
                    
                    try:
                        self.logger.info(f"尝试重试失败交易: {pair_name}, 重试次数: {retry_count + 1}")
                        
                        # 这里可以实现重试逻辑
                        # 目前只是更新重试计数
                        self._update_retry_count(tx_id, retry_count + 1)
                        
                    except Exception as e:
                        self.logger.error(f"重试交易失败 {tx_id}: {e}")
                
                if failed_transactions:
                    self.logger.info(f"处理了 {len(failed_transactions)} 个失败交易")
                
        except Exception as e:
            self.logger.error(f"重试失败交易服务异常: {e}")
    
    def _update_retry_count(self, tx_id: int, retry_count: int):
        """更新重试计数。"""
        try:
            storage = self.trading_executor.storage
            
            with storage.get_connection() as conn:
                conn.execute("""
                    UPDATE transaction_history 
                    SET retry_count = ?
                    WHERE id = ?
                """, (retry_count, tx_id))
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"更新重试计数失败: {e}")
            raise


class TransactionCleanupService:
    """
    交易记录清理服务。
    
    定期清理过期的交易记录。
    """
    
    def __init__(self, storage: DataStorage, retention_days: int = 30):
        """
        初始化清理服务。
        
        Args:
            storage: 数据存储实例
            retention_days: 保留天数
        """
        self.storage = storage
        self.retention_days = retention_days
        self.logger = logger
    
    def cleanup_old_transactions(self):
        """清理过期的交易记录。"""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=self.retention_days)
            
            with self.storage.get_connection() as conn:
                cursor = conn.execute("""
                    DELETE FROM transaction_history 
                    WHERE created_at < ?
                """, (cutoff_date.isoformat(),))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                if deleted_count > 0:
                    self.logger.info(f"清理了 {deleted_count} 条过期交易记录")
                
                return deleted_count
                
        except Exception as e:
            self.logger.error(f"清理交易记录失败: {e}")
            raise

"""
交易执行相关的数据模型。
"""

from typing import Optional, Dict, Any, List
from datetime import datetime, timezone
from enum import Enum
from pydantic import BaseModel, Field


class TradingStatus(str, Enum):
    """交易状态枚举。"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    CONFIRMED = "confirmed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TradingConfig(BaseModel):
    """交易配置模型。"""
    
    id: Optional[int] = None
    monitor_pair_name: str
    wallet_name: str
    enabled: bool = True
    
    # 交易参数
    trading_amount: str  # 交易金额
    max_slippage_percent: float = 5.0  # 最大滑点百分比
    
    # 风险控制
    daily_limit_usd: Optional[float] = None  # 日交易限额（美元）
    single_trade_limit_usd: Optional[float] = None  # 单次交易限额（美元）
    
    # 触发条件
    min_price_change_percent: float = 5.0  # 最小价格变化百分比
    cooldown_minutes: int = 60  # 交易冷却时间（分钟）
    
    # 时间戳
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    class Config:
        use_enum_values = True


class TradingResult(BaseModel):
    """交易执行结果模型。"""
    
    success: bool
    tx_hash: Optional[str] = None
    request_id: Optional[str] = None  # Relay request ID
    error_message: Optional[str] = None
    
    # 交易详情
    amount_in: Optional[str] = None
    amount_out: Optional[str] = None
    gas_used: Optional[int] = None
    gas_price: Optional[str] = None
    
    # 时间信息
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    estimated_completion_time: Optional[datetime] = None
    
    class Config:
        arbitrary_types_allowed = True


class TransactionRecord(BaseModel):
    """交易历史记录模型。"""
    
    id: Optional[int] = None
    monitor_pair_name: str
    wallet_name: str
    wallet_address: str
    
    # 交易信息
    tx_hash: str
    request_id: Optional[str] = None
    status: TradingStatus
    
    # 金额信息
    amount_in: str
    token_in_symbol: str
    amount_out: str
    token_out_symbol: str
    
    # 费用信息
    gas_used: Optional[int] = None
    gas_price: Optional[str] = None
    total_fee_usd: Optional[float] = None
    
    # 价格信息
    trigger_price: float  # 触发交易时的价格
    execution_price: Optional[float] = None  # 实际执行价格
    slippage_percent: Optional[float] = None
    
    # 错误信息
    error_message: Optional[str] = None
    retry_count: int = 0
    
    # 时间信息
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    submitted_at: Optional[datetime] = None
    confirmed_at: Optional[datetime] = None
    
    # 原始数据
    quote_data: Optional[Dict[str, Any]] = None
    transaction_data: Optional[Dict[str, Any]] = None
    
    class Config:
        use_enum_values = True


class RiskMetrics(BaseModel):
    """风险指标模型。"""
    
    wallet_address: str
    
    # 交易统计
    daily_volume_usd: float = 0.0
    daily_trade_count: int = 0
    total_volume_usd: float = 0.0
    total_trade_count: int = 0
    
    # 成功率
    success_rate: float = 0.0
    failed_trades_count: int = 0
    
    # 最近交易
    last_trade_time: Optional[datetime] = None
    cooldown_remaining_minutes: int = 0
    
    # 余额信息
    current_balance_eth: Optional[float] = None
    current_balance_usd: Optional[float] = None
    
    # 风险评分 (0-100)
    risk_score: float = 0.0
    
    class Config:
        arbitrary_types_allowed = True


class TradingLimits(BaseModel):
    """交易限制配置。"""
    
    # 全局限制
    global_daily_limit_usd: float = 10000.0
    global_single_trade_limit_usd: float = 1000.0
    
    # 钱包限制
    wallet_daily_limit_usd: float = 5000.0
    wallet_single_trade_limit_usd: float = 500.0
    
    # 频率限制
    min_interval_minutes: int = 5  # 最小交易间隔
    max_trades_per_hour: int = 10
    max_trades_per_day: int = 50
    
    # 滑点限制
    max_slippage_percent: float = 10.0
    
    # 余额要求
    min_balance_reserve_percent: float = 10.0  # 保留余额百分比
    
    class Config:
        arbitrary_types_allowed = True

"""
交易执行器。

负责自动交易的执行、监控和风险控制。
"""

import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timezone

from ..api.client import RelayAPIClient
from ..api.models import Quote
from ..config.models import MonitorPair
from ..crypto.wallet import WalletManager
from ..crypto.signer import TransactionSigner
from ..storage.database import DataStorage
from ..alerts.channels import BarkChannel
from ..config.runtime import RuntimeConfigManager

from .models import (
    TradingConfig, TradingResult, TradingStatus,
    TransactionRecord
)
from .exceptions import TradingError


logger = logging.getLogger(__name__)


class TradingExecutor:
    """
    交易执行器。
    
    负责基于价格监控触发的自动交易执行。
    """
    
    def __init__(
        self,
        api_client: RelayAPIClient,
        wallet_manager: WalletManager,
        storage: DataStorage,
    ):
        """
        初始化交易执行器。

        Args:
            api_client: Relay API 客户端
            wallet_manager: 钱包管理器
            storage: 数据存储
        """
        self.api_client = api_client
        self.wallet_manager = wallet_manager
        self.storage = storage
        self.signer = TransactionSigner(wallet_manager)
        self.logger = logger
        

        # 确保交易相关表存在
        self._ensure_trading_tables()
    
    def execute_price_alert_trade(
        self,
        pair: MonitorPair,
        old_price: float,
        new_price: float,
        quote: Quote
    ) -> TradingResult:
        """
        执行基于价格告警的自动交易。
        
        Args:
            pair: 监控对配置
            old_price: 旧价格
            new_price: 新价格
            quote: 最新报价
            
        Returns:
            交易执行结果
        """
        try:
            self.logger.info(f"开始执行自动交易: {pair.name}")
            
            # 获取交易配置
            trading_config = self._get_trading_config(pair.name)
            if not trading_config or not trading_config.enabled:
                return TradingResult(
                    success=False,
                    error_message="交易配置未启用或不存在"
                )
            
            # 交易条件已在监控引擎中检查，直接执行交易

            # 构建交易
            tx_data = self._extract_transaction_from_quote(quote)
            if not tx_data:
                return TradingResult(
                    success=False,
                    error_message="无法从报价中提取交易数据"
                )

            # 检查钱包余额
            balance_check = self._check_wallet_balance(trading_config, tx_data)
            if not balance_check.success:
                # 发送余额不足通知
                self._send_balance_insufficient_notification(trading_config, balance_check.error_message or "余额不足")
                return balance_check
            
            # 执行交易
            result = self._execute_transaction(trading_config, tx_data, quote)
            
            # 记录交易历史
            self._record_transaction(
                trading_config, pair, quote, result, old_price, new_price
            )

            # 发送交易结果通知
            self._send_trading_result_notification(trading_config, pair, result, is_test=False)

            return result
            
        except Exception as e:
            self.logger.error(f"自动交易执行失败: {e}")
            return TradingResult(
                success=False,
                error_message=f"交易执行异常: {str(e)}"
            )

    def execute_test_trade(
        self,
        pair: MonitorPair,
        test_amount: float,
        quote: Quote
    ) -> TradingResult:
        """执行测试交易。"""
        try:
            self.logger.info(f"开始执行测试交易: {pair.name}, 金额: {test_amount}")

            # 检查钱包名称
            if not pair.wallet_name:
                return TradingResult(
                    success=False,
                    error_message="钱包名称未配置"
                )

            # 获取交易配置
            trading_config = self._get_trading_config(pair.name)
            if not trading_config:
                return TradingResult(
                    success=False,
                    error_message="交易配置不存在"
                )

            # 构建交易数据
            tx_data = self._extract_transaction_from_quote(quote)
            if not tx_data:
                return TradingResult(
                    success=False,
                    error_message="无法从报价中提取交易数据"
                )

            # 检查钱包余额（测试和真实都需要）
            balance_check = self._check_wallet_balance(trading_config, tx_data)
            if not balance_check.success:
                # 发送余额不足通知
                self._send_balance_insufficient_notification(trading_config, balance_check.error_message or "余额不足")
                return balance_check

            # 执行真实交易
            result = self._execute_transaction(trading_config, tx_data, quote)

            # 记录交易历史（测试和真实都需要）
            self._record_transaction(
                trading_config, pair, quote, result,
                old_price=0, new_price=0  # 测试交易没有价格变化
            )

            # 发送交易结果通知
            self._send_trading_result_notification(trading_config, pair, result, is_test=True)

            return result

        except Exception as e:
            self.logger.error(f"测试交易执行异常: {e}")
            return TradingResult(
                success=False,
                error_message=f"测试交易执行失败: {str(e)}"
            )
    


    def _extract_transaction_from_quote(self, quote: Quote) -> Optional[Dict[str, Any]]:
        """从报价中提取交易数据。"""
        try:
            self.logger.info(f"开始提取交易数据，共{len(quote.steps)}个步骤")

            for i, step in enumerate(quote.steps):
                self.logger.info(f"步骤 {i}: kind={step.get('kind')}, items={len(step.get('items', []))}")

                if step.get('kind') == 'transaction':
                    items = step.get('items', [])
                    for j, item in enumerate(items):
                        self.logger.info(f"  项目 {j}: status={item.get('status')}")

                        if item.get('status') == 'incomplete':
                            tx_data = item.get('data')
                            self.logger.info(f"  找到交易数据: {tx_data}")
                            return tx_data

            self.logger.warning("未找到可执行的交易数据")
            return None

        except Exception as e:
            self.logger.error(f"提取交易数据失败: {e}")
            return None
    
    def _check_wallet_balance(
        self,
        config: TradingConfig,
        tx_data: Dict[str, Any]
    ) -> TradingResult:
        """检查钱包余额。"""
        try:
            # 获取钱包地址
            wallet_address = self.wallet_manager.get_wallet_address(config.wallet_name)

            # 获取需要的金额
            required_amount = int(tx_data.get('value', 0))
            gas_limit = int(tx_data.get('gas', 21000))
            gas_price = int(tx_data.get('maxFeePerGas', tx_data.get('gasPrice', 0)))

            total_required = required_amount + (gas_limit * gas_price)

            # 获取链ID
            chain_id = tx_data.get('chainId')
            if not chain_id:
                return TradingResult(
                    success=False,
                    error_message="交易数据中缺少chainId"
                )

            # 检查实际余额
            try:
                rpc_url = self._get_rpc_url(chain_id)
                current_balance = self._get_wallet_balance(wallet_address, rpc_url)

                if current_balance < total_required:
                    return TradingResult(
                        success=False,
                        error_message=f"余额不足: 需要 {total_required / 1e18:.6f} ETH，当前 {current_balance / 1e18:.6f} ETH"
                    )

                # 检查是否保留足够的余额
                reserve_amount = int(current_balance * 0.1)  # 保留10%
                if current_balance - total_required < reserve_amount:
                    return TradingResult(
                        success=False,
                        error_message=f"余额不足（需保留10%）: 需要 {(total_required + reserve_amount) / 1e18:.6f} ETH，当前 {current_balance / 1e18:.6f} ETH"
                    )

                self.logger.info(f"余额检查通过: {current_balance / 1e18:.6f} ETH >= {total_required / 1e18:.6f} ETH")
                return TradingResult(success=True)

            except Exception as balance_error:
                self.logger.warning(f"无法获取实际余额，跳过检查: {balance_error}")
                # 如果无法获取余额，允许继续（但记录警告）
                return TradingResult(success=True)

        except Exception as e:
            return TradingResult(
                success=False,
                error_message=f"余额检查失败: {str(e)}"
            )

    def _get_wallet_balance(self, wallet_address: str, rpc_url: str) -> int:
        """获取钱包的ETH余额（wei单位）。"""
        try:
            from web3 import Web3

            web3 = Web3(Web3.HTTPProvider(rpc_url))

            if not web3.is_connected():
                raise Exception(f"无法连接到RPC节点: {rpc_url}")

            balance = web3.eth.get_balance(Web3.to_checksum_address(wallet_address))
            self.logger.debug(f"钱包 {wallet_address} 余额: {balance / 1e18:.6f} ETH")

            return balance

        except Exception as e:
            self.logger.error(f"获取钱包余额失败: {e}")
            raise
    
    def _execute_transaction(
        self,
        config: TradingConfig,
        tx_data: Dict[str, Any],
        quote: Quote
    ) -> TradingResult:
        """执行交易。"""
        try:
            # 获取链信息
            chain_id = tx_data.get('chainId')
            if not chain_id:
                return TradingResult(
                    success=False,
                    error_message="缺少链ID信息"
                )

            # 执行Relay协议的步骤化交易
            return self._execute_relay_steps(config, quote)

        except Exception as e:
            self.logger.error(f"交易执行失败: {e}")
            return TradingResult(
                success=False,
                error_message=f"交易执行失败: {str(e)}"
            )

    def _execute_relay_steps(self, config: TradingConfig, quote: Quote) -> TradingResult:
        """执行Relay协议的步骤化交易。"""
        try:
            self.logger.info(f"开始执行Relay步骤，共{len(quote.steps)}个步骤")

            executed_steps = []
            final_tx_hash = None

            for i, step in enumerate(quote.steps):
                self.logger.info(f"执行步骤 {i+1}/{len(quote.steps)}: {step.get('kind', 'unknown')}")

                step_result = self._execute_single_step(config, step, i)
                executed_steps.append(step_result)

                if not step_result.get('success', False):
                    return TradingResult(
                        success=False,
                        error_message=f"步骤 {i+1} 执行失败: {step_result.get('error', 'Unknown error')}"
                    )

                # 保存最后一个交易步骤的哈希
                if step.get('kind') == 'transaction' and step_result.get('tx_hash'):
                    final_tx_hash = step_result['tx_hash']

            self.logger.info(f"所有Relay步骤执行成功，最终交易哈希: {final_tx_hash}")

            return TradingResult(
                success=True,
                tx_hash=final_tx_hash,
                request_id=quote.steps[0].get('requestId') if quote.steps else None,
                amount_in=quote.details.currency_in.get('amountFormatted'),
                amount_out=quote.details.currency_out.get('amountFormatted')
            )

        except Exception as e:
            self.logger.error(f"Relay步骤执行失败: {e}")
            return TradingResult(
                success=False,
                error_message=f"Relay步骤执行失败: {str(e)}"
            )

    def _execute_single_step(self, config: TradingConfig, step: Dict[str, Any], step_index: int) -> Dict[str, Any]:
        """执行单个Relay步骤。"""
        step_kind = step.get('kind')
        step_id = step.get('id')

        if step_kind == 'signature':
            return self._execute_signature_step(config, step, step_index)
        elif step_kind == 'transaction':
            return self._execute_transaction_step(config, step, step_index)
        else:
            self.logger.warning(f"未知步骤类型: {step_kind}")
            return {'success': True, 'message': f'跳过未知步骤类型: {step_kind}'}

    def _execute_signature_step(self, config: TradingConfig, step: Dict[str, Any], step_index: int) -> Dict[str, Any]:
        """执行签名步骤。"""
        try:
            items = step.get('items', [])
            signatures = []

            for item in items:
                if item.get('status') == 'incomplete':
                    # 获取需要签名的数据
                    sign_data = item.get('data', {})

                    # 使用钱包签名
                    signature = self._sign_message(config.wallet_name, sign_data)
                    signatures.append(signature)

                    # 提交签名到Relay API
                    self._submit_signature_to_relay(step, item, signature)

            return {
                'success': True,
                'step_type': 'signature',
                'signatures': signatures
            }

        except Exception as e:
            self.logger.error(f"签名步骤执行失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _execute_transaction_step(self, config: TradingConfig, step: Dict[str, Any], step_index: int) -> Dict[str, Any]:
        """执行交易步骤。"""
        try:
            items = step.get('items', [])
            tx_hashes = []

            for item in items:
                if item.get('status') == 'incomplete':
                    # 获取交易数据
                    tx_data = item.get('data', {})

                    # 签名并提交交易
                    tx_hash = self._sign_and_submit_transaction(config, tx_data)
                    tx_hashes.append(tx_hash)

                    # 提交交易哈希到Relay API
                    self._submit_transaction_to_relay(step, item, tx_hash)

            return {
                'success': True,
                'step_type': 'transaction',
                'tx_hash': tx_hashes[-1] if tx_hashes else None,
                'tx_hashes': tx_hashes
            }

        except Exception as e:
            self.logger.error(f"交易步骤执行失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _sign_message(self, wallet_name: str, sign_data: Dict[str, Any]) -> str:
        """签名消息数据。"""
        try:
            # 获取私钥
            private_key = self.wallet_manager.get_private_key(wallet_name)

            # 根据签名类型处理
            sign_type = sign_data.get('type', 'message')
            message = sign_data.get('message', '')

            if sign_type == 'eip712':
                # EIP-712 结构化签名
                from eth_account import Account

                structured_data = sign_data.get('data', {})

                # 简化的EIP-712签名实现
                # 在实际环境中，这里应该使用完整的EIP-712编码
                message_str = json.dumps(structured_data, sort_keys=True)

                from eth_account.messages import encode_defunct
                encoded_message = encode_defunct(text=message_str)
                account = Account.from_key(private_key)
                signature = account.sign_message(encoded_message)
                return signature.signature.hex()

            else:
                # 普通消息签名 (EIP-191)
                from eth_account.messages import encode_defunct
                from eth_account import Account

                encoded_message = encode_defunct(text=message)
                account = Account.from_key(private_key)
                signature = account.sign_message(encoded_message)
                return signature.signature.hex()

        except Exception as e:
            self.logger.error(f"消息签名失败: {e}")
            raise

    def _submit_signature_to_relay(self, step: Dict[str, Any], item: Dict[str, Any], signature: str):
        """提交签名到Relay API。"""
        # 注意：根据Relay API文档，可能不需要单独提交签名
        # 签名通常包含在交易中一起提交
        self.logger.debug(f"签名已生成: {signature[:20]}...")
        # 暂时不提交签名，等待更多API文档确认

    def _sign_and_submit_transaction(self, config: TradingConfig, tx_data: Dict[str, Any]) -> str:
        """签名并提交交易到区块链。"""
        try:
            # 获取链ID
            chain_id = tx_data.get('chainId')
            if not chain_id:
                raise ValueError("交易数据中缺少chainId")

            # 获取RPC URL（这里需要配置管理）
            rpc_url = self._get_rpc_url(chain_id)

            # 使用交易签名器签名并提交
            tx_hash = self.signer.sign_and_submit_transaction(
                wallet_name=config.wallet_name,
                transaction_data=tx_data,
                rpc_url=rpc_url,
                chain_id=chain_id
            )

            self.logger.info(f"交易提交成功: {tx_hash}")
            return tx_hash

        except Exception as e:
            self.logger.error(f"交易签名和提交失败: {e}")
            raise

    def _submit_transaction_to_relay(self, step: Dict[str, Any], item: Dict[str, Any], tx_hash: str):
        """提交交易哈希到Relay API。"""
        try:
            # 从交易数据中获取链ID
            chain_id = item.get('data', {}).get('chainId')
            if not chain_id:
                self.logger.debug("交易数据中没有chainId，跳过交易哈希提交")
                return

            # 确保交易哈希有0x前缀
            if not tx_hash.startswith('0x'):
                tx_hash = '0x' + tx_hash

            # 构建提交数据 - 使用正确的Relay API格式
            submit_data = {
                'chainId': str(chain_id),
                'txHash': tx_hash
            }

            # 调用正确的Relay API端点
            endpoint = "/transactions/index"
            response = self.api_client._make_request("POST", endpoint, json=submit_data)

            self.logger.info(f"交易哈希提交成功: chainId={chain_id}, txHash={tx_hash}")
            self.logger.debug(f"Relay API响应: {response}")

        except Exception as e:
            self.logger.error(f"交易哈希提交失败: {e}")
            # 不抛出异常，因为这不应该影响交易的成功

    def _get_rpc_url(self, chain_id: int) -> str:
        """获取指定链的RPC URL。"""
        # 常用链的RPC URL映射
        rpc_urls = {
            1: "https://eth.llamarpc.com",  # Ethereum
            10: "https://mainnet.optimism.io",  # Optimism
            137: "https://polygon-rpc.com",  # Polygon
            42161: "https://arb1.arbitrum.io/rpc",  # Arbitrum
            8453: "https://mainnet.base.org",  # Base
            56: "https://bsc-dataseed.binance.org",  # BSC
            43114: "https://api.avax.network/ext/bc/C/rpc",  # Avalanche
            2741: "https://api.mainnet.abs.xyz",  # Abstract Mainnet
        }

        rpc_url = rpc_urls.get(chain_id)
        if not rpc_url:
            raise ValueError(f"不支持的链ID: {chain_id}")

        return rpc_url

    def monitor_pending_transactions(self):
        """监控待确认的交易状态。"""
        try:
            with self.storage.get_connection() as conn:
                # 获取所有待确认的交易
                cursor = conn.execute("""
                    SELECT id, tx_hash, wallet_name, monitor_pair_name, created_at
                    FROM transaction_history
                    WHERE status = 'submitted'
                    AND tx_hash IS NOT NULL
                    AND tx_hash != ''
                    ORDER BY created_at DESC
                    LIMIT 50
                """)

                pending_transactions = cursor.fetchall()
                processed_count = 0
                confirmed_count = 0
                failed_count = 0

                for tx_record in pending_transactions:
                    tx_id, tx_hash, wallet_name, pair_name, created_at = tx_record
                    processed_count += 1

                    try:
                        # 检查交易状态
                        status_result = self._check_and_update_transaction_status(tx_id, tx_hash, pair_name)
                        if status_result == 'confirmed':
                            confirmed_count += 1
                        elif status_result == 'failed':
                            failed_count += 1

                    except Exception as e:
                        self.logger.error(f"检查交易状态失败 {tx_hash}: {e}")

                        # 如果交易超过1小时仍未确认，标记为失败
                        from datetime import datetime, timedelta
                        created_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        if datetime.now(timezone.utc) - created_time > timedelta(hours=1):
                            self._mark_transaction_failed(tx_id, "交易超时未确认")
                            failed_count += 1

                if processed_count > 0:
                    status_summary = []
                    if confirmed_count > 0:
                        status_summary.append(f"{confirmed_count}个已确认")
                    if failed_count > 0:
                        status_summary.append(f"{failed_count}个失败")
                    pending_count = processed_count - confirmed_count - failed_count
                    if pending_count > 0:
                        status_summary.append(f"{pending_count}个仍待确认")

                    summary_text = ", ".join(status_summary) if status_summary else "状态无变化"
                    self.logger.info(f"处理了 {processed_count} 个交易: {summary_text}")
                else:
                    self.logger.debug("没有待确认的交易需要监控")

        except Exception as e:
            self.logger.error(f"监控待确认交易失败: {e}")

    def _check_and_update_transaction_status(self, tx_id: int, tx_hash: str, pair_name: str) -> Optional[str]:
        """检查并更新单个交易的状态。

        Returns:
            str: 更新后的状态 ('confirmed', 'failed', 'pending') 或 None 如果无法检查
        """
        try:
            # 获取交易对应的链ID
            chain_id = self._get_chain_id_from_pair(pair_name)
            if not chain_id:
                self.logger.warning(f"无法确定交易对 {pair_name} 的链ID")
                return None

            # 获取RPC URL
            rpc_url = self._get_rpc_url(chain_id)

            # 检查交易状态
            tx_status = self.signer.get_transaction_status(tx_hash, rpc_url)

            if tx_status['status'] == 'success':
                # 交易成功确认
                self._mark_transaction_confirmed(tx_id, tx_status)
                self.logger.info(f"交易确认成功: {tx_hash}")
                return 'confirmed'

            elif tx_status['status'] == 'failed':
                # 交易失败
                self._mark_transaction_failed(tx_id, "区块链交易失败")
                self.logger.warning(f"交易失败: {tx_hash}")
                return 'failed'

            # pending状态不需要更新，继续等待
            return 'pending'

        except Exception as e:
            self.logger.error(f"检查交易状态失败 {tx_hash}: {e}")
            raise

    def _get_chain_id_from_pair(self, pair_name: str) -> Optional[int]:
        """从交易对名称推断链ID。"""
        try:
            # 解析交易对名称，例如 "ARB_ETH_to_POLYGON_POL"
            parts = pair_name.split('_to_')
            if len(parts) != 2:
                return None

            origin_part = parts[0]  # "ARB_ETH"
            dest_part = parts[1]    # "POLYGON_POL"

            # 提取链名称（第一部分）
            origin_chain = origin_part.split('_')[0]  # "ARB"

            # 链名称到链ID的映射
            chain_mapping = {
                'ETH': 1,       # Ethereum
                'ETHEREUM': 1,
                'ARB': 42161,   # Arbitrum
                'ARBITRUM': 42161,
                'POLYGON': 137, # Polygon
                'POL': 137,
                'OP': 10,       # Optimism
                'OPTIMISM': 10,
                'BASE': 8453,   # Base
                'BSC': 56,      # BSC
                'AVAX': 43114,  # Avalanche
                'AVALANCHE': 43114,
                'ABSTRACT': 2741,  # Abstract Mainnet
            }

            return chain_mapping.get(origin_chain.upper())

        except Exception as e:
            self.logger.error(f"解析链ID失败 {pair_name}: {e}")
            return None

    def _mark_transaction_confirmed(self, tx_id: int, tx_status: Dict[str, Any]):
        """标记交易为已确认。"""
        try:
            with self.storage.get_connection() as conn:
                conn.execute("""
                    UPDATE transaction_history
                    SET status = 'confirmed',
                        confirmed_at = ?,
                        gas_used = ?,
                        gas_price = ?
                    WHERE id = ?
                """, (
                    datetime.now(timezone.utc).isoformat(),
                    tx_status.get('gas_used'),
                    str(tx_status.get('effective_gas_price', 0)),
                    tx_id
                ))
                conn.commit()

        except Exception as e:
            self.logger.error(f"标记交易确认失败: {e}")
            raise

    def _mark_transaction_failed(self, tx_id: int, error_message: str):
        """标记交易为失败。"""
        try:
            with self.storage.get_connection() as conn:
                conn.execute("""
                    UPDATE transaction_history
                    SET status = 'failed',
                        error_message = ?
                    WHERE id = ?
                """, (error_message, tx_id))
                conn.commit()

        except Exception as e:
            self.logger.error(f"标记交易失败失败: {e}")
            raise
    
    def _record_transaction(
        self,
        config: TradingConfig,
        pair: MonitorPair,
        quote: Quote,
        result: TradingResult,
        old_price: float,
        new_price: float
    ):
        """记录交易历史。"""
        try:
            wallet_address = self.wallet_manager.get_wallet_address(config.wallet_name)
            
            record = TransactionRecord(
                monitor_pair_name=pair.name,
                wallet_name=config.wallet_name,
                wallet_address=wallet_address,
                tx_hash=result.tx_hash or "",
                request_id=result.request_id,
                status=TradingStatus.SUBMITTED if result.success else TradingStatus.FAILED,
                amount_in=quote.details.currency_in.get('amountFormatted', '0'),
                token_in_symbol=quote.details.currency_in.get('currency', {}).get('symbol', ''),
                amount_out=quote.details.currency_out.get('amountFormatted', '0'),
                token_out_symbol=quote.details.currency_out.get('currency', {}).get('symbol', ''),
                trigger_price=new_price,
                total_fee_usd=quote.total_fee_usd,
                error_message=result.error_message,
                quote_data=quote.model_dump(),
                submitted_at=datetime.now(timezone.utc) if result.success else None
            )
            
            # 存储到数据库
            self._store_transaction_record(record)
            
        except Exception as e:
            self.logger.error(f"记录交易历史失败: {e}")
    
    def _get_trading_config(self, pair_name: str) -> Optional[TradingConfig]:
        """获取交易配置。"""
        try:
            with self.storage.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT * FROM trading_configs
                    WHERE monitor_pair_name = ? AND enabled = 1
                """, (pair_name,))

                row = cursor.fetchone()
                if row:
                    return TradingConfig(
                        id=row[0],
                        monitor_pair_name=row[1],
                        wallet_name=row[2],
                        enabled=bool(row[3]),
                        trading_amount=row[4],
                        max_slippage_percent=row[5],
                        daily_limit_usd=row[6],
                        single_trade_limit_usd=row[7],
                        min_price_change_percent=row[8],
                        cooldown_minutes=row[9]
                    )
                else:
                    # 如果数据库中没有配置，返回默认配置
                    self.logger.info(f"未找到交易配置 {pair_name}，使用默认配置")
                    return TradingConfig(
                        monitor_pair_name=pair_name,
                        wallet_name="default",
                        enabled=True,
                        trading_amount="1.0",
                        max_slippage_percent=5.0,
                        min_price_change_percent=5.0,
                        cooldown_minutes=60
                    )

        except Exception as e:
            self.logger.error(f"获取交易配置失败: {e}")
            return None
    
    def _get_last_trade(self, wallet_name: str) -> Optional[TransactionRecord]:
        """获取最后一次交易记录。"""
        try:
            with self.storage.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT * FROM transaction_history
                    WHERE wallet_name = ?
                    ORDER BY created_at DESC
                    LIMIT 1
                """, (wallet_name,))

                row = cursor.fetchone()
                if row:
                    return TransactionRecord(
                        id=row[0],
                        monitor_pair_name=row[1],
                        wallet_name=row[2],
                        wallet_address=row[3],
                        tx_hash=row[4],
                        request_id=row[5],
                        status=TradingStatus(row[6]),
                        amount_in=row[7],
                        token_in_symbol=row[8],
                        amount_out=row[9],
                        token_out_symbol=row[10],
                        gas_used=row[11],
                        gas_price=row[12],
                        total_fee_usd=row[13],
                        trigger_price=row[14],
                        execution_price=row[15],
                        slippage_percent=row[16],
                        error_message=row[17],
                        retry_count=row[18]
                    )
                return None

        except Exception as e:
            self.logger.error(f"获取最后交易记录失败: {e}")
            return None

    def _store_transaction_record(self, record: TransactionRecord):
        """存储交易记录到数据库。"""
        try:
            with self.storage.get_connection() as conn:
                conn.execute("""
                    INSERT INTO transaction_history (
                        monitor_pair_name, wallet_name, wallet_address, tx_hash, request_id,
                        status, amount_in, token_in_symbol, amount_out, token_out_symbol,
                        gas_used, gas_price, total_fee_usd, trigger_price, execution_price,
                        slippage_percent, error_message, retry_count, created_at, submitted_at,
                        confirmed_at, quote_data, transaction_data
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    record.monitor_pair_name,
                    record.wallet_name,
                    record.wallet_address,
                    record.tx_hash,
                    record.request_id,
                    record.status.value if hasattr(record.status, 'value') else record.status,
                    record.amount_in,
                    record.token_in_symbol,
                    record.amount_out,
                    record.token_out_symbol,
                    record.gas_used,
                    record.gas_price,
                    record.total_fee_usd,
                    record.trigger_price,
                    record.execution_price,
                    record.slippage_percent,
                    record.error_message,
                    record.retry_count,
                    record.created_at.isoformat(),
                    record.submitted_at.isoformat() if record.submitted_at else None,
                    record.confirmed_at.isoformat() if record.confirmed_at else None,
                    json.dumps(record.quote_data) if record.quote_data else None,
                    json.dumps(record.transaction_data) if record.transaction_data else None
                ))
                conn.commit()
                self.logger.info(f"交易记录已存储: {record.tx_hash}")

        except Exception as e:
            self.logger.error(f"存储交易记录失败: {e}")
            raise
    
    def _ensure_trading_tables(self):
        """确保交易相关表存在。"""
        try:
            with self.storage.get_connection() as conn:
                # 交易配置表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS trading_configs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        monitor_pair_name TEXT UNIQUE NOT NULL,
                        wallet_name TEXT NOT NULL,
                        enabled BOOLEAN DEFAULT 1,
                        trading_amount TEXT NOT NULL,
                        max_slippage_percent REAL DEFAULT 5.0,
                        daily_limit_usd REAL,
                        single_trade_limit_usd REAL,
                        min_price_change_percent REAL DEFAULT 5.0,
                        cooldown_minutes INTEGER DEFAULT 60,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 交易历史表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS transaction_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        monitor_pair_name TEXT NOT NULL,
                        wallet_name TEXT NOT NULL,
                        wallet_address TEXT NOT NULL,
                        tx_hash TEXT NOT NULL,
                        request_id TEXT,
                        status TEXT NOT NULL,
                        amount_in TEXT NOT NULL,
                        token_in_symbol TEXT NOT NULL,
                        amount_out TEXT NOT NULL,
                        token_out_symbol TEXT NOT NULL,
                        gas_used INTEGER,
                        gas_price TEXT,
                        total_fee_usd REAL,
                        trigger_price REAL NOT NULL,
                        execution_price REAL,
                        slippage_percent REAL,
                        error_message TEXT,
                        retry_count INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        submitted_at TIMESTAMP,
                        confirmed_at TIMESTAMP,
                        quote_data TEXT,
                        transaction_data TEXT
                    )
                """)
                
                conn.commit()
                self.logger.debug("交易相关表已确保存在")
                
        except Exception as e:
            self.logger.error(f"创建交易表失败: {e}")
            raise TradingError(f"创建交易表失败: {str(e)}")

    def _send_balance_insufficient_notification(self, config: TradingConfig, error_message: str):
        """发送余额不足通知。"""
        try:
            # 获取Bark配置
            runtime_config = RuntimeConfigManager(self.storage)
            bark_keys = runtime_config.get_bark_api_keys()

            if not bark_keys:
                self.logger.warning("Bark通知未配置，跳过余额不足通知")
                return

            # 构建通知数据
            alert_data = {
                'type': 'balance_insufficient',
                'title': '💰 钱包余额不足',
                'message': f'交易对: {config.monitor_pair_name}\n钱包: {config.wallet_name}\n错误: {error_message}\n\n请及时充值以确保自动交易正常运行。',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'pair_name': config.monitor_pair_name,
                'wallet_name': config.wallet_name,
                'error': error_message
            }

            # 使用现有的Bark通道发送通知
            bark_channel = BarkChannel(enabled=True, keys=bark_keys)
            success = bark_channel.send_alert(alert_data)

            if success:
                self.logger.info(f"已发送余额不足通知: {config.wallet_name}")
            else:
                self.logger.warning(f"余额不足通知发送失败: {config.wallet_name}")

        except Exception as e:
            self.logger.error(f"发送余额不足通知失败: {e}")

    def _send_trading_result_notification(self, config: TradingConfig, pair: MonitorPair, result: TradingResult, is_test: bool = False):
        """发送交易结果通知。"""
        try:
            # 获取Bark配置
            runtime_config = RuntimeConfigManager(self.storage)
            bark_keys = runtime_config.get_bark_api_keys()

            if not bark_keys:
                self.logger.debug("Bark通知未配置，跳过交易结果通知")
                return

            # 构建通知数据
            if result.success:
                title = f"{'🧪 测试' if is_test else '🎉'} 交易成功"
                message = f"""交易对: {pair.name}
钱包: {config.wallet_name}
输入: {result.amount_in} {pair.origin_token}
输出: {result.amount_out} {pair.destination_token}
交易哈希: {result.tx_hash}

{'这是一笔测试交易。' if is_test else '自动交易执行成功！'}"""
            else:
                title = f"{'🧪 测试' if is_test else '❌'} 交易失败"
                message = f"""交易对: {pair.name}
钱包: {config.wallet_name}
错误: {result.error_message}

{'测试交易失败，请检查配置。' if is_test else '自动交易执行失败，请检查系统状态。'}"""

            alert_data = {
                'type': 'trading_result',
                'title': title,
                'message': message,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'pair_name': pair.name,
                'wallet_name': config.wallet_name,
                'success': result.success,
                'is_test': is_test,
                'tx_hash': result.tx_hash if result.success else None,
                'error': result.error_message if not result.success else None
            }

            # 使用现有的Bark通道发送通知
            bark_channel = BarkChannel(enabled=True, keys=bark_keys)
            success = bark_channel.send_alert(alert_data)

            if success:
                self.logger.info(f"已发送交易结果通知: {pair.name} - {'成功' if result.success else '失败'}")
            else:
                self.logger.warning(f"交易结果通知发送失败: {pair.name}")

        except Exception as e:
            self.logger.error(f"发送交易结果通知失败: {e}")

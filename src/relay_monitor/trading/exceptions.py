"""
交易执行模块的异常类。
"""


class TradingError(Exception):
    """交易执行相关错误的基础异常类。"""
    pass


class InsufficientBalanceError(TradingError):
    """余额不足错误。"""
    
    def __init__(self, required_amount: str, available_amount: str, token_symbol: str):
        super().__init__(
            f"余额不足: 需要 {required_amount} {token_symbol}, "
            f"可用 {available_amount} {token_symbol}"
        )
        self.required_amount = required_amount
        self.available_amount = available_amount
        self.token_symbol = token_symbol


class TradingConditionError(TradingError):
    """交易条件不满足错误。"""
    pass


class SlippageExceededError(TradingError):
    """滑点超出限制错误。"""
    
    def __init__(self, actual_slippage: float, max_slippage: float):
        super().__init__(
            f"滑点超出限制: 实际 {actual_slippage:.2f}%, "
            f"最大允许 {max_slippage:.2f}%"
        )
        self.actual_slippage = actual_slippage
        self.max_slippage = max_slippage


class TradingLimitExceededError(TradingError):
    """交易限额超出错误。"""
    pass


class TransactionFailedError(TradingError):
    """交易失败错误。"""
    
    def __init__(self, tx_hash: str, reason: str):
        super().__init__(f"交易失败 {tx_hash}: {reason}")
        self.tx_hash = tx_hash
        self.reason = reason


class RiskControlError(TradingError):
    """风险控制错误。"""
    pass

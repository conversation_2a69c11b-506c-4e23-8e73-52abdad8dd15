"""
Relay Monitor 的维护任务。

提供自动化维护任务，包括：
- 数据库清理和备份
- 日志文件清理
- 系统健康监控
- 性能优化
"""

import os
import glob
import logging
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional

from ..config.models import Config
from ..storage.database import DataStorage


logger = logging.getLogger(__name__)


class MaintenanceManager:
    """
    Manages automated maintenance tasks for the Relay Monitor system.
    
    Features:
    - Database cleanup and backup
    - Log file rotation and cleanup
    - System health monitoring
    - Performance optimization
    - Configurable schedules
    """
    
    def __init__(self, config: Config, storage: DataStorage):
        """
        Initialize maintenance manager.
        
        Args:
            config: Application configuration
            storage: Database storage instance
        """
        self.config = config
        self.storage = storage
        self.logger = logger
        
        # Maintenance statistics
        self.stats = {
            'last_database_cleanup': None,
            'last_log_cleanup': None,
            'database_cleanups_performed': 0,
            'log_cleanups_performed': 0,
            'total_data_cleaned_mb': 0
        }
    
    def run_database_cleanup(self) -> Dict[str, Any]:
        """
        Clean up old database records based on configuration.
        
        Returns:
            Dictionary with cleanup results
        """
        self.logger.info("Starting database cleanup...")
        
        try:
            # Get database size before cleanup
            db_stats_before = self.storage.get_database_stats()
            size_before_mb = db_stats_before.get('db_size_mb', 0)
            
            # Perform cleanup
            max_history_days = self.config.monitoring.max_history_days
            self.storage.cleanup_old_data(days=max_history_days)
            
            # Get database size after cleanup
            db_stats_after = self.storage.get_database_stats()
            size_after_mb = db_stats_after.get('db_size_mb', 0)
            
            # Calculate space saved
            space_saved_mb = max(0, size_before_mb - size_after_mb)
            
            # Update statistics
            self.stats['last_database_cleanup'] = datetime.now()
            self.stats['database_cleanups_performed'] += 1
            self.stats['total_data_cleaned_mb'] += space_saved_mb
            
            result = {
                'success': True,
                'timestamp': datetime.now(),
                'max_history_days': max_history_days,
                'size_before_mb': size_before_mb,
                'size_after_mb': size_after_mb,
                'space_saved_mb': space_saved_mb,
                'records_before': {
                    'price_history': db_stats_before.get('price_history_count', 0),
                    'alert_history': db_stats_before.get('alert_history_count', 0),
                    'system_stats': db_stats_before.get('system_stats_count', 0)
                },
                'records_after': {
                    'price_history': db_stats_after.get('price_history_count', 0),
                    'alert_history': db_stats_after.get('alert_history_count', 0),
                    'system_stats': db_stats_after.get('system_stats_count', 0)
                }
            }
            
            self.logger.info(f"Database cleanup completed: "
                           f"saved {space_saved_mb:.2f}MB, "
                           f"cleaned data older than {max_history_days} days")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Database cleanup failed: {e}")
            return {
                'success': False,
                'timestamp': datetime.now(),
                'error': str(e)
            }
    

    
    def run_log_cleanup(self) -> Dict[str, Any]:
        """
        Clean up old log files.
        
        Returns:
            Dictionary with cleanup results
        """
        if not self.config.logging.cleanup_enabled:
            self.logger.debug("Log cleanup is disabled")
            return {
                'success': False,
                'timestamp': datetime.now(),
                'reason': 'cleanup_disabled'
            }
        
        self.logger.info("Starting log cleanup...")
        
        try:
            log_dir = os.path.dirname(self.config.logging.file)
            if not log_dir:
                log_dir = "."
            
            # Find old log files
            max_age_days = self.config.logging.max_log_age_days
            cutoff_date = datetime.now() - timedelta(days=max_age_days)
            
            # Pattern for rotated log files
            log_pattern = f"{self.config.logging.file}.*"
            old_files = []
            total_size_cleaned = 0
            
            for log_file in glob.glob(log_pattern):
                try:
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(log_file))
                    if file_mtime < cutoff_date:
                        file_size = os.path.getsize(log_file)
                        old_files.append({
                            'path': log_file,
                            'size_mb': file_size / (1024 * 1024),
                            'modified': file_mtime
                        })
                        total_size_cleaned += file_size
                        os.remove(log_file)
                        self.logger.debug(f"Removed old log file: {log_file}")
                except Exception as e:
                    self.logger.warning(f"Failed to process log file {log_file}: {e}")
            
            # Update statistics
            self.stats['last_log_cleanup'] = datetime.now()
            self.stats['log_cleanups_performed'] += 1
            
            result = {
                'success': True,
                'timestamp': datetime.now(),
                'max_age_days': max_age_days,
                'files_removed': len(old_files),
                'total_size_cleaned_mb': total_size_cleaned / (1024 * 1024),
                'removed_files': old_files
            }
            
            self.logger.info(f"Log cleanup completed: "
                           f"removed {len(old_files)} files, "
                           f"freed {total_size_cleaned / (1024 * 1024):.2f}MB")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Log cleanup failed: {e}")
            return {
                'success': False,
                'timestamp': datetime.now(),
                'error': str(e)
            }
    

    
    def get_maintenance_stats(self) -> Dict[str, Any]:
        """Get maintenance statistics."""
        return self.stats.copy()
    
    def should_run_database_cleanup(self) -> bool:
        """Check if database cleanup should run."""
        if not self.config.database.cleanup_enabled:
            return False
        
        last_cleanup = self.stats.get('last_database_cleanup')
        if not last_cleanup:
            return True
        
        interval_hours = self.config.database.cleanup_interval_hours
        next_cleanup = last_cleanup + timedelta(hours=interval_hours)
        return datetime.now() >= next_cleanup
    

    
    def should_run_log_cleanup(self) -> bool:
        """Check if log cleanup should run."""
        if not self.config.logging.cleanup_enabled:
            return False
        
        last_cleanup = self.stats.get('last_log_cleanup')
        if not last_cleanup:
            return True
        
        interval_hours = self.config.logging.cleanup_interval_hours
        next_cleanup = last_cleanup + timedelta(hours=interval_hours)
        return datetime.now() >= next_cleanup

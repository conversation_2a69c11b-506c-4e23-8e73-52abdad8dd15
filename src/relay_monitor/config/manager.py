"""
Configuration manager for loading and managing TOML configuration files.
"""

import os
import logging
from pathlib import Path
from typing import Optional

import toml
from pydantic import ValidationError

from .models import Config
from .runtime import RuntimeConfigManager
from ..storage.database import DataStorage


logger = logging.getLogger(__name__)


class ConfigManager:
    """
    Manages loading and validation of TOML configuration files.
    
    Supports:
    - Loading from multiple config file locations
    - Environment variable overrides
    - Configuration validation
    - Hot reloading
    """
    
    def __init__(self, config_path: Optional[str] = None, storage: Optional[DataStorage] = None):
        """
        初始化配置管理器。

        Args:
            config_path: 配置文件路径。如果为 None，将搜索配置文件。
            storage: 运行时配置的数据库存储。如果为 None，则禁用运行时配置。
        """
        self.config_path = config_path
        self.config: Optional[Config] = None
        self._last_modified = 0
        self.storage = storage
        self.runtime_config = RuntimeConfigManager(storage) if storage else None

        # Default config file search paths
        self.search_paths = [
            "config/config.local.toml",  # Local override
            "config/config.toml",        # Default config
            "config/config.minimal.toml", # Minimal config for first-time setup
            "relay_monitor.toml",        # Root directory
            os.path.expanduser("~/.relay_monitor.toml"),  # User home
        ]
    
    def find_config_file(self) -> str:
        """
        Find the configuration file to use.
        
        Returns:
            Path to the configuration file
            
        Raises:
            FileNotFoundError: If no configuration file is found
        """
        if self.config_path:
            if os.path.exists(self.config_path):
                return self.config_path
            else:
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        
        # Search for config files in order of preference
        for path in self.search_paths:
            if os.path.exists(path):
                logger.info(f"Using configuration file: {path}")
                return path
        
        raise FileNotFoundError(
            f"No configuration file found. Searched: {', '.join(self.search_paths)}"
        )
    
    def load_config(self, force_reload: bool = False) -> Config:
        """
        Load configuration from TOML file.
        
        Args:
            force_reload: Force reload even if config is already loaded
            
        Returns:
            Loaded and validated configuration
        """
        config_file = self.find_config_file()
        
        # Check if we need to reload
        if not force_reload and self.config:
            current_modified = os.path.getmtime(config_file)
            if current_modified <= self._last_modified:
                return self.config
        
        logger.info(f"Loading configuration from {config_file}")
        
        try:
            # 加载 TOML 文件
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = toml.load(f)
            
            # 应用环境变量覆盖
            config_data = self._apply_env_overrides(config_data)

            # 应用运行时配置覆盖
            config_data = self._apply_runtime_overrides(config_data)

            # 验证并创建配置对象
            self.config = Config(**config_data)
            self._last_modified = os.path.getmtime(config_file)
            
            logger.info("Configuration loaded successfully")
            # Note: Monitor pairs are now loaded from database, not config file
            
            return self.config
            
        except toml.TomlDecodeError as e:
            raise ValueError(f"Invalid TOML syntax in {config_file}: {e}")
        except ValidationError as e:
            raise ValueError(f"Configuration validation failed: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to load configuration: {e}")
    
    def _apply_env_overrides(self, config_data: dict) -> dict:
        """
        Apply environment variable overrides to configuration.
        
        Environment variables should be prefixed with RELAY_MONITOR_ and use
        double underscores to separate nested keys.
        
        Example: RELAY_MONITOR_API__BASE_URL=https://custom.api.url
        """
        prefix = "RELAY_MONITOR_"
        
        for key, value in os.environ.items():
            if not key.startswith(prefix):
                continue
            
            # Remove prefix and convert to lowercase
            config_key = key[len(prefix):].lower()
            
            # Split nested keys
            keys = config_key.split('__')
            
            # Navigate to the correct nested dictionary
            current = config_data
            for k in keys[:-1]:
                if k not in current:
                    current[k] = {}
                current = current[k]
            
            # Set the value (try to convert to appropriate type)
            final_key = keys[-1]
            current[final_key] = self._convert_env_value(value)
            
            logger.debug(f"Applied environment override: {config_key} = {value}")
        
        return config_data
    
    def _convert_env_value(self, value: str):
        """Convert environment variable string to appropriate type."""
        # Boolean conversion
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # Integer conversion
        try:
            return int(value)
        except ValueError:
            pass
        
        # Float conversion
        try:
            return float(value)
        except ValueError:
            pass
        
        # Return as string
        return value
    
    def get_config(self) -> Config:
        """Get current configuration, loading if necessary."""
        if self.config is None:
            return self.load_config()
        return self.config
    
    def reload_if_changed(self) -> bool:
        """
        Reload configuration if the file has been modified.
        
        Returns:
            True if configuration was reloaded, False otherwise
        """
        try:
            config_file = self.find_config_file()
            current_modified = os.path.getmtime(config_file)
            
            if current_modified > self._last_modified:
                self.load_config(force_reload=True)
                logger.info("Configuration reloaded due to file changes")
                return True
                
        except Exception as e:
            logger.error(f"Failed to check for configuration changes: {e}")
        
        return False
    
    def validate_config(self, config_path: Optional[str] = None) -> bool:
        """
        Validate configuration file without loading it.
        
        Args:
            config_path: Path to config file to validate
            
        Returns:
            True if configuration is valid
        """
        try:
            if config_path:
                old_path = self.config_path
                self.config_path = config_path
                
            config_file = self.find_config_file()
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = toml.load(f)
            
            Config(**config_data)
            logger.info(f"Configuration file {config_file} is valid")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False
        finally:
            if config_path:
                self.config_path = old_path
    
    def create_example_config(self, output_path: str = "config/config.example.toml"):
        """Create an example configuration file."""
        example_config = Config()
        
        # Convert to dict and then to TOML
        config_dict = example_config.dict()
        
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            toml.dump(config_dict, f)
        
        logger.info(f"Example configuration created at {output_path}")

    def save_config(self, config: Config) -> bool:
        """
        将配置保存到 TOML 文件。

        Args:
            config: 要保存的配置对象

        Returns:
            如果保存成功返回 True，否则返回 False
        """
        try:
            config_file = self.find_config_file()
            temp_file = f"{config_file}.tmp"

            # 将配置转换为字典
            config_dict = config.dict()

            # 首先写入临时文件（原子操作）
            with open(temp_file, 'w', encoding='utf-8') as f:
                toml.dump(config_dict, f)

            # 原子重命名
            os.replace(temp_file, config_file)

            # 更新修改时间
            self._last_modified = os.path.getmtime(config_file)

            logger.info(f"Configuration saved to {config_file}")
            return True

        except Exception as e:
            # Clean up temporary file if it exists
            temp_file = f"{config_file}.tmp"
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except OSError as e:
                    logger.warning(f"Failed to remove temporary file {temp_file}: {e}")

            logger.error(f"Failed to save configuration: {e}")
            return False

    def update_section(self, section: str, data: dict) -> bool:
        """
        Update a specific section of the configuration.

        Args:
            section: Configuration section name (e.g., 'monitoring', 'database', 'logging', 'api', 'admin')
            data: Dictionary containing the new values for the section

        Returns:
            True if update was successful, False otherwise
        """
        try:
            if not self.config:
                self.load_config()

            # Get the current configuration as a dictionary
            config_dict = self.config.dict()

            # Update the specific section
            if section in config_dict:
                config_dict[section].update(data)
            else:
                logger.error(f"Configuration section '{section}' not found")
                return False

            # Validate the updated configuration
            updated_config = Config(**config_dict)

            # Save the updated configuration
            if self.save_config(updated_config):
                self.config = updated_config
                logger.info(f"Configuration section '{section}' updated successfully")
                return True
            else:
                logger.error(f"Failed to save updated configuration for section '{section}'")
                return False

        except Exception as e:
            logger.error(f"Failed to update configuration section '{section}': {e}")
            return False

    def get_config(self) -> Config:
        """
        Get the current configuration.

        Returns:
            Current configuration object
        """
        if not self.config:
            self.load_config()
        return self.config

    def _apply_runtime_overrides(self, config_data: dict) -> dict:
        """
        Apply runtime configuration overrides from database.

        Runtime configuration has higher priority than file configuration.

        Args:
            config_data: Configuration data from file

        Returns:
            Configuration data with runtime overrides applied
        """
        if not self.runtime_config:
            return config_data

        try:
            # Override admin password if set in runtime config
            if self.runtime_config.has_admin_password():
                if 'admin' not in config_data:
                    config_data['admin'] = {}
                # Set a placeholder - actual verification will use runtime config
                config_data['admin']['password'] = '__RUNTIME_CONFIG__'

            # Override Bark API keys
            bark_keys = self.runtime_config.get_bark_api_keys()
            if bark_keys:
                if 'alerts' not in config_data:
                    config_data['alerts'] = {}
                if 'bark' not in config_data['alerts']:
                    config_data['alerts']['bark'] = {}
                config_data['alerts']['bark']['enabled'] = True
                config_data['alerts']['bark']['keys'] = bark_keys

            # Override SMTP API key
            smtp_key = self.runtime_config.get_smtp_api_key()
            if smtp_key:
                if 'alerts' not in config_data:
                    config_data['alerts'] = {}
                if 'smtp_dev' not in config_data['alerts']:
                    config_data['alerts']['smtp_dev'] = {}
                config_data['alerts']['smtp_dev']['enabled'] = True
                config_data['alerts']['smtp_dev']['api_key'] = smtp_key

            # Note: Monitor pairs are now completely managed in database
            # No need to load them into config object
            config_data['monitor_pairs'] = []  # Always empty, database is the source of truth

            logger.debug("Applied runtime configuration overrides")

        except Exception as e:
            logger.warning(f"Failed to apply runtime overrides: {e}")

        return config_data



    def get_runtime_config_manager(self) -> Optional[RuntimeConfigManager]:
        """
        Get runtime configuration manager.

        Returns:
            Runtime configuration manager or None if not available
        """
        return self.runtime_config

    def is_first_setup(self) -> bool:
        """
        Check if this is the first setup.

        Returns:
            True if this is the first setup (no admin password configured)
        """
        if not self.runtime_config:
            # No fallback to file-based config for security
            logger.warning("Runtime configuration not available - assuming first setup")
            return True

        return self.runtime_config.is_first_setup()

    def setup_initial_config(self, admin_password: str, bark_keys: list = None) -> bool:
        """
        Setup initial configuration for first-time users.

        Args:
            admin_password: Admin password to set
            bark_keys: Optional Bark API keys

        Returns:
            True if setup was successful
        """
        if not self.runtime_config:
            logger.error("Runtime configuration not available for initial setup")
            return False

        try:
            # Set admin password
            if not self.runtime_config.set_admin_password(admin_password):
                return False

            # Set Bark keys if provided
            if bark_keys:
                self.runtime_config.set_bark_api_keys(bark_keys)

            # Try to migrate any existing file configuration
            try:
                config_file = self.find_config_file()
                with open(config_file, 'r', encoding='utf-8') as f:
                    file_config = toml.load(f)
                self.runtime_config.migrate_from_file_config(file_config)
            except Exception as e:
                logger.warning(f"Could not migrate file configuration: {e}")

            logger.info("Initial configuration setup completed successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to setup initial configuration: {e}")
            return False

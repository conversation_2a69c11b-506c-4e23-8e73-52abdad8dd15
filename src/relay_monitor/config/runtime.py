"""
Runtime configuration manager for Relay Monitor.

Manages configuration that can be changed at runtime through the web interface,
stored in the database with higher priority than file-based configuration.
"""

import logging
import bcrypt
from typing import Optional, Dict, Any, List
from ..storage.database import DataStorage
from ..storage.models import RuntimeConfigRecord

logger = logging.getLogger(__name__)


class RuntimeConfigManager:
    """
    Manages runtime configuration stored in database.
    
    Features:
    - Password encryption/verification
    - Type-safe configuration access
    - Configuration validation
    - Migration from file-based config
    """
    
    def __init__(self, storage: DataStorage):
        """
        Initialize runtime configuration manager.
        
        Args:
            storage: Database storage instance
        """
        self.storage = storage
        self._config_cache = {}
        self._cache_dirty = True
    
    def _refresh_cache(self):
        """Refresh configuration cache from database."""
        if self._cache_dirty:
            configs = self.storage.get_all_runtime_configs()
            self._config_cache = {config.config_key: config for config in configs}
            self._cache_dirty = False
    
    def _invalidate_cache(self):
        """Invalidate configuration cache."""
        self._cache_dirty = True
    
    # =============================================================================
    # Password Management
    # =============================================================================
    
    def set_admin_password(self, password: str) -> bool:
        """
        Set admin password with encryption and strength validation.

        Args:
            password: Plain text password

        Returns:
            True if password was set successfully
        """
        try:
            # Validate password strength
            from ..web.auth import validate_password_strength
            is_valid, error_message = validate_password_strength(password)
            if not is_valid:
                logger.warning(f"Password strength validation failed: {error_message}")
                return False

            # Hash password with bcrypt
            salt = bcrypt.gensalt()
            hashed = bcrypt.hashpw(password.encode('utf-8'), salt)

            # Store in database
            success = self.storage.set_runtime_config_value(
                'admin_password_hash',
                hashed.decode('utf-8'),
                'string',
                'Encrypted admin password'
            )

            if success:
                self._invalidate_cache()
                logger.info("Admin password updated successfully")

            return success
        except Exception as e:
            logger.error(f"Failed to set admin password: {e}")
            return False
    
    def verify_admin_password(self, password: str) -> bool:
        """
        Verify admin password.
        
        Args:
            password: Plain text password to verify
            
        Returns:
            True if password is correct
        """
        try:
            password_hash = self.get_config_value('admin_password_hash')
            if not password_hash:
                return False
            
            return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
        except Exception as e:
            logger.error(f"Failed to verify admin password: {e}")
            return False
    
    def has_admin_password(self) -> bool:
        """
        Check if admin password is set.
        
        Returns:
            True if admin password is configured
        """
        password_hash = self.get_config_value('admin_password_hash')
        return password_hash is not None and password_hash.strip() != ''
    
    # =============================================================================
    # Alert Configuration
    # =============================================================================
    
    def set_bark_api_keys(self, keys: List[str]) -> bool:
        """
        Set Bark API keys.
        
        Args:
            keys: List of Bark API keys
            
        Returns:
            True if keys were set successfully
        """
        try:
            return self.storage.set_runtime_config_value(
                'bark_api_keys',
                keys,
                'json',
                'Bark push notification API keys'
            )
        except Exception as e:
            logger.error(f"Failed to set Bark API keys: {e}")
            return False
    
    def get_bark_api_keys(self) -> List[str]:
        """
        Get Bark API keys.
        
        Returns:
            List of Bark API keys
        """
        keys = self.get_config_value('bark_api_keys', [])
        return keys if isinstance(keys, list) else []
    
    def set_smtp_api_key(self, api_key: str) -> bool:
        """
        Set SMTP.dev API key.
        
        Args:
            api_key: SMTP.dev API key
            
        Returns:
            True if key was set successfully
        """
        try:
            return self.storage.set_runtime_config_value(
                'smtp_api_key',
                api_key,
                'string',
                'SMTP.dev API key for email notifications'
            )
        except Exception as e:
            logger.error(f"Failed to set SMTP API key: {e}")
            return False
    
    def get_smtp_api_key(self) -> Optional[str]:
        """
        Get SMTP.dev API key.
        
        Returns:
            SMTP.dev API key or None
        """
        return self.get_config_value('smtp_api_key')
    
    # =============================================================================
    # General Configuration Access
    # =============================================================================
    
    def get_config_value(self, key: str, default_value: Any = None) -> Any:
        """
        Get configuration value by key.
        
        Args:
            key: Configuration key
            default_value: Default value if key not found
            
        Returns:
            Configuration value
        """
        return self.storage.get_runtime_config_value(key, default_value)
    
    def set_config_value(self, key: str, value: Any, config_type: str = 'string', description: str = None) -> bool:
        """
        Set configuration value.
        
        Args:
            key: Configuration key
            value: Configuration value
            config_type: Value type ('string', 'number', 'boolean', 'json')
            description: Configuration description
            
        Returns:
            True if value was set successfully
        """
        success = self.storage.set_runtime_config_value(key, value, config_type, description)
        if success:
            self._invalidate_cache()
        return success
    
    def get_all_configs(self) -> Dict[str, Any]:
        """
        Get all runtime configurations as a dictionary.
        
        Returns:
            Dictionary of all configurations
        """
        self._refresh_cache()
        result = {}
        for key, config in self._config_cache.items():
            result[key] = self.storage.get_runtime_config_value(key)
        return result
    
    def delete_config(self, key: str) -> bool:
        """
        Delete configuration by key.
        
        Args:
            key: Configuration key to delete
            
        Returns:
            True if configuration was deleted
        """
        success = self.storage.delete_runtime_config(key)
        if success:
            self._invalidate_cache()
        return success
    
    # =============================================================================
    # Setup and Migration
    # =============================================================================
    
    def is_first_setup(self) -> bool:
        """
        Check if this is the first setup (no admin password configured).
        
        Returns:
            True if this is the first setup
        """
        return not self.has_admin_password()
    
    def migrate_from_file_config(self, file_config: Dict[str, Any]) -> bool:
        """
        Migrate configuration from file-based config to runtime config.
        
        Args:
            file_config: Configuration dictionary from file
            
        Returns:
            True if migration was successful
        """
        try:
            migrated_count = 0
            
            # Migrate admin password if present
            admin_config = file_config.get('admin', {})
            if admin_config.get('password') and admin_config['password'] != 'admin123!!.':
                if self.set_admin_password(admin_config['password']):
                    migrated_count += 1
                    logger.info("Migrated admin password from file config")
            
            # Migrate alert configurations
            alerts_config = file_config.get('alerts', {})
            
            # Migrate Bark keys
            bark_config = alerts_config.get('bark', {})
            if bark_config.get('enabled') and bark_config.get('keys'):
                keys = bark_config['keys']
                if isinstance(keys, str):
                    keys = [k.strip() for k in keys.split(',') if k.strip()]
                if keys and self.set_bark_api_keys(keys):
                    migrated_count += 1
                    logger.info("Migrated Bark API keys from file config")
            
            # Migrate SMTP.dev key
            smtp_config = alerts_config.get('smtp_dev', {})
            if smtp_config.get('enabled') and smtp_config.get('api_key'):
                if self.set_smtp_api_key(smtp_config['api_key']):
                    migrated_count += 1
                    logger.info("Migrated SMTP API key from file config")
            
            logger.info(f"Migration completed: {migrated_count} configurations migrated")
            return True
            
        except Exception as e:
            logger.error(f"Failed to migrate configuration: {e}")
            return False

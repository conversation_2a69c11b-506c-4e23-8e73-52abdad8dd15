"""
使用 Pydantic 进行验证的配置数据模型。
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, validator


class APIConfig(BaseModel):
    """API 配置设置。"""

    base_url: str = "https://api.relay.link"
    timeout: int = 30
    retry_attempts: int = 3
    retry_delay: float = 1.0
    cache_ttl: int = 1800  # 增加到30分钟，减少频繁的token加载日志


class MonitorPair(BaseModel):
    """监控对的配置。"""

    name: str
    description: str
    origin_chain: str
    destination_chain: str
    origin_token: str
    destination_token: str
    amount: str
    enabled: bool = True
    alert_threshold_percent: float = 5.0

    # 自动交易配置
    auto_trading_enabled: bool = False
    wallet_name: Optional[str] = None
    trading_amount: Optional[str] = None
    max_slippage_percent: float = 5.0
    min_price_change_percent: float = 5.0
    cooldown_minutes: int = 60
    daily_limit_usd: Optional[float] = None
    single_trade_limit_usd: Optional[float] = None

    @validator('amount')
    def validate_amount(cls, v):
        """Validate amount is a positive number."""
        try:
            amount = float(v)
            if amount <= 0:
                raise ValueError("Amount must be positive")
            return v
        except ValueError:
            raise ValueError("Amount must be a valid number")

    @validator('trading_amount')
    def validate_trading_amount(cls, v):
        """Validate trading amount if provided."""
        if v is not None:
            try:
                amount = float(v)
                if amount <= 0:
                    raise ValueError("Trading amount must be positive")
            except ValueError:
                raise ValueError("Trading amount must be a valid number")
        return v


class MonitoringConfig(BaseModel):
    """监控配置设置。"""

    interval_seconds: int = 300
    price_change_threshold_percent: float = 5.0
    enabled: bool = True
    max_history_days: int = 15


class ConsoleAlertConfig(BaseModel):
    """控制台警报配置。"""
    
    enabled: bool = True


class EmailAlertConfig(BaseModel):
    """邮件警报配置。"""
    
    enabled: bool = False
    smtp_server: str = ""
    smtp_port: int = 587
    username: str = ""
    password: str = ""
    from_email: str = ""
    to_emails: List[str] = []


class WebhookAlertConfig(BaseModel):
    """Webhook 警报配置。"""

    enabled: bool = False
    url: str = ""
    headers: Dict[str, str] = {}


class SMTPDevAlertConfig(BaseModel):
    """SMTP.dev 警报配置。"""

    enabled: bool = False
    api_key: str = ""
    to_emails: List[str] = []
    from_email: str = ""


class BarkAlertConfig(BaseModel):
    """Bark iOS 推送通知配置。"""

    enabled: bool = False
    keys: List[str] = []  # 支持多个Bark Key
    server_url: str = "https://api.day.app"
    timeout: int = 10

    # 向后兼容：如果只有一个key，也支持单个key字段
    @property
    def key(self) -> str:
        """向后兼容的单个key属性"""
        return self.keys[0] if self.keys else ""

    @key.setter
    def key(self, value: str):
        """向后兼容的单个key设置"""
        if value:
            self.keys = [value]
        else:
            self.keys = []


class AlertsConfig(BaseModel):
    """警报配置设置。"""

    enabled: bool = True
    rate_limit_minutes: float = 5
    console: ConsoleAlertConfig = ConsoleAlertConfig()
    email: EmailAlertConfig = EmailAlertConfig()
    webhook: WebhookAlertConfig = WebhookAlertConfig()
    smtp_dev: SMTPDevAlertConfig = SMTPDevAlertConfig()
    bark: BarkAlertConfig = BarkAlertConfig()


class DatabaseConfig(BaseModel):
    """数据库配置设置。"""

    path: str = "data/relay_monitor.db"
    cleanup_enabled: bool = True
    cleanup_interval_hours: int = 6


class WebConfig(BaseModel):
    """Web 界面配置设置。"""
    
    host: str = "127.0.0.1"
    port: int = 5000
    debug: bool = False


class LoggingConfig(BaseModel):
    """日志配置设置。"""

    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: str = "logs/relay_monitor.log"
    max_file_size_mb: int = 10
    backup_count: int = 5
    cleanup_enabled: bool = True
    cleanup_interval_hours: int = 24
    max_log_age_days: int = 7


class CacheConfig(BaseModel):
    """缓存配置设置。"""

    chains_ttl: int = 3600
    tokens_ttl: int = 3600
    refresh_on_startup: bool = True


class AdminConfig(BaseModel):
    """管理面板配置设置。"""

    enabled: bool = True
    password: str = ""  # Empty by default, will be set through runtime config
    session_timeout_minutes: int = 60
    max_login_attempts: int = 5
    lockout_duration_minutes: int = 15


class Config(BaseModel):
    """主配置模型。"""

    api: APIConfig = APIConfig()
    monitoring: MonitoringConfig = MonitoringConfig()
    # 注意：monitor_pairs 已移除，现在完全使用数据库存储交易对配置
    alerts: AlertsConfig = AlertsConfig()
    database: DatabaseConfig = DatabaseConfig()
    web: WebConfig = WebConfig()
    logging: LoggingConfig = LoggingConfig()
    cache: CacheConfig = CacheConfig()
    admin: AdminConfig = AdminConfig()

    def get_enabled_pairs(self) -> List[MonitorPair]:
        """
        获取启用的监控对。

        注意：此方法已弃用，请直接使用 DataStorage.get_enabled_monitor_pairs()
        从数据库获取交易对配置。
        """
        # 返回空列表，因为交易对配置现在完全存储在数据库中
        return []

{% extends "base.html" %}

{% block title %}警报历史 - Relay Monitor{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1><i class="bi bi-bell"></i> 警报历史</h1>
        <p class="text-muted">历史价格变化和系统警报</p>
    </div>
</div>

<!-- Alerts List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="bi bi-list-ul"></i> 最近警报</h5>
                <div>
                    {% if has_prev %}
                        <a href="{{ url_for('main.alerts_page', page=page-1) }}" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-arrow-left"></i> 上一页
                        </a>
                    {% endif %}
                    {% if has_next %}
                        <a href="{{ url_for('main.alerts_page', page=page+1) }}" class="btn btn-sm btn-outline-primary">
                            下一页 <i class="bi bi-arrow-right"></i>
                        </a>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                {% if alerts %}
                    <div class="list-group list-group-flush">
                        {% for alert in alerts %}
                        <div class="list-group-item alert-item {% if alert.alert_type == 'price_change' %}alert-price-change{% else %}alert-system{% endif %}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-2">
                                        {% if alert.alert_type == 'price_change' %}
                                            <i class="bi bi-graph-up text-warning me-2"></i>
                                            <h6 class="mb-0">价格变化: {{ alert.monitor_pair_name }}</h6>
                                        {% else %}
                                            <i class="bi bi-exclamation-triangle text-danger me-2"></i>
                                            <h6 class="mb-0">系统警报: {{ alert.alert_type.title() }}</h6>
                                        {% endif %}
                                    </div>
                                    
                                    <p class="mb-1">{{ alert.message }}</p>
                                    
                                    {% if alert.change_percent %}
                                        <div class="row">
                                            <div class="col-md-4">
                                                <small class="text-muted">之前汇率:</small><br>
                                                <span>{{ alert.old_rate|number(6) if alert.old_rate else '不可用' }}</span>
                                            </div>
                                            <div class="col-md-4">
                                                <small class="text-muted">新汇率:</small><br>
                                                <span>{{ alert.new_rate|number(6) if alert.new_rate else '不可用' }}</span>
                                            </div>
                                            <div class="col-md-4">
                                                <small class="text-muted">变化:</small><br>
                                                <span class="{% if alert.change_percent > 0 %}price-change-positive{% else %}price-change-negative{% endif %}">
                                                    {{ alert.change_percent|percentage }}
                                                </span>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">{{ alert.timestamp|datetime }}</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <!-- Pagination Info -->
                    <div class="mt-3 text-center">
                        <small class="text-muted">
                            第 {{ page }} 页
                            {% if has_prev or has_next %}
                                - 
                                {% if has_prev %}
                                    <a href="{{ url_for('main.alerts_page', page=page-1) }}">上一页</a>
                                {% endif %}
                                {% if has_prev and has_next %} | {% endif %}
                                {% if has_next %}
                                    <a href="{{ url_for('main.alerts_page', page=page+1) }}">下一页</a>
                                {% endif %}
                            {% endif %}
                        </small>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-bell-slash display-1 text-muted"></i>
                        <h4 class="text-muted mt-3">未找到警报</h4>
                        <p class="text-muted">尚未触发任何警报</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Alert Statistics -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-bar-chart"></i> 警报统计</h6>
            </div>
            <div class="card-body">
                {% if alerts %}
                    {% set price_alerts = alerts|selectattr("alert_type", "equalto", "price_change")|list %}
                    {% set system_alerts = alerts|rejectattr("alert_type", "equalto", "price_change")|list %}
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 text-warning">{{ price_alerts|length }}</div>
                                <small class="text-muted">价格警报</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 text-danger">{{ system_alerts|length }}</div>
                                <small class="text-muted">系统警报</small>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <p class="text-muted mb-0">无统计数据</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-info-circle"></i> 警报信息</h6>
            </div>
            <div class="card-body">
                <p class="mb-2"><strong>价格警报:</strong> 当价格变化超过配置阈值时触发</p>
                <p class="mb-2"><strong>系统警报:</strong> 关于系统状态和错误的通知</p>
                <p class="mb-0"><small class="text-muted">在 config.toml 文件中配置警报阈值</small></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

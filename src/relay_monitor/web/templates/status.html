{% extends "base.html" %}

{% block title %}系统状态 - Relay Monitor{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1><i class="bi bi-gear"></i> 系统状态</h1>
        <p class="text-muted">监控系统健康状况和配置</p>
    </div>
</div>

<!-- Monitor Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-activity"></i> 监控状态</h5>
            </div>
            <div class="card-body">
                {% if monitor_status %}
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card-metric">
                                <div class="metric-value {% if monitor_status.running %}text-success{% else %}text-danger{% endif %}">
                                    {% if monitor_status.running %}
                                        <i class="bi bi-play-circle"></i>
                                    {% else %}
                                        <i class="bi bi-stop-circle"></i>
                                    {% endif %}
                                </div>
                                <div class="metric-label">
                                    {% if monitor_status.running %}运行中{% else %}已停止{% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card-metric">
                                <div class="metric-value text-info">{{ monitor_status.stats.total_checks or 0 }}</div>
                                <div class="metric-label">总检查次数</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card-metric">
                                <div class="metric-value text-warning">{{ monitor_status.stats.alerts_triggered or 0 }}</div>
                                <div class="metric-label">触发警报</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card-metric">
                                <div class="metric-value text-danger">{{ monitor_status.stats.errors_encountered or 0 }}</div>
                                <div class="metric-label">错误</div>
                            </div>
                        </div>
                    </div>
                    
                    {% if monitor_status.running and monitor_status.uptime_formatted %}
                        <div class="mt-3">
                            <strong>运行时间:</strong> {{ monitor_status.uptime_formatted }}
                        </div>
                    {% endif %}
                {% else %}
                    <p class="text-muted">监控状态不可用</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Database Status -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-database"></i> 数据库统计</h6>
            </div>
            <div class="card-body">
                {% if db_stats %}
                    <table class="table table-sm">
                        <tr>
                            <td>数据库大小:</td>
                            <td>{{ db_stats.db_size_mb }} MB</td>
                        </tr>
                        <tr>
                            <td>价格记录:</td>
                            <td>{{ db_stats.price_history_count }}</td>
                        </tr>
                        <tr>
                            <td>警报记录:</td>
                            <td>{{ db_stats.alert_history_count }}</td>
                        </tr>
                        <tr>
                            <td>区块链:</td>
                            <td>{{ db_stats.chains_count }}</td>
                        </tr>
                        <tr>
                            <td>代币:</td>
                            <td>{{ db_stats.tokens_count }}</td>
                        </tr>
                    </table>
                {% else %}
                    <p class="text-muted">数据库统计不可用</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-list-check"></i> 监控交易对摘要</h6>
            </div>
            <div class="card-body">
                {% if pairs_summary %}
                    <table class="table table-sm">
                        {% for pair in pairs_summary %}
                        <tr>
                            <td>{{ pair.pair_name }}</td>
                            <td>{{ pair.total_records }} 条记录</td>
                            <td>
                                {% if pair.latest_timestamp %}
                                    <small class="text-muted">{{ pair.latest_timestamp|datetime }}</small>
                                {% else %}
                                    <small class="text-muted">无数据</small>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </table>
                {% else %}
                    <p class="text-muted">无监控交易对数据</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Configuration -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-gear-fill"></i> 配置</h6>
            </div>
            <div class="card-body">
                {% if config %}
                    <div class="row">
                        <div class="col-md-6">
                            <h6>API 配置</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td>基础URL:</td>
                                    <td>{{ config.api.base_url }}</td>
                                </tr>
                                <tr>
                                    <td>超时时间:</td>
                                    <td>{{ config.api.timeout }}s</td>
                                </tr>
                                <tr>
                                    <td>重试次数:</td>
                                    <td>{{ config.api.retry_attempts }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>监控配置</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td>检查间隔:</td>
                                    <td>{{ config.monitoring.interval_seconds }}s</td>
                                </tr>
                                <tr>
                                    <td>价格阈值:</td>
                                    <td>{{ config.monitoring.price_change_threshold_percent }}%</td>
                                </tr>
                                <tr>
                                    <td>启用交易对:</td>
                                    <td>{{ pairs|length if pairs else 0 }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                {% else %}
                    <p class="text-muted">配置不可用</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

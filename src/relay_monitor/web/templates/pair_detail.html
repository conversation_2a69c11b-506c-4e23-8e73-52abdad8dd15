{% extends "base.html" %}

{% block title %}{{ pair.name }} - Relay Monitor{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">仪表板</a></li>
                <li class="breadcrumb-item active">{{ pair.name }}</li>
            </ol>
        </nav>
        
        <h1><i class="bi bi-link-45deg"></i> {{ pair.name }}</h1>
        <p class="text-muted">{{ pair.description }}</p>
    </div>
</div>

<!-- Pair Information -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-info-circle"></i> 交易对配置</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>源链:</strong></td>
                        <td><span class="badge bg-primary">{{ pair.origin_chain|title }}</span></td>
                    </tr>
                    <tr>
                        <td><strong>源代币:</strong></td>
                        <td>{{ pair.origin_token }}</td>
                    </tr>
                    <tr>
                        <td><strong>目标链:</strong></td>
                        <td><span class="badge bg-success">{{ pair.destination_chain|title }}</span></td>
                    </tr>
                    <tr>
                        <td><strong>目标代币:</strong></td>
                        <td>{{ pair.destination_token }}</td>
                    </tr>
                    <tr>
                        <td><strong>金额:</strong></td>
                        <td>{{ pair.amount }}</td>
                    </tr>
                    <tr>
                        <td><strong>警报阈值:</strong></td>
                        <td>{{ pair.alert_threshold_percent }}%</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-bar-chart"></i> 统计</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="card-metric">
                            <div class="metric-value text-info">{{ stats_24h.sample_count or 0 }}</div>
                            <div class="metric-label">24小时样本</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card-metric">
                            <div class="metric-value text-warning">{{ stats_7d.sample_count or 0 }}</div>
                            <div class="metric-label">7天样本</div>
                        </div>
                    </div>
                </div>
                
                {% if stats_24h.sample_count > 0 %}
                    <hr>
                    <table class="table table-sm">
                        <tr>
                            <td>24小时平均汇率:</td>
                            <td>{{ stats_24h.avg_rate|number(6) }}</td>
                        </tr>
                        <tr>
                            <td>24小时最低汇率:</td>
                            <td>{{ stats_24h.min_rate|number(6) }}</td>
                        </tr>
                        <tr>
                            <td>24小时最高汇率:</td>
                            <td>{{ stats_24h.max_rate|number(6) }}</td>
                        </tr>
                        <tr>
                            <td>24小时波动率:</td>
                            <td>{{ stats_24h.rate_volatility|number(2) }}%</td>
                        </tr>
                    </table>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Price History Chart -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-graph-up"></i> 价格历史 (7天)</h6>
            </div>
            <div class="card-body">
                {% if price_history %}
                    <canvas id="priceChart" width="400" height="200"></canvas>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-graph-up display-4 text-muted"></i>
                        <p class="text-muted mt-2">无价格历史数据</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Price Data -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-table"></i> 最近价格数据</h6>
            </div>
            <div class="card-body">
                {% if price_history %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>时间戳</th>
                                    <th>汇率</th>
                                    <th>费用 (USD)</th>
                                    <th>时间估计</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in price_history[:20] %}
                                <tr>
                                    <td>{{ record.timestamp|datetime }}</td>
                                    <td>{{ record.exchange_rate|number(6) }}</td>
                                    <td>{{ record.total_fee_usd|currency(4) }}</td>
                                    <td>{{ record.time_estimate_seconds }}s</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">无价格数据</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Alerts for this pair -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-bell"></i> 最近警报</h6>
            </div>
            <div class="card-body">
                {% if alerts %}
                    <div class="list-group list-group-flush">
                        {% for alert in alerts[:10] %}
                        <div class="list-group-item alert-item alert-price-change">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ alert.message }}</h6>
                                    {% if alert.change_percent %}
                                        <small class="{% if alert.change_percent > 0 %}price-change-positive{% else %}price-change-negative{% endif %}">
                                            变化: {{ alert.change_percent|percentage }}
                                        </small>
                                    {% endif %}
                                </div>
                                <small class="text-muted">{{ alert.timestamp|datetime }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">此交易对无警报</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
{% if price_history %}
<script>
// Price history chart
const ctx = document.getElementById('priceChart').getContext('2d');
const priceData = [
    {% for record in price_history|reverse %}
    {
        x: '{{ record.timestamp.strftime("%Y-%m-%dT%H:%M:%S") }}',
        y: {{ record.exchange_rate }}
    },
    {% endfor %}
];

// 确保Chart.js已加载
if (typeof Chart !== 'undefined') {
    try {
        new Chart(ctx, {
            type: 'line',
            data: {
                datasets: [{
                    label: '汇率',
                    data: priceData,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.1,
                    pointRadius: 2,
                    pointHoverRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            displayFormats: {
                                hour: 'MM-dd HH:mm',
                                day: 'MM-dd',
                                minute: 'HH:mm'
                            }
                        },
                        title: {
                            display: true,
                            text: '时间'
                        }
                    },
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: '汇率'
                        },
                        ticks: {
                            callback: function(value) {
                                return value.toFixed(6);
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return '汇率: ' + context.parsed.y.toFixed(6);
                            }
                        }
                    }
                }
            }
        });
    } catch (error) {
        console.error('Chart.js error:', error);
        document.getElementById('priceChart').parentElement.innerHTML =
            '<div class="text-center py-4"><i class="bi bi-exclamation-triangle display-4 text-warning"></i><p class="text-muted mt-2">图表加载失败: ' + error.message + '</p></div>';
    }
} else {
    console.error('Chart.js not loaded');
    document.getElementById('priceChart').parentElement.innerHTML =
        '<div class="text-center py-4"><i class="bi bi-exclamation-triangle display-4 text-warning"></i><p class="text-muted mt-2">Chart.js库未加载</p></div>';
}
</script>
{% endif %}
{% endblock %}

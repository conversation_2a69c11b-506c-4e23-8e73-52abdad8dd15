{% extends "base.html" %}

{% block title %}仪表板 - Relay Monitor{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1><i class="bi bi-graph-up"></i> 跨链桥价格监控仪表板</h1>
                <p class="text-muted">实时监控跨链桥价格变化</p>
            </div>
            <div>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshDashboardData()">
                    <i class="bi bi-arrow-clockwise me-1"></i>
                    手动刷新
                </button>
                <span id="refresh-indicator" class="ms-2" style="display: none;">
                    <i class="bi bi-arrow-clockwise spin text-primary"></i>
                    <small class="text-muted">刷新中...</small>
                </span>
            </div>
        </div>
    </div>
</div>

<!-- System Status Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card card-metric">
            <div class="card-body">
                <div class="metric-value text-primary">{{ pairs|length }}</div>
                <div class="metric-label">监控交易对</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card card-metric">
            <div class="card-body">
                <div class="metric-value {% if monitor_status.running %}text-success{% else %}text-danger{% endif %}">
                    {% if monitor_status.running %}
                        <i class="bi bi-play-circle"></i>
                    {% else %}
                        <i class="bi bi-stop-circle"></i>
                    {% endif %}
                </div>
                <div class="metric-label">
                    {% if monitor_status.running %}运行中{% else %}已停止{% endif %}
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card card-metric">
            <div class="card-body">
                <div class="metric-value text-info">{{ monitor_status.stats.total_checks or 0 }}</div>
                <div class="metric-label">总检查次数</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card card-metric">
            <div class="card-body">
                <div class="metric-value text-warning">{{ monitor_status.stats.alerts_triggered or 0 }}</div>
                <div class="metric-label">触发警报</div>
            </div>
        </div>
    </div>
</div>

<!-- Monitor Pairs -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-list-ul"></i> 监控交易对</h5>
            </div>
            <div class="card-body">
                {% if pairs %}
                    <!-- 紧凑表格布局，避免纵向滚动 -->
                    <div class="table-responsive" style="max-height: 70vh; overflow-y: auto;">
                        <table class="table table-hover table-sm">
                            <thead class="table-light sticky-top">
                                <tr>
                                    <th style="width: 25%;">交易对</th>
                                    <th style="width: 25%;">路由</th>
                                    <th style="width: 12%;">最新价格</th>
                                    <th style="width: 12%;">手续费</th>
                                    <th style="width: 15%;">24h统计</th>
                                    <th style="width: 11%;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for pair in pairs %}
                                <tr>
                                    <td>
                                        <div class="fw-bold">{{ pair.name }}</div>
                                        {% if pair.description %}
                                        <small class="text-muted">{{ pair.description }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-primary me-1" style="font-size: 0.7rem;">{{ pair.origin_chain|title }}</span>
                                            <small class="text-muted me-1">{{ pair.origin_token }}</small>
                                            <i class="bi bi-arrow-right mx-1"></i>
                                            <span class="badge bg-success me-1" style="font-size: 0.7rem;">{{ pair.destination_chain|title }}</span>
                                            <small class="text-muted">{{ pair.destination_token }}</small>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        {% if pair.latest_price %}
                                            <div class="fw-bold text-primary">{{ pair.latest_price|number(6) }}</div>
                                        {% else %}
                                            <span class="text-muted">无数据</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        {% if pair.latest_fee %}
                                            <div class="fw-bold text-warning">{{ pair.latest_fee|currency(4) }}</div>
                                        {% else %}
                                            <span class="text-muted">无数据</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if pair.stats_24h.sample_count > 0 %}
                                            <div style="font-size: 0.75rem;">
                                                <div>平均: <span class="fw-bold">{{ pair.stats_24h.avg_rate|number(4) }}</span></div>
                                                <div>波动: <span class="fw-bold">{{ pair.stats_24h.rate_volatility|number(2) }}%</span></div>
                                                <div class="text-muted">样本: {{ pair.stats_24h.sample_count }}</div>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">无数据</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('main.pair_detail', pair_name=pair.name) }}"
                                               class="btn btn-outline-primary btn-sm" title="查看详情">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            {% if monitor_status.running %}
                                            <button class="btn btn-outline-secondary btn-sm"
                                                    id="check-{{ pair.name }}"
                                                    onclick="checkPairPrice('{{ pair.name }}')" title="立即检查">
                                                <i class="bi bi-arrow-clockwise"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-inbox display-1 text-muted"></i>
                        <h4 class="text-muted">未配置监控交易对</h4>
                        <p class="text-muted">请通过管理界面添加监控交易对</p>
                        <a href="{{ url_for('admin.pairs') }}" class="btn btn-primary">
                            <i class="bi bi-plus-lg"></i> 添加交易对
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Alerts -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="bi bi-bell"></i> 最近警报</h5>
                <a href="{{ url_for('main.alerts_page') }}" class="btn btn-sm btn-outline-primary">
                    查看全部 <i class="bi bi-arrow-right"></i>
                </a>
            </div>
            <div class="card-body">
                {% if recent_alerts %}
                    <div class="list-group list-group-flush">
                        {% for alert in recent_alerts[:5] %}
                        <div class="list-group-item alert-item {% if alert.alert_type == 'price_change' %}alert-price-change{% else %}alert-system{% endif %}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">
                                        {% if alert.alert_type == 'price_change' %}
                                            <i class="bi bi-graph-up"></i> Price Change: {{ alert.monitor_pair_name }}
                                        {% else %}
                                            <i class="bi bi-exclamation-triangle"></i> System Alert
                                        {% endif %}
                                    </h6>
                                    <p class="mb-1">{{ alert.message }}</p>
                                    {% if alert.change_percent %}
                                        <small class="{% if alert.change_percent > 0 %}price-change-positive{% else %}price-change-negative{% endif %}">
                                            变化: {{ alert.change_percent|percentage }}
                                        </small>
                                    {% endif %}
                                </div>
                                <small class="text-muted">{{ alert.timestamp|datetime }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="bi bi-bell-slash text-muted"></i>
                        <p class="text-muted mb-0">无最近警报</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
{% if monitor_status.running %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-clock"></i> 运行时间</h6>
            </div>
            <div class="card-body">
                {% if monitor_status.uptime_formatted %}
                    <p class="mb-0">{{ monitor_status.uptime_formatted }}</p>
                {% else %}
                    <p class="mb-0 text-muted">不可用</p>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-activity"></i> 最新价格</h6>
            </div>
            <div class="card-body">
                {% if monitor_status.last_prices %}
                    {% for pair_name, price in monitor_status.last_prices.items() %}
                        <small class="d-block">{{ pair_name }}: {{ price|number(6) }}</small>
                    {% endfor %}
                {% else %}
                    <p class="mb-0 text-muted">无价格数据</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

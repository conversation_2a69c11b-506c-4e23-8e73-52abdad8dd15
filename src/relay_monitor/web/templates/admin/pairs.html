{% extends "admin/base.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-12">
        <div class="card admin-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-arrow-left-right me-2"></i>
                    交易对管理
                </h5>
                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addPairModal">
                    <i class="bi bi-plus-lg me-1"></i>
                    添加交易对
                </button>
            </div>
            <div class="card-body">
                {% if pairs %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>名称</th>
                                <th>描述</th>
                                <th style="text-align: center;">源链</th>
                                <th style="text-align: center;">目标链</th>
                                <th style="text-align: center;">代币对</th>
                                <th style="text-align: center;">金额</th>
                                <th style="text-align: center;">阈值</th>
                                <th style="text-align: center;">状态</th>
                                <th style="text-align: center;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for pair in pairs %}
                            <tr>
                                <td>
                                    <strong>{{ pair.name }}</strong>
                                </td>
                                <td>{{ pair.description or '-' }}</td>
                                <td style="text-align: center;">
                                    <span class="badge bg-secondary">{{ pair.origin_chain }}</span>
                                </td>
                                <td style="text-align: center;">
                                    <span class="badge bg-secondary">{{ pair.destination_chain }}</span>
                                </td>
                                <td style="text-align: center;">
                                    {{ pair.origin_token }} → {{ pair.destination_token }}
                                </td>
                                <td style="text-align: center;">{{ pair.amount }}</td>
                                <td style="text-align: center;">{{ pair.alert_threshold_percent }}%</td>
                                <td style="text-align: center;">
                                    <span class="badge {{ 'bg-success' if pair.enabled else 'bg-secondary' }}">
                                        {{ '启用' if pair.enabled else '禁用' }}
                                    </span>
                                </td>
                                <td style="text-align: center;">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary"
                                                onclick="editPair('{{ pair.name }}')" title="编辑">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-{{ 'warning' if pair.enabled else 'success' }}"
                                                onclick="togglePair('{{ pair.name }}', {{ pair.enabled|lower }})"
                                                title="{{ '禁用' if pair.enabled else '启用' }}">
                                            <i class="bi bi-{{ 'pause-fill' if pair.enabled else 'play-fill' }}"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger"
                                                onclick="showDeleteConfirm('{{ pair.name }}')"
                                                title="删除">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-arrow-left-right" style="font-size: 3rem; color: var(--bs-secondary);"></i>
                    <h5 class="text-muted mt-3">暂无交易对</h5>
                    <p class="text-muted">点击上方按钮添加第一个交易对</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 添加交易对模态框 -->
<div class="modal fade" id="addPairModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-plus-lg me-2"></i>
                    添加交易对
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addPairForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="pairName" class="form-label">交易对名称</label>
                                <input type="text" class="form-control" id="pairName" name="name" placeholder="留空将自动生成">
                                <div class="form-text">留空将根据选择的链和代币自动生成，格式：CHAIN1_TOKEN1_to_CHAIN2_TOKEN2</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="pairDescription" class="form-label">描述</label>
                                <input type="text" class="form-control" id="pairDescription" name="description">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="originChain" class="form-label">源链 *</label>
                                <select class="form-select" id="originChain" name="origin_chain" required>
                                    <option value="">请选择源链</option>
                                </select>
                                <div class="form-text">选择交易的起始区块链</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="destinationChain" class="form-label">目标链 *</label>
                                <select class="form-select" id="destinationChain" name="destination_chain" required>
                                    <option value="">请选择目标链</option>
                                </select>
                                <div class="form-text">选择交易的目标区块链</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="originToken" class="form-label">源代币 *</label>
                                <select class="form-select" id="originToken" name="origin_token" required disabled>
                                    <option value="">请先选择源链</option>
                                </select>
                                <div class="form-text">选择要交换的代币</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="destinationToken" class="form-label">目标代币 *</label>
                                <select class="form-select" id="destinationToken" name="destination_token" required disabled>
                                    <option value="">请先选择目标链</option>
                                </select>
                                <div class="form-text">选择要获得的代币</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="amount" class="form-label">监控金额 *</label>
                                <input type="text" class="form-control" id="amount" name="amount" required>
                                <div class="form-text">例如：1.0, 1000.0</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="threshold" class="form-label">警报阈值 (%)</label>
                                <input type="number" class="form-control" id="threshold" name="alert_threshold_percent" 
                                       value="5.0" step="0.1" min="0.1" max="100">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">状态</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enabled" name="enabled" checked>
                                    <label class="form-check-label" for="enabled">
                                        启用监控
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="savePair()">
                    <i class="bi bi-check-lg me-1"></i>
                    保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑交易对模态框 -->
<div class="modal fade" id="editPairModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-pencil me-2"></i>
                    编辑交易对
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editPairForm">
                    <input type="hidden" id="editPairName" name="name">
                    
                    <div class="mb-3">
                        <label for="editDescription" class="form-label">描述</label>
                        <input type="text" class="form-control" id="editDescription" name="description">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editAmount" class="form-label">监控金额</label>
                                <input type="text" class="form-control" id="editAmount" name="amount">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editThreshold" class="form-label">警报阈值 (%)</label>
                                <input type="number" class="form-control" id="editThreshold" name="alert_threshold_percent" 
                                       step="0.1" min="0.1" max="100">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="editEnabled" name="enabled">
                        <label class="form-check-label" for="editEnabled">
                            启用监控
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updatePair()">
                    <i class="bi bi-check-lg me-1"></i>
                    更新
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle-fill text-danger me-2"></i>
                    确认删除
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">确定要删除交易对 <strong id="deletePairName"></strong> 吗？</p>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>警告：</strong>此操作不可撤销！删除后将停止监控该交易对，所有相关的历史数据将被保留。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-1"></i>
                    取消
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="bi bi-trash me-1"></i>
                    确认删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 保存新交易对
function savePair() {
    const form = document.getElementById('addPairForm');
    const formData = new FormData(form);

    // 验证必需字段
    const requiredFields = ['origin_chain', 'destination_chain', 'origin_token', 'destination_token', 'amount'];
    for (const field of requiredFields) {
        const value = formData.get(field);
        if (!value || value.trim() === '') {
            showError(`请填写${getFieldDisplayName(field)}`);
            return;
        }
    }

    // 如果名称为空，使用自动生成的名称
    let pairName = formData.get('name');
    if (!pairName || pairName.trim() === '') {
        generatePairName(); // 确保生成最新的名称
        pairName = document.getElementById('pairName').value;
        if (!pairName || pairName.trim() === '') {
            showError('无法生成交易对名称，请检查所选的链和代币');
            return;
        }
    }

    const data = {
        name: pairName.trim(),
        description: formData.get('description'),
        origin_chain: formData.get('origin_chain'),
        destination_chain: formData.get('destination_chain'),
        origin_token: formData.get('origin_token'),
        destination_token: formData.get('destination_token'),
        amount: formData.get('amount'),
        alert_threshold_percent: parseFloat(formData.get('alert_threshold_percent')),
        enabled: document.getElementById('enabled').checked
    };

    // 验证金额格式
    if (isNaN(parseFloat(data.amount)) || parseFloat(data.amount) <= 0) {
        showError('请输入有效的监控金额');
        return;
    }

    fetch('/api/admin/pairs', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('交易对添加成功！');

            // 1秒后关闭模态框并重新加载页面
            setTimeout(() => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('addPairModal'));
                modal.hide();
                location.reload();
            }, 1000);
        } else {
            showError('添加失败: ' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('添加失败，请检查网络连接');
    });
}

// 编辑交易对
function editPair(pairName) {
    // 获取当前交易对数据
    fetch('/api/admin/pairs')
    .then(response => response.json())
    .then(data => {
        const pair = data.pairs.find(p => p.name === pairName);
        if (pair) {
            document.getElementById('editPairName').value = pair.name;
            document.getElementById('editDescription').value = pair.description || '';
            document.getElementById('editAmount').value = pair.amount;
            document.getElementById('editThreshold').value = pair.alert_threshold_percent;
            document.getElementById('editEnabled').checked = pair.enabled;

            new bootstrap.Modal(document.getElementById('editPairModal')).show();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('获取交易对数据失败，请检查网络连接');
    });
}

// 更新交易对
function updatePair() {
    const form = document.getElementById('editPairForm');
    const formData = new FormData(form);
    const pairName = formData.get('name');

    const data = {
        description: formData.get('description'),
        amount: formData.get('amount'),
        alert_threshold_percent: parseFloat(formData.get('alert_threshold_percent')),
        enabled: document.getElementById('editEnabled').checked
    };

    fetch(`/api/admin/pairs/${pairName}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('交易对更新成功！');

            // 1秒后关闭模态框并重新加载页面
            setTimeout(() => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('editPairModal'));
                modal.hide();
                location.reload();
            }, 1000);
        } else {
            showError('更新失败: ' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('更新失败，请检查网络连接');
    });
}

// 切换交易对状态
function togglePair(pairName, currentEnabled) {
    const newEnabled = !currentEnabled;
    const action = newEnabled ? '启用' : '禁用';

    if (!confirm(`确定要${action}交易对 '${pairName}' 吗？`)) {
        return;
    }

    fetch(`/api/admin/pairs/${pairName}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ enabled: newEnabled })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(`交易对${action}成功！`);
            setTimeout(() => location.reload(), 1000);
        } else {
            showError(`${action}失败: ` + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError(`${action}失败，请检查网络连接`);
    });
}

// 显示删除确认对话框
function showDeleteConfirm(pairName) {
    document.getElementById('deletePairName').textContent = pairName;

    // 设置确认按钮的点击事件
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    confirmBtn.onclick = function() {
        deletePair(pairName);
    };

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    modal.show();
}

// 删除交易对
function deletePair(pairName) {
    // 关闭确认模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
    modal.hide();

    fetch(`/api/admin/pairs/${pairName}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('交易对删除成功！');
            setTimeout(() => location.reload(), 1000);
        } else {
            showError('删除失败: ' + (data.error || '未知错误'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('删除失败，请检查网络连接');
    });
}

// 全局变量存储链和代币数据
let chainsData = [];
let tokensCache = {};

// 字段显示名称映射
function getFieldDisplayName(field) {
    const fieldNames = {
        'name': '交易对名称',
        'origin_chain': '源链',
        'destination_chain': '目标链',
        'origin_token': '源代币',
        'destination_token': '目标代币',
        'amount': '监控金额'
    };
    return fieldNames[field] || field;
}

// 加载链数据
async function loadChains() {
    try {
        const response = await fetch('/api/admin/chains');
        const data = await response.json();

        if (data.chains) {
            chainsData = data.chains;
            populateChainSelects();
        } else {
            console.error('Failed to load chains:', data.error);
            showError('加载区块链列表失败: ' + (data.error || '未知错误'));
        }
    } catch (error) {
        console.error('Error loading chains:', error);
        showError('加载区块链列表失败，请检查网络连接');
    }
}

// 显示消息提示
function showMessage(message, type = 'danger') {
    // 移除现有的提示
    const existingAlerts = document.querySelectorAll('.alert-message');
    existingAlerts.forEach(alert => alert.remove());

    // 创建新的提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-message mb-3`;

    const iconMap = {
        'danger': 'bi-exclamation-triangle-fill',
        'warning': 'bi-exclamation-triangle',
        'success': 'bi-check-circle-fill',
        'info': 'bi-info-circle-fill'
    };

    alertDiv.innerHTML = `
        <i class="bi ${iconMap[type] || iconMap.danger} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 尝试多个可能的容器位置
    let container = document.querySelector('#addPairModal .modal-body');
    if (!container) {
        container = document.querySelector('.modal-body');
    }
    if (!container) {
        container = document.querySelector('.card-body');
    }
    if (!container) {
        container = document.querySelector('.container-fluid');
    }
    if (!container) {
        container = document.body;
    }

    // 插入到容器顶部
    if (container && container.firstChild) {
        container.insertBefore(alertDiv, container.firstChild);
    } else if (container) {
        container.appendChild(alertDiv);
    } else {
        // 如果都找不到，创建一个固定位置的提示
        alertDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        document.body.appendChild(alertDiv);
    }

    // 5秒后自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// 显示错误消息（保持向后兼容）
function showError(message) {
    showMessage(message, 'danger');
}

// 显示成功消息
function showSuccess(message) {
    showMessage(message, 'success');
}

// 显示警告消息
function showWarning(message) {
    showMessage(message, 'warning');
}

// 显示信息消息
function showInfo(message) {
    showMessage(message, 'info');
}

// 填充链选择框
function populateChainSelects() {
    const originChainSelect = document.getElementById('originChain');
    const destinationChainSelect = document.getElementById('destinationChain');

    // 清空现有选项
    originChainSelect.innerHTML = '<option value="">请选择源链</option>';
    destinationChainSelect.innerHTML = '<option value="">请选择目标链</option>';

    // 添加链选项
    chainsData.forEach(chain => {
        const option1 = new Option(chain.display_name, chain.name);
        const option2 = new Option(chain.display_name, chain.name);

        originChainSelect.appendChild(option1);
        destinationChainSelect.appendChild(option2);
    });
}

// 加载指定链的代币
async function loadTokensForChain(chainName) {
    if (tokensCache[chainName]) {
        return tokensCache[chainName];
    }

    try {
        const response = await fetch(`/api/admin/chains/${chainName}/tokens`);
        const data = await response.json();

        if (data.tokens) {
            tokensCache[chainName] = data.tokens;
            return data.tokens;
        } else {
            console.error('Failed to load tokens for chain:', chainName, data.error);
            showError(`加载 ${chainName} 链的代币失败: ${data.error || '未知错误'}`);
            return [];
        }
    } catch (error) {
        console.error('Error loading tokens for chain:', chainName, error);
        showError(`加载 ${chainName} 链的代币失败，请检查网络连接`);
        return [];
    }
}

// 填充代币选择框
async function populateTokenSelect(selectId, chainName) {
    const tokenSelect = document.getElementById(selectId);

    if (!chainName) {
        tokenSelect.innerHTML = '<option value="">请先选择区块链</option>';
        tokenSelect.disabled = true;
        return;
    }

    // 显示加载状态
    tokenSelect.innerHTML = '<option value="">加载代币中...</option>';
    tokenSelect.disabled = true;

    const tokens = await loadTokensForChain(chainName);

    // 清空并填充代币选项
    tokenSelect.innerHTML = '<option value="">请选择代币</option>';

    if (tokens.length === 0) {
        tokenSelect.innerHTML = '<option value="">该链暂无可用代币</option>';
        tokenSelect.disabled = true;
        return;
    }

    tokens.forEach(token => {
        const option = new Option(`${token.symbol} (${token.name})`, token.symbol);
        tokenSelect.appendChild(option);
    });

    tokenSelect.disabled = false;

    // 触发名称生成
    generatePairName();
}

// 自动生成交易对名称
function generatePairName() {
    const originChain = document.getElementById('originChain').value.toUpperCase();
    const originToken = document.getElementById('originToken').value.toUpperCase();
    const destChain = document.getElementById('destinationChain').value.toUpperCase();
    const destToken = document.getElementById('destinationToken').value.toUpperCase();

    if (originChain && originToken && destChain && destToken) {
        const name = `${originChain}_${originToken}_to_${destChain}_${destToken}`;
        const nameInput = document.getElementById('pairName');

        // 只有当用户没有手动输入名称时才自动生成
        if (!nameInput.value.trim() || nameInput.dataset.autoGenerated === 'true') {
            nameInput.value = name;
            nameInput.dataset.autoGenerated = 'true';
        }
    }
}

// 表单验证和事件监听
document.addEventListener('DOMContentLoaded', function() {
    // 加载链数据
    loadChains();

    // 监听链选择变化
    document.getElementById('originChain').addEventListener('change', function() {
        const chainName = this.value;
        populateTokenSelect('originToken', chainName);
        generatePairName();
    });

    document.getElementById('destinationChain').addEventListener('change', function() {
        const chainName = this.value;
        populateTokenSelect('destinationToken', chainName);
        generatePairName();
    });

    // 监听代币选择变化
    document.getElementById('originToken').addEventListener('change', generatePairName);
    document.getElementById('destinationToken').addEventListener('change', generatePairName);

    // 监听用户手动输入名称
    document.getElementById('pairName').addEventListener('input', function() {
        // 如果用户手动输入了内容，标记为非自动生成
        if (this.value.trim()) {
            this.dataset.autoGenerated = 'false';
        } else {
            this.dataset.autoGenerated = 'true';
            generatePairName(); // 如果清空了，重新自动生成
        }
    });

    // 当模态框打开时重新加载链数据
    document.getElementById('addPairModal').addEventListener('show.bs.modal', function() {
        if (chainsData.length === 0) {
            loadChains();
        }
        // 重置名称输入框状态
        const nameInput = document.getElementById('pairName');
        nameInput.value = '';
        nameInput.dataset.autoGenerated = 'true';
    });
});
</script>
{% endblock %}

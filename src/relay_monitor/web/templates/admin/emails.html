{% extends "admin/base.html" %}

{% block title %}邮件管理{% endblock %}

{% block admin_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-envelope"></i> 邮件管理</h2>
                <div>
                    <button type="button" class="btn btn-primary me-2" onclick="sendTestAlert()">
                        <i class="bi bi-send"></i> 发送测试警报
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="refreshEmails()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                </div>
            </div>

            <!-- 邮件配置信息 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-gear"></i> 邮件配置
                            </h5>
                        </div>
                        <div class="card-body">
                            <p><strong>发件人：</strong> {{ from_email or '未配置' }}</p>
                            <p><strong>收件人：</strong> 
                                {% if to_emails %}
                                    {% for email in to_emails %}
                                        <span class="badge bg-primary me-1">{{ email }}</span>
                                    {% endfor %}
                                {% else %}
                                    未配置
                                {% endif %}
                            </p>
                            <p class="text-muted mb-0">
                                <small>使用SMTP.dev服务发送邮件警报</small>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-mailbox"></i> 邮箱选择
                            </h5>
                        </div>
                        <div class="card-body">
                            <select class="form-select" id="emailSelect" onchange="changeEmail()">
                                {% for account in accounts %}
                                    <option value="{{ account.address }}" 
                                            {% if account.address == selected_email %}selected{% endif %}>
                                        {{ account.address }}
                                        {% if not account.is_active %} (非活跃){% endif %}
                                    </option>
                                {% endfor %}
                            </select>
                            <small class="text-muted">选择要查看邮件的邮箱</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 邮件列表 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ul"></i> 收到的邮件 
                        <span class="badge bg-primary">{{ messages|length }}</span>
                        {% if selected_email %}
                            <small class="text-muted">- {{ selected_email }}</small>
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if messages %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>主题</th>
                                        <th>发件人</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for message in messages %}
                                    <tr>
                                        <td>
                                            <small class="text-muted">
                                                {{ message.created_at[:19].replace('T', ' ') }}
                                            </small>
                                        </td>
                                        <td>
                                            <strong>{{ message.subject }}</strong>
                                            {% if message.intro %}
                                                <br><small class="text-muted">{{ message.intro[:100] }}{% if message.intro|length > 100 %}...{% endif %}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                {{ message.from.name or message.from.address }}
                                            </span>
                                        </td>
                                        <td>
                                            {% if message.is_read %}
                                                <span class="badge bg-success">已读</span>
                                            {% else %}
                                                <span class="badge bg-warning">未读</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    onclick="viewEmailDetail('{{ message.id }}', '{{ message.account_email }}')">
                                                <i class="bi bi-eye"></i> 查看
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-inbox display-1 text-muted"></i>
                            <h4 class="text-muted mt-3">暂无邮件</h4>
                            <p class="text-muted">发送测试警报或等待系统警报邮件</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 邮件详情模态框 -->
<div class="modal fade" id="emailDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">邮件详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="emailDetailContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function sendTestAlert() {
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="bi bi-hourglass-split"></i> 发送中...';
    
    fetch('{{ url_for("admin.test_email_alert") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // 3秒后刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        } else {
            showAlert('danger', data.error || '发送失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', '发送测试警报时发生错误');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

function refreshEmails() {
    window.location.reload();
}

function changeEmail() {
    const select = document.getElementById('emailSelect');
    const selectedEmail = select.value;
    if (selectedEmail) {
        window.location.href = `{{ url_for('admin.emails') }}?email=${encodeURIComponent(selectedEmail)}`;
    }
}

function viewEmailDetail(messageId, accountEmail) {
    const modal = new bootstrap.Modal(document.getElementById('emailDetailModal'));
    const content = document.getElementById('emailDetailContent');
    
    // 显示加载状态
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    `;
    
    modal.show();
    
    // 获取邮件详情
    const url = `{{ url_for("admin.email_detail", message_id="MESSAGE_ID") }}`.replace('MESSAGE_ID', messageId);
    const params = new URLSearchParams();
    if (accountEmail) {
        params.append('email', accountEmail);
    }
    
    fetch(`${url}?${params}`)
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            content.innerHTML = `<div class="alert alert-danger">${data.error}</div>`;
            return;
        }
        
        // 渲染邮件详情
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <strong>发件人：</strong> ${data.from.name || data.from.address}<br>
                    <strong>收件人：</strong> ${data.to.map(t => t.name || t.address).join(', ')}<br>
                    <strong>主题：</strong> ${data.subject}<br>
                    <strong>时间：</strong> ${data.createdAt.replace('T', ' ').substring(0, 19)}
                </div>
                <div class="col-md-6">
                    <strong>状态：</strong> ${data.isRead ? '<span class="badge bg-success">已读</span>' : '<span class="badge bg-warning">未读</span>'}<br>
                    <strong>大小：</strong> ${(data.size / 1024).toFixed(1)} KB<br>
                    <strong>附件：</strong> ${data.hasAttachments ? '有' : '无'}
                </div>
            </div>
            <hr>
            <div class="mt-3">
                <h6>邮件内容：</h6>
                <div class="border p-3" style="max-height: 400px; overflow-y: auto;">
                    ${data.html && data.html.length > 0 ? data.html[0] : (data.text || '无内容')}
                </div>
            </div>
        `;
    })
    .catch(error => {
        console.error('Error:', error);
        content.innerHTML = '<div class="alert alert-danger">加载邮件详情时发生错误</div>';
    });
}

function showAlert(type, message) {
    // 移除现有的提示
    const existingAlerts = document.querySelectorAll('.alert-message');
    existingAlerts.forEach(alert => alert.remove());

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed alert-message`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // 5秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}

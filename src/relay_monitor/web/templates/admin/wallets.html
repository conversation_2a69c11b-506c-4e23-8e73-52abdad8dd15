{% extends "admin/base.html" %}

{% block title %}钱包管理{% endblock %}

{% block admin_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-wallet me-2"></i>钱包管理
                    </h5>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addWalletModal">
                        <i class="fas fa-plus me-1"></i>添加钱包
                    </button>
                </div>
                <div class="card-body">
                    <!-- 钱包列表 -->
                    <div class="table-responsive">
                        <table class="table table-striped" id="walletsTable">
                            <thead>
                                <tr>
                                    <th>钱包名称</th>
                                    <th>地址</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for wallet in wallets %}
                                <tr>
                                    <td>
                                        <strong>{{ wallet.name }}</strong>
                                    </td>
                                    <td>
                                        <code class="text-muted">{{ wallet.address }}</code>
                                        <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('{{ wallet.address }}')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </td>
                                    <td>{{ wallet.created_at }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-danger" onclick="deleteWallet('{{ wallet.name }}')">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted">
                                        <i class="fas fa-info-circle me-2"></i>暂无钱包，请添加钱包
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加钱包模态框 -->
<div class="modal fade" id="addWalletModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加钱包</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addWalletForm">
                    <div class="mb-3">
                        <label for="walletName" class="form-label">钱包名称</label>
                        <input type="text" class="form-control" id="walletName" required>
                        <div class="form-text">为钱包设置一个易于识别的名称</div>
                    </div>
                    <div class="mb-3">
                        <label for="privateKey" class="form-label">私钥</label>
                        <textarea class="form-control" id="privateKey" rows="3" required placeholder="输入64位十六进制私钥（可带或不带0x前缀）"></textarea>
                        <div class="form-text text-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            私钥将被加密存储，请确保私钥的安全性
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-1"></i>
                        私钥将使用系统配置的交易主密码进行加密存储。请确保已在系统设置中配置交易主密码。
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addWallet()">
                    <i class="fas fa-plus me-1"></i>添加钱包
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 消息提示区域 -->
<div id="messageArea"></div>
{% endblock %}

{% block extra_js %}
<script>
// 显示消息
function showMessage(message, type = 'info') {
    const messageArea = document.getElementById('messageArea');
    const alertClass = type === 'error' ? 'alert-danger' : 
                      type === 'success' ? 'alert-success' : 
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    messageArea.innerHTML = alertHtml;
    
    // 自动隐藏成功消息
    if (type === 'success') {
        setTimeout(() => {
            const alert = messageArea.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 3000);
    }
}

// 复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showMessage('地址已复制到剪贴板', 'success');
    }).catch(() => {
        showMessage('复制失败', 'error');
    });
}

// 添加钱包
function addWallet() {
    const name = document.getElementById('walletName').value.trim();
    const privateKey = document.getElementById('privateKey').value.trim();

    if (!name || !privateKey) {
        showMessage('请填写钱包名称和私钥', 'error');
        return;
    }

    const data = {
        name: name,
        private_key: privateKey
    };
    
    fetch('/api/admin/wallets', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('addWalletModal'));
            modal.hide();
            // 清空表单
            document.getElementById('addWalletForm').reset();
            // 刷新页面
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showMessage(data.error || '添加钱包失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('网络错误，请重试', 'error');
    });
}

// 删除钱包
function deleteWallet(walletName) {
    if (!confirm(`确定要删除钱包 "${walletName}" 吗？此操作不可撤销。`)) {
        return;
    }
    
    fetch(`/api/admin/wallets/${encodeURIComponent(walletName)}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            // 刷新页面
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showMessage(data.error || '删除钱包失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('网络错误，请重试', 'error');
    });
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 清空模态框表单当模态框关闭时
    document.getElementById('addWalletModal').addEventListener('hidden.bs.modal', function() {
        document.getElementById('addWalletForm').reset();
    });
});
</script>
{% endblock %}

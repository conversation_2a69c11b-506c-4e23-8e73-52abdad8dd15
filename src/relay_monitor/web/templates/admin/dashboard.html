{% extends "admin/base.html" %}

{% block admin_content %}
<div class="row">
    <!-- 系统状态卡片 -->
    <div class="col-md-3 mb-4">
        <div class="card admin-card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="bi bi-server" style="font-size: 2rem; color: var(--bs-primary);"></i>
                </div>
                <h5 class="card-title">监控状态</h5>
                <span class="badge status-badge {{ 'bg-success' if monitor_status.running else 'bg-danger' }}">
                    {{ '运行中' if monitor_status.running else '已停止' }}
                </span>
            </div>
        </div>
    </div>
    
    <!-- 交易对统计 -->
    <div class="col-md-3 mb-4">
        <div class="card admin-card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="bi bi-arrow-left-right" style="font-size: 2rem; color: var(--bs-info);"></i>
                </div>
                <h5 class="card-title">交易对</h5>
                <h3 class="text-info">{{ enabled_pairs_count }}</h3>
                <small class="text-muted">已启用 / 总计 {{ total_pairs_count }}</small>
            </div>
        </div>
    </div>
    
    <!-- 数据库统计 -->
    <div class="col-md-3 mb-4">
        <div class="card admin-card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="bi bi-database" style="font-size: 2rem; color: var(--bs-warning);"></i>
                </div>
                <h5 class="card-title">价格记录</h5>
                <h3 class="text-warning">{{ db_stats.price_history_count }}</h3>
                <small class="text-muted">条记录</small>
            </div>
        </div>
    </div>
    
    <!-- 警报统计 -->
    <div class="col-md-3 mb-4">
        <div class="card admin-card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="bi bi-bell-fill" style="font-size: 2rem; color: var(--bs-danger);"></i>
                </div>
                <h5 class="card-title">警报记录</h5>
                <h3 class="text-danger">{{ db_stats.alert_history_count }}</h3>
                <small class="text-muted">条记录</small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 系统信息 -->
    <div class="col-md-6 mb-4">
        <div class="card admin-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle-fill me-2"></i>
                    系统信息
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tbody>
                        <tr>
                            <td><strong>监控状态:</strong></td>
                            <td>
                                <span class="badge {{ 'bg-success' if monitor_status.running else 'bg-danger' }}">
                                    {{ '运行中' if monitor_status.running else '已停止' }}
                                </span>
                            </td>
                        </tr>
                        {% if monitor_status.stats %}
                        <tr>
                            <td><strong>总检查次数:</strong></td>
                            <td>{{ monitor_status.stats.get('total_checks', 0) }}</td>
                        </tr>
                        <tr>
                            <td><strong>触发警报:</strong></td>
                            <td>{{ monitor_status.stats.get('alerts_triggered', 0) }}</td>
                        </tr>
                        {% endif %}
                        <tr>
                            <td><strong>数据库大小:</strong></td>
                            <td>{{ db_stats.db_size_mb }} MB</td>
                        </tr>
                        <tr>
                            <td><strong>最后更新:</strong></td>
                            <td>{{ db_stats.latest_price_timestamp if db_stats.latest_price_timestamp else '无' }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 快速操作 -->
    <div class="col-md-6 mb-4">
        <div class="card admin-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-tools me-2"></i>
                    快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('admin.pairs') }}" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left-right me-2"></i>
                        管理交易对
                    </a>
                    <a href="{{ url_for('admin.alerts') }}" class="btn btn-outline-info">
                        <i class="bi bi-bell-fill me-2"></i>
                        配置警报
                    </a>
                    <button type="button" class="btn btn-outline-warning" onclick="reloadConfig()">
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        重载配置
                    </button>
                    <a href="{{ url_for('main.status') }}" class="btn btn-outline-secondary" target="_blank">
                        <i class="bi bi-graph-up me-2"></i>
                        查看监控状态
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 会话信息 -->
{% if session_info %}
<div class="row">
    <div class="col-12">
        <div class="card admin-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-shield-check me-2"></i>
                    会话信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>登录时间:</strong><br>
                        <span class="text-muted">{{ session_info.login_time.strftime('%Y-%m-%d %H:%M:%S') }}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>客户端IP:</strong><br>
                        <span class="text-muted">{{ session_info.user_ip }}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>会话到期:</strong><br>
                        <span class="text-muted">{{ session_info.expires_at.strftime('%Y-%m-%d %H:%M:%S') }}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>剩余时间:</strong><br>
                        <span class="text-muted" id="remaining-time">计算中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 会话过期模态框 -->
<div class="modal fade" id="sessionExpiredModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-clock-fill text-warning me-2"></i>
                    会话已过期
                </h5>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    您的登录会话已过期，为了安全起见，请重新登录。
                </div>
                <p class="mb-0">点击下方按钮将跳转到登录页面。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="redirectToLogin()">
                    <i class="bi bi-box-arrow-in-right me-1"></i>
                    重新登录
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 重载配置确认模态框 -->
<div class="modal fade" id="reloadConfirmModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-arrow-clockwise text-primary me-2"></i>
                    确认重载配置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">确定要重载系统配置吗？</p>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>操作说明：</strong>
                    <ul class="mb-0 mt-2">
                        <li>重新加载配置文件</li>
                        <li>重启监控系统</li>
                        <li>应用新的配置参数</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-1"></i>
                    取消
                </button>
                <button type="button" class="btn btn-primary" onclick="executeReload()">
                    <i class="bi bi-arrow-clockwise me-1"></i>
                    确认重载
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 重载配置
function reloadConfig() {
    showReloadConfirmModal();
}

// 显示重载确认对话框
function showReloadConfirmModal() {
    const modal = new bootstrap.Modal(document.getElementById('reloadConfirmModal'));
    modal.show();
}

// 执行配置重载
function executeReload() {
    // 关闭确认模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('reloadConfirmModal'));
    modal.hide();

    const btn = document.querySelector('[onclick="reloadConfig()"]');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>重载中...';
    btn.disabled = true;

    fetch('/api/admin/reload', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showDashboardMessage('配置重载成功！', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showDashboardMessage('配置重载失败: ' + (data.error || '未知错误'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showDashboardMessage('配置重载失败，请检查网络连接', 'danger');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// 显示会话过期模态框
function showSessionExpiredModal() {
    const modal = new bootstrap.Modal(document.getElementById('sessionExpiredModal'));
    modal.show();
}

// 跳转到登录页面
function redirectToLogin() {
    window.location.href = '{{ url_for("admin.login") }}';
}

// 显示仪表板消息
function showDashboardMessage(message, type = 'danger') {
    // 移除现有的提示
    const existingAlerts = document.querySelectorAll('.alert-message');
    existingAlerts.forEach(alert => alert.remove());

    // 创建新的提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-message mb-3`;

    const iconMap = {
        'danger': 'bi-exclamation-triangle-fill',
        'warning': 'bi-exclamation-triangle',
        'success': 'bi-check-circle-fill',
        'info': 'bi-info-circle-fill'
    };

    alertDiv.innerHTML = `
        <i class="bi ${iconMap[type] || iconMap.danger} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 尝试多个可能的容器位置
    let container = document.querySelector('.container-fluid');
    if (!container) {
        container = document.querySelector('.container');
    }
    if (!container) {
        container = document.querySelector('main');
    }
    if (!container) {
        container = document.querySelector('.card-body');
    }
    if (!container) {
        container = document.body;
    }

    // 插入到容器顶部
    if (container && container.firstChild) {
        container.insertBefore(alertDiv, container.firstChild);
    } else if (container) {
        container.appendChild(alertDiv);
    } else {
        // 如果都找不到，创建一个固定位置的提示
        alertDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        document.body.appendChild(alertDiv);
    }

    // 5秒后自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// 更新剩余时间
let remainingSeconds = {{ session_info.remaining_seconds if session_info else 0 }};

function updateRemainingTime() {
    const remainingElement = document.getElementById('remaining-time');
    if (!remainingElement) return;

    {% if session_info %}
    if (remainingSeconds > 0) {
        const minutes = Math.floor(remainingSeconds / 60);
        const seconds = remainingSeconds % 60;
        remainingElement.textContent = `${minutes}分${seconds}秒`;

        if (minutes < 5) {
            remainingElement.className = 'text-danger';
        } else if (minutes < 10) {
            remainingElement.className = 'text-warning';
        } else {
            remainingElement.className = 'text-muted';
        }

        // 每秒减少1秒
        remainingSeconds--;
    } else {
        remainingElement.textContent = '已过期';
        remainingElement.className = 'text-danger';

        // 显示友好的会话过期提示
        showSessionExpiredModal();
    }
    {% endif %}
}

// 页面加载完成后启动
document.addEventListener('DOMContentLoaded', function() {
    updateRemainingTime();
    setInterval(updateRemainingTime, 1000); // 每秒更新一次
});
</script>
{% endblock %}

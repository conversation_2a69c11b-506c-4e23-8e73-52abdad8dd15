{% extends "admin/base.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-12">
        <div class="card admin-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-bell-fill me-2"></i>
                    警报配置
                </h5>
            </div>
            <div class="card-body">
                <form id="alertsForm">
                    <!-- 全局设置 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2 mb-3">全局设置</h6>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="alertsEnabled" 
                                       {{ 'checked' if alerts_config.enabled else '' }}>
                                <label class="form-check-label" for="alertsEnabled">
                                    <strong>启用警报系统</strong>
                                </label>
                            </div>
                            <div class="form-text">关闭后将不会发送任何警报通知</div>
                        </div>
                        <div class="col-md-6">
                            <label for="rateLimitMinutes" class="form-label">警报频率限制 (分钟)</label>
                            <input type="number" class="form-control" id="rateLimitMinutes" 
                                   value="{{ alerts_config.rate_limit_minutes }}" min="1" max="1440">
                            <div class="form-text">同一交易对在此时间内最多发送一次警报</div>
                        </div>
                    </div>

                    <!-- 控制台警报 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2 mb-3">
                                <i class="bi bi-terminal me-2"></i>
                                控制台警报
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="consoleEnabled" 
                                       {{ 'checked' if alerts_config.console.enabled else '' }}>
                                <label class="form-check-label" for="consoleEnabled">
                                    <strong>启用控制台警报</strong>
                                </label>
                            </div>
                            <div class="form-text">在终端/日志中显示警报信息</div>
                        </div>
                    </div>

                    <!-- SMTP.dev 邮件警报 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2 mb-3">
                                <i class="bi bi-envelope-fill me-2"></i>
                                邮件警报 (SMTP.dev)
                            </h6>
                        </div>
                        <div class="col-12">
                            <div class="alert alert-info">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-info-circle-fill me-2"></i>
                                    <div>
                                        <strong>邮件警报已启用</strong><br>
                                        <small>使用 SMTP.dev 服务发送邮件警报。邮件配置在系统配置文件中管理。</small>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="bi bi-send me-2"></i>发件人
                                            </h6>
                                            <p class="card-text">{{ alerts_config.smtp_dev.from_email or '<EMAIL>' }}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="bi bi-inbox me-2"></i>收件人
                                            </h6>
                                            <p class="card-text">
                                                {% if alerts_config.smtp_dev.to_emails %}
                                                    {% for email in alerts_config.smtp_dev.to_emails %}
                                                        <span class="badge bg-primary me-1">{{ email }}</span>
                                                    {% endfor %}
                                                {% else %}
                                                    <span class="badge bg-primary"><EMAIL></span>
                                                {% endif %}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="bi bi-gear me-1"></i>
                                    要修改邮件配置，请前往 <a href="{{ url_for('admin.emails') }}" class="text-decoration-none">邮件管理</a> 页面
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Bark推送警报 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2 mb-3">
                                <i class="bi bi-phone me-2"></i>
                                Bark推送警报 (iOS)
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="barkEnabled"
                                       {{ 'checked' if alerts_config.bark.enabled else '' }}>
                                <label class="form-check-label" for="barkEnabled">
                                    <strong>启用Bark推送警报</strong>
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="barkKeys" class="form-label">Bark Keys</label>
                                        <textarea class="form-control" id="barkKeys" rows="3"
                                                  placeholder="your_bark_key_here&#10;another_bark_key&#10;third_bark_key">{{ alerts_config.bark.keys|join('\n') }}</textarea>
                                        <div class="form-text">每行一个Bark Key，支持多个iOS设备同时接收推送</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="barkTimeout" class="form-label">超时时间 (秒)</label>
                                        <input type="number" class="form-control" id="barkTimeout"
                                               value="{{ alerts_config.bark.timeout }}"
                                               min="1" max="60" placeholder="10">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="barkServerUrl" class="form-label">服务器地址</label>
                                <input type="url" class="form-control" id="barkServerUrl"
                                       value="{{ alerts_config.bark.server_url }}"
                                       placeholder="https://api.day.app">
                                <div class="form-text">默认使用官方服务器，也可以使用自建服务器</div>
                            </div>
                            <div class="alert alert-info">
                                <div class="d-flex align-items-start">
                                    <i class="bi bi-info-circle-fill me-2 mt-1"></i>
                                    <div>
                                        <strong>如何获取Bark Key：</strong><br>
                                        <small>
                                            1. 在App Store下载并安装Bark应用<br>
                                            2. 打开Bark应用，复制显示的推送密钥<br>
                                            3. 将密钥粘贴到上方的"Bark Key"字段中
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 保存按钮 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                                    <i class="bi bi-arrow-counterclockwise me-1"></i>
                                    重置
                                </button>
                                <div>
                                    <button type="button" class="btn btn-outline-primary me-2" onclick="testAlerts()">
                                        <i class="bi bi-check-circle me-1"></i>
                                        测试警报
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="saveAlerts()">
                                        <i class="bi bi-check-lg me-1"></i>
                                        保存配置
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 测试警报模态框 -->
<div class="modal fade" id="testAlertModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-check-circle me-2"></i>
                    测试警报
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>选择要测试的警报类型：</p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-secondary" onclick="sendTestAlert('console')">
                        <i class="bi bi-terminal me-2"></i>
                        测试控制台警报
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="sendTestAlert('email')">
                        <i class="bi bi-envelope-fill me-2"></i>
                        测试邮件警报
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="sendTestAlert('bark')">
                        <i class="bi bi-phone me-2"></i>
                        测试Bark推送
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 保存警报配置
function saveAlerts() {
    const data = {
        enabled: document.getElementById('alertsEnabled').checked,
        rate_limit_minutes: parseInt(document.getElementById('rateLimitMinutes').value),
        console: {
            enabled: document.getElementById('consoleEnabled').checked
        },
        bark: {
            enabled: document.getElementById('barkEnabled').checked,
            keys: document.getElementById('barkKeys').value.split('\n').map(key => key.trim()).filter(key => key),
            server_url: document.getElementById('barkServerUrl').value,
            timeout: parseInt(document.getElementById('barkTimeout').value) || 10
        }
    };

    fetch('/api/admin/alerts', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlertsMessage('警报配置保存成功！', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlertsMessage('保存失败: ' + (data.error || '未知错误'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlertsMessage('保存失败，请检查网络连接', 'danger');
    });
}

// 重置表单
function resetForm() {
    showResetConfirmModal();
}

// 显示重置确认对话框
function showResetConfirmModal() {
    const modal = new bootstrap.Modal(document.getElementById('resetConfirmModal'));
    modal.show();
}

// 执行重置
function executeReset() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('resetConfirmModal'));
    modal.hide();
    location.reload();
}

// 测试警报
function testAlerts() {
    const modal = document.getElementById('testAlertModal');
    // 移除aria-hidden属性以避免可访问性问题
    modal.removeAttribute('aria-hidden');
    new bootstrap.Modal(modal).show();
}

// 发送测试警报
function sendTestAlert(type) {
    const testData = {
        type: type,
        message: `这是一条来自 Relay Monitor 的测试警报 (${new Date().toLocaleString()})`
    };

    fetch('/api/admin/test-alert', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlertsMessage(`${type}警报测试成功！请检查相应的接收渠道。`, 'success');
        } else {
            showAlertsMessage(`${type}警报测试失败: ` + (data.error || '未知错误'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlertsMessage(`${type}警报测试失败，请检查网络连接`, 'danger');
    });
}

// 显示警报页面消息
function showAlertsMessage(message, type = 'danger') {
    // 移除现有的提示
    const existingAlerts = document.querySelectorAll('.alert-message');
    existingAlerts.forEach(alert => alert.remove());

    // 创建新的提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-message mb-3`;

    const iconMap = {
        'danger': 'bi-exclamation-triangle-fill',
        'warning': 'bi-exclamation-triangle',
        'success': 'bi-check-circle-fill',
        'info': 'bi-info-circle-fill'
    };

    alertDiv.innerHTML = `
        <i class="bi ${iconMap[type] || iconMap.danger} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 尝试多个可能的容器位置
    let container = document.querySelector('.container-fluid');
    if (!container) {
        container = document.querySelector('.container');
    }
    if (!container) {
        container = document.querySelector('main');
    }
    if (!container) {
        container = document.querySelector('.card-body');
    }
    if (!container) {
        container = document.body;
    }

    // 插入到容器顶部
    if (container && container.firstChild) {
        container.insertBefore(alertDiv, container.firstChild);
    } else if (container) {
        container.appendChild(alertDiv);
    } else {
        // 如果都找不到，创建一个固定位置的提示
        alertDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        document.body.appendChild(alertDiv);
    }

    // 5秒后自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// 表单验证和交互
document.addEventListener('DOMContentLoaded', function() {
    // Bark Keys输入验证
    const barkKeys = document.getElementById('barkKeys');
    if (barkKeys) {
        barkKeys.addEventListener('blur', function() {
            const value = this.value.trim();
            if (value) {
                const keys = value.split('\n').map(key => key.trim()).filter(key => key);
                if (keys.length > 0) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            } else {
                this.classList.remove('is-invalid', 'is-valid');
            }
        });
    }

    // Bark服务器URL验证
    const barkServerUrl = document.getElementById('barkServerUrl');
    if (barkServerUrl) {
        barkServerUrl.addEventListener('blur', function() {
            const value = this.value.trim();
            if (value) {
                try {
                    new URL(value);
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } catch (e) {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            } else {
                this.classList.remove('is-invalid', 'is-valid');
            }
        });
    }

    // 数字输入验证
    const numberInputs = ['rateLimitMinutes', 'barkTimeout'];
    numberInputs.forEach(id => {
        const input = document.getElementById(id);
        if (input) {
            input.addEventListener('blur', function() {
                const value = parseInt(this.value);
                const min = parseInt(this.getAttribute('min')) || 1;
                const max = parseInt(this.getAttribute('max')) || 9999;

                if (value >= min && value <= max) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            });
        }
    });
});
</script>

<!-- 重置确认模态框 -->
<div class="modal fade" id="resetConfirmModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-arrow-counterclockwise text-warning me-2"></i>
                    确认重置
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">确定要重置所有修改吗？</p>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>警告：</strong>所有未保存的修改将会丢失，页面将重新加载。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-1"></i>
                    取消
                </button>
                <button type="button" class="btn btn-warning" onclick="executeReset()">
                    <i class="bi bi-arrow-counterclockwise me-1"></i>
                    确认重置
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

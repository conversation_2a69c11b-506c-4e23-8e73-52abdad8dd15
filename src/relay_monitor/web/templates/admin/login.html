{% extends "base.html" %}

{% block title %}管理员登录 - Relay Monitor{% endblock %}

{% block extra_head %}
<style>
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    overflow: hidden;
    max-width: 400px;
    width: 100%;
}

.login-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.login-body {
    padding: 2rem;
}

.form-floating {
    margin-bottom: 1rem;
}

.btn-login {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.btn-login:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.back-link {
    color: white;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.back-link:hover {
    color: white;
    opacity: 1;
}

.security-notice {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 1rem;
    margin-top: 1rem;
    font-size: 0.875rem;
    color: #6c757d;
}
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h1 class="h3 mb-3">
                <i class="bi bi-shield-lock me-2"></i>
                管理员登录
            </h1>
            <p class="mb-0">Relay Monitor 管理后台</p>
        </div>
        
        <div class="login-body">
            <!-- Flash消息 -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{{ 'exclamation-triangle-fill' if category == 'error' else 'info-circle-fill' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="post" id="loginForm">
                <div class="form-floating">
                    <input type="password" 
                           class="form-control" 
                           id="password" 
                           name="password" 
                           placeholder="请输入管理密码"
                           required
                           autocomplete="current-password">
                    <label for="password">
                        <i class="bi bi-lock-fill me-2"></i>管理密码
                    </label>
                </div>
                
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-login">
                        <i class="bi bi-box-arrow-in-right me-2"></i>
                        登录
                    </button>
                </div>
            </form>
            
            <div class="security-notice">
                <i class="fas fa-info-circle me-2"></i>
                <strong>安全提示：</strong>
                <ul class="mb-0 mt-2">
                    <li>管理密码在配置文件中设置</li>
                    <li>多次登录失败将导致IP被临时锁定</li>
                    <li>会话将在一定时间后自动过期</li>
                </ul>
            </div>
            
            <div class="text-center mt-3">
                <a href="{{ url_for('main.index') }}" class="back-link">
                    <i class="fas fa-arrow-left me-1"></i>
                    返回主页
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('loginForm');
    const passwordInput = document.getElementById('password');
    
    // 自动聚焦到密码输入框
    passwordInput.focus();
    
    // 表单提交处理
    form.addEventListener('submit', function(e) {
        const password = passwordInput.value.trim();
        
        if (!password) {
            e.preventDefault();
            showLoginMessage('请输入管理密码', 'warning');
            passwordInput.focus();
            return;
        }
        
        // 显示加载状态
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>登录中...';
        submitBtn.disabled = true;
        
        // 如果登录失败，恢复按钮状态
        setTimeout(function() {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    });
    
    // 回车键提交
    passwordInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            form.submit();
        }
    });

    // 显示登录页面消息
    function showLoginMessage(message, type = 'danger') {
        // 移除现有的提示
        const existingAlerts = document.querySelectorAll('.login-container .alert');
        existingAlerts.forEach(alert => alert.remove());

        // 创建新的提示框
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;

        const iconMap = {
            'danger': 'bi-exclamation-triangle-fill',
            'warning': 'bi-exclamation-triangle',
            'success': 'bi-check-circle-fill',
            'info': 'bi-info-circle-fill'
        };

        alertDiv.innerHTML = `
            <i class="bi ${iconMap[type] || iconMap.danger} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // 插入到登录表单上方
        const loginCard = document.querySelector('.login-card');
        loginCard.insertBefore(alertDiv, loginCard.firstChild);

        // 5秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
});
</script>
{% endblock %}

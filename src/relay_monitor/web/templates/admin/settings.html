{% extends "admin/base.html" %}

{% block admin_content %}
<div class="row">
    <div class="col-12">
        <div class="card admin-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear me-2"></i>
                    系统设置
                </h5>
            </div>
            <div class="card-body">
                <!-- 设置导航 -->
                <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="monitoring-tab" data-bs-toggle="tab" data-bs-target="#monitoring" type="button" role="tab">
                            <i class="bi bi-activity me-1"></i>监控设置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="database-tab" data-bs-toggle="tab" data-bs-target="#database" type="button" role="tab">
                            <i class="bi bi-database me-1"></i>数据库设置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="logging-tab" data-bs-toggle="tab" data-bs-target="#logging" type="button" role="tab">
                            <i class="bi bi-file-text me-1"></i>日志设置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="api-tab" data-bs-toggle="tab" data-bs-target="#api" type="button" role="tab">
                            <i class="bi bi-cloud me-1"></i>API设置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                            <i class="bi bi-shield-lock me-1"></i>安全设置
                        </button>
                    </li>

                </ul>

                <div class="tab-content mt-3" id="settingsTabContent">
                    <!-- 监控设置 -->
                    <div class="tab-pane fade show active" id="monitoring" role="tabpanel">
                        <form id="monitoringForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="intervalSeconds" class="form-label">监控间隔（秒）</label>
                                        <input type="number" class="form-control" id="intervalSeconds" min="10" max="3600" value="{{ config.monitoring.interval_seconds }}">
                                        <div class="form-text">价格检查的间隔时间，建议30-300秒</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="priceThreshold" class="form-label">价格变化阈值（%）</label>
                                        <input type="number" class="form-control" id="priceThreshold" min="0.1" max="50" step="0.1" value="{{ config.monitoring.price_change_threshold_percent }}">
                                        <div class="form-text">触发警报的价格变化百分比</div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="maxHistoryDays" class="form-label">历史数据保留天数</label>
                                        <input type="number" class="form-control" id="maxHistoryDays" min="1" max="365" value="{{ config.monitoring.max_history_days }}">
                                        <div class="form-text">超过此天数的数据将被自动清理</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="monitoringEnabled" {% if config.monitoring.enabled %}checked{% endif %}>
                                            <label class="form-check-label" for="monitoringEnabled">
                                                启用监控系统
                                            </label>
                                        </div>
                                        <div class="form-text">关闭后将停止所有价格监控</div>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg me-1"></i>保存监控设置
                            </button>
                        </form>
                    </div>

                    <!-- 数据库设置 -->
                    <div class="tab-pane fade" id="database" role="tabpanel">
                        <form id="databaseForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="cleanupEnabled" {% if config.database.cleanup_enabled %}checked{% endif %}>
                                            <label class="form-check-label" for="cleanupEnabled">
                                                启用自动清理
                                            </label>
                                        </div>
                                        <div class="form-text">自动清理过期的历史数据</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="cleanupInterval" class="form-label">清理检查间隔（小时）</label>
                                        <input type="number" class="form-control" id="cleanupInterval" min="1" max="168" value="{{ config.database.cleanup_interval_hours }}">
                                        <div class="form-text">多久检查一次是否需要清理数据</div>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>维护信息：</strong>
                                数据库路径：{{ config.database.path }}<br>
                                当前大小：<span id="dbSize">加载中...</span><br>
                                最后清理：<span id="lastCleanup">加载中...</span>
                            </div>
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="bi bi-check-lg me-1"></i>保存数据库设置
                            </button>
                            <button type="button" class="btn btn-warning" onclick="runCleanup()">
                                <i class="bi bi-trash me-1"></i>立即清理
                            </button>
                        </form>
                    </div>

                    <!-- 日志设置 -->
                    <div class="tab-pane fade" id="logging" role="tabpanel">
                        <form id="loggingForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="logLevel" class="form-label">日志级别</label>
                                        <select class="form-select" id="logLevel">
                                            <option value="DEBUG" {% if config.logging.level == 'DEBUG' %}selected{% endif %}>DEBUG - 详细调试信息</option>
                                            <option value="INFO" {% if config.logging.level == 'INFO' %}selected{% endif %}>INFO - 一般信息</option>
                                            <option value="WARNING" {% if config.logging.level == 'WARNING' %}selected{% endif %}>WARNING - 警告信息</option>
                                            <option value="ERROR" {% if config.logging.level == 'ERROR' %}selected{% endif %}>ERROR - 错误信息</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="maxFileSize" class="form-label">日志文件最大大小（MB）</label>
                                        <input type="number" class="form-control" id="maxFileSize" min="1" max="100" value="{{ config.logging.max_file_size_mb }}">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="backupCount" class="form-label">保留日志文件数量</label>
                                        <input type="number" class="form-control" id="backupCount" min="1" max="20" value="{{ config.logging.backup_count }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="maxLogAge" class="form-label">日志保留天数</label>
                                        <input type="number" class="form-control" id="maxLogAge" min="1" max="90" value="{{ config.logging.max_log_age_days }}">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="logCleanupEnabled" {% if config.logging.cleanup_enabled %}checked{% endif %}>
                                    <label class="form-check-label" for="logCleanupEnabled">
                                        启用日志自动清理
                                    </label>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg me-1"></i>保存日志设置
                            </button>
                        </form>
                    </div>

                    <!-- API设置 -->
                    <div class="tab-pane fade" id="api" role="tabpanel">
                        <form id="apiForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="apiTimeout" class="form-label">API超时时间（秒）</label>
                                        <input type="number" class="form-control" id="apiTimeout" min="5" max="120" value="{{ config.api.timeout }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="retryAttempts" class="form-label">重试次数</label>
                                        <input type="number" class="form-control" id="retryAttempts" min="1" max="10" value="{{ config.api.retry_attempts }}">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="retryDelay" class="form-label">重试延迟（秒）</label>
                                        <input type="number" class="form-control" id="retryDelay" min="0.1" max="10" step="0.1" value="{{ config.api.retry_delay }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="cacheTtl" class="form-label">缓存时间（秒）</label>
                                        <input type="number" class="form-control" id="cacheTtl" min="10" max="3600" value="{{ config.api.cache_ttl }}">
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>API状态：</strong>
                                基础URL：{{ config.api.base_url }}<br>
                                连接状态：<span id="apiStatus" class="badge bg-secondary">检查中...</span>
                            </div>
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="bi bi-check-lg me-1"></i>保存API设置
                            </button>
                            <button type="button" class="btn btn-info" onclick="testApiConnection()">
                                <i class="bi bi-wifi me-1"></i>测试连接
                            </button>
                        </form>
                    </div>

                    <!-- 安全设置 -->
                    <div class="tab-pane fade" id="security" role="tabpanel">
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>安全提醒：</strong>敏感信息（如密码和API密钥）存储在环境变量中，无法通过Web界面修改。
                        </div>
                        
                        <form id="securityForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="sessionTimeout" class="form-label">会话超时时间（分钟）</label>
                                        <input type="number" class="form-control" id="sessionTimeout" min="5" max="1440" value="{{ config.admin.session_timeout_minutes }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="maxLoginAttempts" class="form-label">最大登录尝试次数</label>
                                        <input type="number" class="form-control" id="maxLoginAttempts" min="3" max="20" value="{{ config.admin.max_login_attempts }}">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="lockoutDuration" class="form-label">锁定时间（分钟）</label>
                                        <input type="number" class="form-control" id="lockoutDuration" min="1" max="60" value="{{ config.admin.lockout_duration_minutes }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="adminEnabled" {% if config.admin.enabled %}checked{% endif %}>
                                            <label class="form-check-label" for="adminEnabled">
                                                启用管理界面
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg me-1"></i>保存安全设置
                            </button>
                        </form>
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 设置表单提交处理
document.addEventListener('DOMContentLoaded', function() {
    // 监控设置表单
    document.getElementById('monitoringForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveSettings('monitoring', {
            interval_seconds: parseInt(document.getElementById('intervalSeconds').value),
            price_change_threshold_percent: parseFloat(document.getElementById('priceThreshold').value),
            max_history_days: parseInt(document.getElementById('maxHistoryDays').value),
            enabled: document.getElementById('monitoringEnabled').checked
        });
    });

    // 数据库设置表单
    document.getElementById('databaseForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveSettings('database', {
            cleanup_enabled: document.getElementById('cleanupEnabled').checked,
            cleanup_interval_hours: parseInt(document.getElementById('cleanupInterval').value)
        });
    });

    // 日志设置表单
    document.getElementById('loggingForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveSettings('logging', {
            level: document.getElementById('logLevel').value,
            max_file_size_mb: parseInt(document.getElementById('maxFileSize').value),
            backup_count: parseInt(document.getElementById('backupCount').value),
            max_log_age_days: parseInt(document.getElementById('maxLogAge').value),
            cleanup_enabled: document.getElementById('logCleanupEnabled').checked
        });
    });

    // API设置表单
    document.getElementById('apiForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveSettings('api', {
            timeout: parseInt(document.getElementById('apiTimeout').value),
            retry_attempts: parseInt(document.getElementById('retryAttempts').value),
            retry_delay: parseFloat(document.getElementById('retryDelay').value),
            cache_ttl: parseInt(document.getElementById('cacheTtl').value)
        });
    });

    // 安全设置表单
    document.getElementById('securityForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveSettings('admin', {
            session_timeout_minutes: parseInt(document.getElementById('sessionTimeout').value),
            max_login_attempts: parseInt(document.getElementById('maxLoginAttempts').value),
            lockout_duration_minutes: parseInt(document.getElementById('lockoutDuration').value),
            enabled: document.getElementById('adminEnabled').checked
        });
    });

    // 加载数据库状态
    loadDatabaseStatus();
    
    // 检查API状态
    checkApiStatus();
});

function saveSettings(section, data) {
    fetch(`/api/admin/settings/${section}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('设置保存成功！', 'success');
        } else {
            showAlert('保存失败：' + data.error, 'error');
        }
    })
    .catch(error => {
        showAlert('保存失败：' + error.message, 'error');
    });
}

function runCleanup() {
    if (confirm('确定要立即清理数据库吗？这将删除过期的历史数据。')) {
        fetch('/api/admin/maintenance/cleanup', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(`清理完成！释放了 ${data.space_saved_mb} MB 空间`, 'success');
                loadDatabaseStatus();
            } else {
                showAlert('清理失败：' + data.error, 'error');
            }
        })
        .catch(error => {
            showAlert('清理失败：' + error.message, 'error');
        });
    }
}

function loadDatabaseStatus() {
    fetch('/api/admin/database/status')
    .then(response => response.json())
    .then(data => {
        document.getElementById('dbSize').textContent = data.size_mb + ' MB';
        document.getElementById('lastCleanup').textContent = data.last_cleanup || '从未';
    })
    .catch(error => {
        document.getElementById('dbSize').textContent = '获取失败';
        document.getElementById('lastCleanup').textContent = '获取失败';
    });
}

function checkApiStatus() {
    fetch('/api/admin/api/status')
    .then(response => response.json())
    .then(data => {
        const statusElement = document.getElementById('apiStatus');
        if (data.connected) {
            statusElement.textContent = '连接正常';
            statusElement.className = 'badge bg-success';
        } else {
            statusElement.textContent = '连接失败';
            statusElement.className = 'badge bg-danger';
        }
    })
    .catch(error => {
        const statusElement = document.getElementById('apiStatus');
        statusElement.textContent = '检查失败';
        statusElement.className = 'badge bg-warning';
    });
}

function testApiConnection() {
    const button = event.target;
    button.disabled = true;
    button.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>测试中...';
    
    fetch('/api/admin/api/test', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('API连接测试成功！', 'success');
        } else {
            showAlert('API连接测试失败：' + data.error, 'error');
        }
    })
    .catch(error => {
        showAlert('API连接测试失败：' + error.message, 'error');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = '<i class="bi bi-wifi me-1"></i>测试连接';
        checkApiStatus();
    });
}

function showAlert(message, type) {
    // 移除现有的提示
    const existingAlerts = document.querySelectorAll('.alert-message');
    existingAlerts.forEach(alert => alert.remove());

    // 创建警报元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show alert-message mb-3`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 尝试多个可能的容器位置
    let container = document.querySelector('.card-body');
    if (!container) {
        container = document.querySelector('.container-fluid');
    }
    if (!container) {
        container = document.querySelector('.container');
    }
    if (!container) {
        container = document.querySelector('main');
    }
    if (!container) {
        container = document.body;
    }

    // 插入到容器顶部
    if (container && container.firstChild) {
        container.insertBefore(alertDiv, container.firstChild);
    } else if (container) {
        container.appendChild(alertDiv);
    } else {
        // 如果都找不到，创建一个固定位置的提示
        alertDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        document.body.appendChild(alertDiv);
    }

    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}


</script>
{% endblock %}

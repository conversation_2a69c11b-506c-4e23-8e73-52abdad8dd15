{% extends "admin/base.html" %}

{% block extra_head %}
<style>
/* 时间线样式 */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #e9ecef;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-item.completed .timeline-marker {
    background: #198754;
    box-shadow: 0 0 0 2px #198754;
}

.timeline-content h6 {
    color: #495057;
    font-size: 0.875rem;
    font-weight: 600;
}

.timeline-content p {
    font-size: 0.8rem;
}

/* 头像样式 */
.avatar-lg {
    width: 60px;
    height: 60px;
}

.avatar-sm {
    width: 32px;
    height: 32px;
}

/* 状态徽章增强 */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
}

/* 卡片阴影 */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    font-weight: 600;
}

/* 输入框样式 */
.font-monospace {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .timeline {
        padding-left: 20px;
    }

    .timeline::before {
        left: 10px;
    }

    .timeline-marker {
        left: -17px;
        width: 10px;
        height: 10px;
    }

    .avatar-lg {
        width: 50px;
        height: 50px;
    }
}

/* 表格优化 */
.table-responsive {
    border-radius: 0.375rem;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
}

/* 过滤器样式 */
.form-select-sm, .form-control-sm {
    font-size: 0.875rem;
}

/* 加载动画 */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* 分页样式 */
.pagination-sm .page-link {
    font-size: 0.875rem;
}

/* 模态框优化 */
.modal-lg {
    max-width: 900px;
}

.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    font-weight: 600;
    color: #495057;
}
</style>
{% endblock %}

{% block admin_content %}
<div class="row">
    <div class="col-12">
        <div class="card admin-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history me-2"></i>
                    交易历史
                </h5>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshTransactions()">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        刷新
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="showStats()">
                        <i class="bi bi-bar-chart me-1"></i>
                        统计
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- 过滤器 -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label for="statusFilter" class="form-label">状态过滤</label>
                        <select class="form-select form-select-sm" id="statusFilter" onchange="filterTransactions()">
                            <option value="">全部状态</option>
                            <option value="submitted">已提交</option>
                            <option value="confirmed">已确认</option>
                            <option value="failed">失败</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="pairFilter" class="form-label">交易对过滤</label>
                        <select class="form-select form-select-sm" id="pairFilter" onchange="filterTransactions()">
                            <option value="">全部交易对</option>
                            {% for pair in pairs %}
                            <option value="{{ pair }}">{{ pair }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="pageSize" class="form-label">每页显示</label>
                        <select class="form-select form-select-sm" id="pageSize" onchange="filterTransactions()">
                            <option value="20">20条</option>
                            <option value="50">50条</option>
                            <option value="100">100条</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                            <i class="bi bi-x-circle me-1"></i>
                            清除过滤
                        </button>
                    </div>
                </div>

                <!-- 交易列表 -->
                <div id="transactionsContainer">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载交易历史...</p>
                    </div>
                </div>

                <!-- 分页 -->
                <div id="paginationContainer" class="d-flex justify-content-between align-items-center mt-3" style="display: none !important;">
                    <div id="paginationInfo" class="text-muted"></div>
                    <nav>
                        <ul class="pagination pagination-sm mb-0" id="paginationNav">
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 交易详情模态框 -->
<div class="modal fade" id="transactionDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">交易详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="transactionDetailContent">
                <!-- 详情内容将通过JavaScript填充 -->
            </div>
        </div>
    </div>
</div>

<!-- 统计模态框 -->
<div class="modal fade" id="statsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">交易统计</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="statsContent">
                <!-- 统计内容将通过JavaScript填充 -->
            </div>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
let currentFilters = {
    status: '',
    pair_name: '',
    limit: 20
};

// 页面加载时获取交易数据
document.addEventListener('DOMContentLoaded', function() {
    loadTransactions();
});

// 加载交易数据
function loadTransactions(page = 1) {
    currentPage = page;
    
    const params = new URLSearchParams({
        page: page,
        ...currentFilters
    });
    
    // 移除空值参数
    for (let [key, value] of params.entries()) {
        if (!value) {
            params.delete(key);
        }
    }
    
    fetch(`/api/admin/transactions?${params}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderTransactions(data.transactions);
            renderPagination(data.pagination);
        } else {
            showError('加载交易数据失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('网络错误，请检查连接');
    });
}

// 渲染交易列表
function renderTransactions(transactions) {
    const container = document.getElementById('transactionsContainer');
    
    if (transactions.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <div class="mb-4">
                    <div class="avatar-lg rounded-circle bg-light d-inline-flex align-items-center justify-content-center">
                        <i class="bi bi-inbox display-4 text-muted"></i>
                    </div>
                </div>
                <h4 class="text-muted mb-2">暂无交易记录</h4>
                <p class="text-muted mb-3">当前过滤条件下没有找到交易记录</p>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="clearFilters()">
                    <i class="bi bi-funnel me-1"></i>清除过滤器
                </button>
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="table-responsive">
            <table class="table table-hover align-middle">
                <thead class="table-light">
                    <tr>
                        <th class="border-0"><i class="bi bi-clock me-1"></i>时间</th>
                        <th class="border-0"><i class="bi bi-arrow-left-right me-1"></i>交易对</th>
                        <th class="border-0 text-center"><i class="bi bi-check-circle me-1"></i>状态</th>
                        <th class="border-0 text-center"><i class="bi bi-box-arrow-in-down me-1"></i>输入</th>
                        <th class="border-0 text-center"><i class="bi bi-box-arrow-up me-1"></i>输出</th>
                        <th class="border-0 text-center"><i class="bi bi-cash-coin me-1"></i>费用</th>
                        <th class="border-0 text-center"><i class="bi bi-wallet2 me-1"></i>钱包</th>
                        <th class="border-0 text-center">操作</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    transactions.forEach(tx => {
        const statusBadge = getStatusBadge(tx.status);
        const createdAt = new Date(tx.created_at).toLocaleString('zh-CN');
        
        html += `
            <tr class="border-bottom">
                <td class="py-3">
                    <div class="d-flex flex-column">
                        <span class="fw-medium">${new Date(tx.created_at).toLocaleDateString('zh-CN')}</span>
                        <small class="text-muted">${new Date(tx.created_at).toLocaleTimeString('zh-CN')}</small>
                    </div>
                </td>
                <td class="py-3">
                    <div class="d-flex align-items-center">
                        <div class="avatar-sm rounded-circle bg-primary bg-opacity-10 d-flex align-items-center justify-content-center me-2">
                            <i class="bi bi-arrow-left-right text-primary"></i>
                        </div>
                        <div>
                            <div class="fw-medium">${tx.monitor_pair_name}</div>
                            <small class="text-muted">ID: ${tx.id}</small>
                        </div>
                    </div>
                </td>
                <td class="py-3 text-center">${statusBadge}</td>
                <td class="py-3 text-center">
                    <div class="d-flex flex-column align-items-center">
                        <span class="fw-bold text-danger">${tx.amount_in || '-'}</span>
                        <small class="text-muted">${tx.token_in_symbol || ''}</small>
                    </div>
                </td>
                <td class="py-3 text-center">
                    <div class="d-flex flex-column align-items-center">
                        <span class="fw-bold text-success">${tx.amount_out || '-'}</span>
                        <small class="text-muted">${tx.token_out_symbol || ''}</small>
                    </div>
                </td>
                <td class="py-3 text-center">
                    <span class="badge bg-warning bg-opacity-10 text-warning">$${parseFloat(tx.total_fee_usd || 0).toFixed(4)}</span>
                </td>
                <td class="py-3 text-center">
                    <div class="d-flex flex-column align-items-center">
                        <span class="fw-medium">${tx.wallet_name || '-'}</span>
                        ${tx.wallet_address ? `<small class="text-muted font-monospace">${tx.wallet_address.substring(0, 8)}...${tx.wallet_address.substring(tx.wallet_address.length - 6)}</small>` : ''}
                    </div>
                </td>
                <td class="py-3 text-center">
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary"
                                onclick="showTransactionDetail('${tx.id}')" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${tx.tx_hash ? `
                            <button type="button" class="btn btn-outline-info"
                                    onclick="copyToClipboard(\`${tx.tx_hash}\`)" title="复制哈希">
                                <i class="bi bi-copy"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    container.innerHTML = html;
}

// 获取状态徽章
function getStatusBadge(status) {
    const badges = {
        'submitted': '<span class="badge bg-warning">已提交</span>',
        'confirmed': '<span class="badge bg-success">已确认</span>',
        'failed': '<span class="badge bg-danger">失败</span>'
    };
    return badges[status] || `<span class="badge bg-secondary">${status}</span>`;
}

// 渲染分页
function renderPagination(pagination) {
    const container = document.getElementById('paginationContainer');
    const info = document.getElementById('paginationInfo');
    const nav = document.getElementById('paginationNav');
    
    if (pagination.total === 0) {
        container.style.display = 'none';
        return;
    }
    
    container.style.display = 'flex';
    
    // 分页信息
    const start = (pagination.page - 1) * pagination.limit + 1;
    const end = Math.min(pagination.page * pagination.limit, pagination.total);
    info.textContent = `显示 ${start}-${end} 条，共 ${pagination.total} 条记录`;
    
    // 分页导航
    let navHtml = '';
    
    // 上一页
    if (pagination.page > 1) {
        navHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadTransactions(${pagination.page - 1})">上一页</a>
            </li>
        `;
    }
    
    // 页码
    const maxPages = 5;
    let startPage = Math.max(1, pagination.page - Math.floor(maxPages / 2));
    let endPage = Math.min(pagination.pages, startPage + maxPages - 1);
    
    if (endPage - startPage < maxPages - 1) {
        startPage = Math.max(1, endPage - maxPages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
        navHtml += `
            <li class="page-item ${i === pagination.page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadTransactions(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    if (pagination.page < pagination.pages) {
        navHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadTransactions(${pagination.page + 1})">下一页</a>
            </li>
        `;
    }
    
    nav.innerHTML = navHtml;
}

// 过滤交易
function filterTransactions() {
    currentFilters.status = document.getElementById('statusFilter').value;
    currentFilters.pair_name = document.getElementById('pairFilter').value;
    currentFilters.limit = parseInt(document.getElementById('pageSize').value);
    
    loadTransactions(1);
}

// 清除过滤器
function clearFilters() {
    document.getElementById('statusFilter').value = '';
    document.getElementById('pairFilter').value = '';
    document.getElementById('pageSize').value = '20';
    
    currentFilters = {
        status: '',
        pair_name: '',
        limit: 20
    };
    
    loadTransactions(1);
}

// 刷新交易数据
function refreshTransactions() {
    loadTransactions(currentPage);
}

// 显示交易详情
function showTransactionDetail(transactionId) {
    // 从当前数据中查找交易详情
    fetch(`/api/admin/transactions?limit=1000`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const transaction = data.transactions.find(tx => tx.id == transactionId);
            if (transaction) {
                renderTransactionDetail(transaction);
                new bootstrap.Modal(document.getElementById('transactionDetailModal')).show();
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('获取交易详情失败');
    });
}

// 渲染交易详情
function renderTransactionDetail(tx) {
    const content = document.getElementById('transactionDetailContent');

    const createdAt = new Date(tx.created_at).toLocaleString('zh-CN');
    const submittedAt = tx.submitted_at ? new Date(tx.submitted_at).toLocaleString('zh-CN') : '-';
    const confirmedAt = tx.confirmed_at ? new Date(tx.confirmed_at).toLocaleString('zh-CN') : '-';

    content.innerHTML = `
        <!-- 交易状态卡片 -->
        <div class="card border-0 bg-light mb-3">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="avatar-lg rounded-circle bg-primary d-flex align-items-center justify-content-center">
                            <i class="bi bi-arrow-left-right text-white fs-4"></i>
                        </div>
                    </div>
                    <div class="col">
                        <h5 class="mb-1">${tx.monitor_pair_name}</h5>
                        <p class="text-muted mb-0">交易ID: #${tx.id}</p>
                    </div>
                    <div class="col-auto">
                        ${getStatusBadge(tx.status)}
                    </div>
                </div>
            </div>
        </div>

        <!-- 交易流程时间线 -->
        <div class="mb-4">
            <h6 class="mb-3"><i class="bi bi-clock-history me-2"></i>交易时间线</h6>
            <div class="timeline">
                <div class="timeline-item ${tx.created_at ? 'completed' : ''}">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h6 class="mb-1">交易创建</h6>
                        <p class="text-muted mb-0">${createdAt}</p>
                    </div>
                </div>
                <div class="timeline-item ${tx.submitted_at ? 'completed' : ''}">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h6 class="mb-1">交易提交</h6>
                        <p class="text-muted mb-0">${submittedAt}</p>
                    </div>
                </div>
                <div class="timeline-item ${tx.confirmed_at ? 'completed' : ''}">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h6 class="mb-1">交易确认</h6>
                        <p class="text-muted mb-0">${confirmedAt}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 交易详情卡片组 -->
        <div class="row g-3 mb-4">
            <!-- 输入输出 -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-transparent">
                        <h6 class="mb-0"><i class="bi bi-arrow-down-up me-2"></i>交易详情</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="text-center">
                                <div class="text-muted small">输入</div>
                                <div class="fw-bold text-danger">${tx.amount_in || '-'}</div>
                                <div class="small text-muted">${tx.token_in_symbol || ''}</div>
                            </div>
                            <div class="text-center">
                                <i class="bi bi-arrow-right text-primary fs-4"></i>
                            </div>
                            <div class="text-center">
                                <div class="text-muted small">输出</div>
                                <div class="fw-bold text-success">${tx.amount_out || '-'}</div>
                                <div class="small text-muted">${tx.token_out_symbol || ''}</div>
                            </div>
                        </div>
                        <hr>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="text-muted small">触发价格</div>
                                <div class="fw-bold">${tx.trigger_price || '-'}</div>
                            </div>
                            <div class="col-6">
                                <div class="text-muted small">执行价格</div>
                                <div class="fw-bold">${tx.execution_price || '-'}</div>
                            </div>
                        </div>
                        ${tx.slippage_percent ? `
                        <div class="text-center mt-2">
                            <div class="text-muted small">滑点</div>
                            <div class="fw-bold ${parseFloat(tx.slippage_percent) > 5 ? 'text-warning' : 'text-success'}">${tx.slippage_percent}%</div>
                        </div>
                        ` : ''}
                    </div>
                </div>
            </div>

            <!-- 费用信息 -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-transparent">
                        <h6 class="mb-0"><i class="bi bi-cash-coin me-2"></i>费用信息</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="text-center p-2 bg-light rounded">
                                    <div class="text-muted small">Gas使用</div>
                                    <div class="fw-bold">${tx.gas_used || '-'}</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-2 bg-light rounded">
                                    <div class="text-muted small">Gas价格</div>
                                    <div class="fw-bold">${tx.gas_price || '-'}</div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="text-center p-3 bg-warning bg-opacity-10 rounded">
                                    <div class="text-muted small">总费用</div>
                                    <div class="fs-5 fw-bold text-warning">$${parseFloat(tx.total_fee_usd || 0).toFixed(4)}</div>
                                </div>
                            </div>
                            ${tx.retry_count > 0 ? `
                            <div class="col-12">
                                <div class="text-center p-2 bg-info bg-opacity-10 rounded">
                                    <div class="text-muted small">重试次数</div>
                                    <div class="fw-bold text-info">${tx.retry_count}</div>
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 钱包信息 -->
        <div class="card mb-3">
            <div class="card-header bg-transparent">
                <h6 class="mb-0"><i class="bi bi-wallet2 me-2"></i>钱包信息</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-2">
                            <span class="text-muted">钱包名称:</span>
                            <span class="fw-bold ms-2">${tx.wallet_name || '-'}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-2">
                            <span class="text-muted">钱包地址:</span>
                            <span class="font-monospace small ms-2">${tx.wallet_address || '-'}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-md-6">
                <h6>交易详情</h6>
                <table class="table table-sm">
                    <tr><td>输入金额:</td><td>${tx.amount_in || '-'} ${tx.token_in_symbol || ''}</td></tr>
                    <tr><td>输出金额:</td><td>${tx.amount_out || '-'} ${tx.token_out_symbol || ''}</td></tr>
                    <tr><td>触发价格:</td><td>${tx.trigger_price || '-'}</td></tr>
                    <tr><td>执行价格:</td><td>${tx.execution_price || '-'}</td></tr>
                    <tr><td>滑点:</td><td>${tx.slippage_percent || '-'}%</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>费用信息</h6>
                <table class="table table-sm">
                    <tr><td>Gas使用:</td><td>${tx.gas_used || '-'}</td></tr>
                    <tr><td>Gas价格:</td><td>${tx.gas_price || '-'}</td></tr>
                    <tr><td>总费用:</td><td>$${parseFloat(tx.total_fee_usd || 0).toFixed(4)}</td></tr>
                    <tr><td>重试次数:</td><td>${tx.retry_count || 0}</td></tr>
                </table>
            </div>
        </div>
        
        <!-- 技术信息 -->
        ${tx.tx_hash || tx.request_id ? `
        <div class="card mb-3">
            <div class="card-header bg-transparent">
                <h6 class="mb-0"><i class="bi bi-code-square me-2"></i>技术信息</h6>
            </div>
            <div class="card-body">
                ${tx.tx_hash ? `
                <div class="mb-3">
                    <label class="form-label text-muted">交易哈希</label>
                    <div class="input-group">
                        <input type="text" class="form-control font-monospace" value="${tx.tx_hash}" readonly>
                        <button class="btn btn-outline-primary" onclick="copyToClipboard(\`${tx.tx_hash}\`)">
                            <i class="bi bi-copy"></i>
                        </button>
                    </div>
                </div>
                ` : ''}

                ${tx.request_id ? `
                <div class="mb-0">
                    <label class="form-label text-muted">请求ID</label>
                    <div class="input-group">
                        <input type="text" class="form-control font-monospace" value="${tx.request_id}" readonly>
                        <button class="btn btn-outline-primary" onclick="copyToClipboard(\`${tx.request_id}\`)">
                            <i class="bi bi-copy"></i>
                        </button>
                    </div>
                </div>
                ` : ''}
            </div>
        </div>
        ` : ''}

        <!-- 错误信息 -->
        ${tx.error_message ? `
        <div class="card border-danger">
            <div class="card-header bg-danger bg-opacity-10 border-danger">
                <h6 class="mb-0 text-danger"><i class="bi bi-exclamation-triangle me-2"></i>错误信息</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-danger mb-0">
                    <i class="bi bi-x-circle me-2"></i>${tx.error_message}
                </div>
            </div>
        </div>
        ` : ''}
    `;
}

// 显示统计信息
function showStats() {
    fetch('/api/admin/transactions/stats')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderStats(data.stats);
            new bootstrap.Modal(document.getElementById('statsModal')).show();
        } else {
            showError('获取统计信息失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('网络错误，请检查连接');
    });
}

// 渲染统计信息
function renderStats(stats) {
    const content = document.getElementById('statsContent');
    
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>总体统计</h6>
                <table class="table table-sm">
                    <tr><td>总交易数:</td><td>${stats.total_transactions}</td></tr>
                    <tr><td>已确认:</td><td class="text-success">${stats.confirmed_count}</td></tr>
                    <tr><td>失败:</td><td class="text-danger">${stats.failed_count}</td></tr>
                    <tr><td>待确认:</td><td class="text-warning">${stats.pending_count}</td></tr>
                    <tr><td>成功率:</td><td>${stats.success_rate.toFixed(2)}%</td></tr>
                    <tr><td>总费用:</td><td>$${stats.total_fees_usd.toFixed(4)}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>今日统计</h6>
                <table class="table table-sm">
                    <tr><td>今日交易:</td><td>${stats.today_transactions}</td></tr>
                    <tr><td>今日确认:</td><td class="text-success">${stats.today_confirmed}</td></tr>
                    <tr><td>今日费用:</td><td>$${stats.today_fees_usd.toFixed(4)}</td></tr>
                </table>
            </div>
        </div>
        
        ${stats.pair_stats.length > 0 ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6>交易对统计</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>交易对</th>
                                <th>总数</th>
                                <th>成功</th>
                                <th>成功率</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${stats.pair_stats.map(pair => `
                                <tr>
                                    <td>${pair.pair_name}</td>
                                    <td>${pair.total_count}</td>
                                    <td class="text-success">${pair.confirmed_count}</td>
                                    <td>${pair.success_rate.toFixed(2)}%</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        ` : ''}
    `;
}

// 复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showSuccess('已复制到剪贴板');
    }).catch(err => {
        console.error('复制失败:', err);
        showError('复制失败');
    });
}

// 清除过滤器
function clearFilters() {
    document.getElementById('statusFilter').value = '';
    document.getElementById('pairFilter').value = '';
    document.getElementById('walletFilter').value = '';
    document.getElementById('dateFilter').value = '';
    loadTransactions();
}

// 显示成功消息
function showSuccess(message) {
    // 这里可以使用toast或其他通知方式
    alert(message);
}

// 显示错误消息
function showError(message) {
    // 这里可以使用toast或其他通知方式
    alert('错误: ' + message);
}
</script>
{% endblock %}

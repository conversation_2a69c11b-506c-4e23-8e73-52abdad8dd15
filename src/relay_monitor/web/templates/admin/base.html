<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }} - 管理后台</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">


<style>
.admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    margin-bottom: 2rem;
}

.admin-nav {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0.5rem 0;
}

.admin-nav .nav-link {
    color: #495057;
    font-weight: 500;
}

.admin-nav .nav-link.active {
    color: #007bff;
    background-color: #e9ecef;
    border-radius: 0.25rem;
}

.admin-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.admin-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

.status-badge {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

.session-info {
    font-size: 0.875rem;
    color: #6c757d;
}
</style>
</head>
<body>
<!-- 管理后台头部 -->
<div class="admin-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="h3 mb-0">
                    <i class="bi bi-gear-fill me-2"></i>
                    Relay Monitor 管理后台
                </h1>
            </div>
            <div class="col-md-6 text-end">
                <div class="session-info text-light">
                    {% if session_info %}
                    <i class="bi bi-person-fill me-1"></i>
                    登录时间: {{ session_info.login_time.strftime('%H:%M:%S') }}
                    <span class="ms-3">
                        <i class="bi bi-clock-fill me-1"></i>
                        会话到期: {{ session_info.expires_at.strftime('%H:%M:%S') }}
                    </span>
                    {% endif %}
                </div>
                <div class="mt-2">
                    <a href="{{ url_for('main.index') }}" class="btn btn-outline-light btn-sm me-2">
                        <i class="bi bi-house-fill me-1"></i>返回主页
                    </a>
                    <a href="{{ url_for('admin.logout') }}" class="btn btn-outline-light btn-sm">
                        <i class="bi bi-box-arrow-right me-1"></i>登出
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 管理导航 -->
<div class="admin-nav">
    <div class="container">
        <ul class="nav nav-pills">
            <li class="nav-item">
                <a class="nav-link {% if request.endpoint == 'admin.dashboard' %}active{% endif %}"
                   href="{{ url_for('admin.dashboard') }}">
                    <i class="bi bi-speedometer2 me-1"></i>仪表板
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if request.endpoint == 'admin.pairs' %}active{% endif %}"
                   href="{{ url_for('admin.pairs') }}">
                    <i class="bi bi-arrow-left-right me-1"></i>交易对管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if request.endpoint == 'admin.alerts' %}active{% endif %}"
                   href="{{ url_for('admin.alerts') }}">
                    <i class="bi bi-bell-fill me-1"></i>警报配置
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if request.endpoint == 'admin.emails' %}active{% endif %}"
                   href="{{ url_for('admin.emails') }}">
                    <i class="bi bi-envelope me-1"></i>邮件管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if request.endpoint == 'admin.wallets' %}active{% endif %}"
                   href="{{ url_for('admin.wallets') }}">
                    <i class="bi bi-wallet2 me-1"></i>钱包管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if request.endpoint == 'admin.trading' %}active{% endif %}"
                   href="{{ url_for('admin.trading') }}">
                    <i class="bi bi-robot me-1"></i>自动交易
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if request.endpoint == 'admin.transactions' %}active{% endif %}"
                   href="{{ url_for('admin.transactions') }}">
                    <i class="bi bi-clock-history me-1"></i>交易历史
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {% if request.endpoint == 'admin.settings' %}active{% endif %}"
                   href="{{ url_for('admin.settings') }}">
                    <i class="bi bi-gear me-1"></i>系统设置
                </a>
            </li>
        </ul>
    </div>
</div>

<!-- 主要内容区域 -->
<div class="container">
    <!-- Flash消息 -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- 页面内容 -->
    {% block admin_content %}{% endblock %}
</div>
<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// 自动刷新会话信息
function updateSessionInfo() {
    const sessionElements = document.querySelectorAll('.session-info');
    if (sessionElements.length > 0) {
        // 这里可以添加AJAX调用来更新会话信息
        // 暂时只是简单的时间更新
        setTimeout(updateSessionInfo, 60000); // 每分钟更新一次
    }
}

// 页面加载完成后启动
document.addEventListener('DOMContentLoaded', function() {
    updateSessionInfo();
    
    // 添加确认对话框到危险操作 - 已移除原生confirm，使用Bootstrap模态框
});
</script>
{% block extra_js %}{% endblock %}

</body>
</html>

{% extends "admin/base.html" %}

{% block title %}自动交易配置{% endblock %}

{% block admin_content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-robot me-2"></i>自动交易配置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>注意：</strong>自动交易功能将在价格监控触发告警时自动执行跨链交易。请谨慎配置交易参数和风险控制。
                    </div>
                    
                    <!-- 交易对配置 -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>交易对</th>
                                    <th>自动交易</th>
                                    <th>钱包</th>
                                    <th>交易金额（来自监控配置）</th>
                                    <th>测试交易</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for pair in pairs %}
                                <tr>
                                    <td>
                                        <strong>{{ pair.name }}</strong>
                                        <br>
                                        <small class="text-muted">
                                            {{ pair.origin_chain }} {{ pair.origin_token }} → 
                                            {{ pair.destination_chain }} {{ pair.destination_token }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" 
                                                   id="autoTrading_{{ loop.index }}"
                                                   {% if pair.auto_trading_enabled %}checked{% endif %}
                                                   onchange="toggleAutoTrading('{{ pair.name }}', this.checked)">
                                            <label class="form-check-label" for="autoTrading_{{ loop.index }}">
                                                {% if pair.auto_trading_enabled %}已启用{% else %}已禁用{% endif %}
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <select class="form-select form-select-sm" 
                                                onchange="updateTradingConfig('{{ pair.name }}', 'wallet_name', this.value)">
                                            <option value="">选择钱包</option>
                                            {% for wallet in wallets %}
                                            <option value="{{ wallet.name }}" 
                                                    {% if pair.wallet_name == wallet.name %}selected{% endif %}>
                                                {{ wallet.name }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ pair.amount }}</span>
                                        <small class="text-muted d-block">使用监控配置</small>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-success"
                                                onclick="showTestTradeModal('{{ pair.name }}')"
                                                {% if not pair.auto_trading_enabled or not pair.wallet_name %}disabled{% endif %}>
                                            <i class="fas fa-play me-1"></i>测试交易
                                        </button>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="5" class="text-center text-muted">
                                        <i class="fas fa-info-circle me-2"></i>暂无监控对，请先添加监控对
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- 测试交易模态框 -->
<div class="modal fade" id="testTradeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-play me-2"></i>测试交易
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>注意：</strong>这将执行真实的跨链交易，请确认交易参数无误。
                </div>

                <form id="testTradeForm">
                    <input type="hidden" id="testPairName">

                    <div class="mb-3">
                        <label class="form-label">交易对</label>
                        <input type="text" class="form-control" id="testPairDisplay" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">使用钱包</label>
                        <input type="text" class="form-control" id="testWalletDisplay" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="testAmount" class="form-label">测试交易金额</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="testAmount"
                                   min="0" step="0.000001" placeholder="输入测试金额" required>
                            <span class="input-group-text" id="testAmountUnit">ETH</span>
                        </div>
                        <div class="form-text">
                            建议使用小额进行测试，如 0.001 ETH
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">交易参数</label>
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">最大滑点：</small>
                                <span class="badge bg-info">20%</span>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">交易类型：</small>
                                <span class="badge bg-primary">手动测试</span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" onclick="executeTestTrade()">
                    <i class="fas fa-play me-1"></i>执行测试交易
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 消息提示区域 -->
<div id="messageArea"></div>
{% endblock %}

{% block extra_js %}
<script>
// 显示消息
function showMessage(message, type = 'info') {
    const messageArea = document.getElementById('messageArea');
    const alertClass = type === 'error' ? 'alert-danger' : 
                      type === 'success' ? 'alert-success' : 
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    messageArea.innerHTML = alertHtml;
    
    // 自动隐藏成功消息
    if (type === 'success') {
        setTimeout(() => {
            const alert = messageArea.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 3000);
    }
}

// 切换自动交易状态
function toggleAutoTrading(pairName, enabled) {
    updateTradingConfig(pairName, 'auto_trading_enabled', enabled);
}

// 更新交易配置
function updateTradingConfig(pairName, field, value) {
    const data = {
        [field]: value
    };
    
    fetch(`/api/admin/pairs/${encodeURIComponent(pairName)}/trading`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('配置更新成功', 'success');
        } else {
            showMessage(data.error || '配置更新失败', 'error');
            // 刷新页面以恢复原始状态
            setTimeout(() => location.reload(), 1000);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('网络错误，请重试', 'error');
        // 刷新页面以恢复原始状态
        setTimeout(() => location.reload(), 1000);
    });
}

// 显示测试交易模态框
function showTestTradeModal(pairName) {
    // 获取交易对配置
    fetch(`/api/admin/pairs/${encodeURIComponent(pairName)}/trading`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const config = data.config;

            // 填充模态框信息
            document.getElementById('testPairName').value = pairName;
            document.getElementById('testPairDisplay').value = pairName;
            document.getElementById('testWalletDisplay').value = config.wallet_name || '未选择';
            document.getElementById('testAmount').value = config.trading_amount || '';

            // 根据交易对设置币种单位
            const originToken = pairName.split('_')[1] || 'ETH';
            document.getElementById('testAmountUnit').textContent = originToken;

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('testTradeModal'));
            modal.show();
        } else {
            showMessage('获取交易配置失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('网络错误，请重试', 'error');
    });
}

// 执行测试交易
function executeTestTrade() {
    const pairName = document.getElementById('testPairName').value;
    const testAmount = document.getElementById('testAmount').value;

    if (!testAmount || parseFloat(testAmount) <= 0) {
        showMessage('请输入有效的测试金额', 'error');
        return;
    }

    // 确认对话框
    if (!confirm(`确定要执行测试交易吗？\n\n交易对：${pairName}\n金额：${testAmount}\n\n这将执行真实的跨链交易！`)) {
        return;
    }

    // 禁用按钮，显示加载状态
    const executeBtn = document.querySelector('#testTradeModal .btn-success');
    const originalText = executeBtn.innerHTML;
    executeBtn.disabled = true;
    executeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>执行中...';

    // 执行测试交易
    fetch(`/api/admin/pairs/${encodeURIComponent(pairName)}/test-trade`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            test_amount: parseFloat(testAmount)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(`测试交易执行成功！\n交易哈希：${data.transaction_hash || '未知'}`, 'success');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('testTradeModal'));
            modal.hide();
        } else {
            showMessage(`测试交易失败：${data.error || '未知错误'}`, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('网络错误，请重试', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        executeBtn.disabled = false;
        executeBtn.innerHTML = originalText;
    });
}


</script>
{% endblock %}

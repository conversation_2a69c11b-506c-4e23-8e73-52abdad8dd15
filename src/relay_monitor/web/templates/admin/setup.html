{% extends "base.html" %}

{% block title %}首次设置 - Relay Monitor{% endblock %}

{% block extra_head %}
<style>
.setup-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.setup-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    overflow: hidden;
    max-width: 500px;
    width: 100%;
}

.setup-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.setup-body {
    padding: 2rem;
}

.form-floating {
    margin-bottom: 1rem;
}

.btn-setup {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-setup:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.setup-notice {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1.5rem;
    font-size: 0.875rem;
    color: #6c757d;
}

.setup-notice ul {
    margin-bottom: 0;
    padding-left: 1.2rem;
}

.setup-notice li {
    margin-bottom: 0.25rem;
}

.optional-section {
    border-top: 1px solid #dee2e6;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
}

.optional-section h6 {
    color: #6c757d;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 1rem;
}
</style>
{% endblock %}

{% block content %}
<div class="setup-container">
    <div class="setup-card">
        <div class="setup-header">
            <h1 class="h3 mb-3">
                <i class="bi bi-gear-fill me-2"></i>
                首次设置
            </h1>
            <p class="mb-0">欢迎使用 Relay Monitor</p>
            <small>请设置管理员密码以开始使用</small>
        </div>
        
        <div class="setup-body">
            <!-- Flash消息 -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{{ 'exclamation-triangle-fill' if category == 'error' else 'info-circle-fill' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="post" id="setupForm">
                <!-- 管理员密码设置 -->
                <div class="form-floating">
                    <input type="password" 
                           class="form-control" 
                           id="admin_password" 
                           name="admin_password" 
                           placeholder="请输入管理员密码"
                           required
                           minlength="6"
                           autocomplete="new-password">
                    <label for="admin_password">
                        <i class="bi bi-lock-fill me-2"></i>管理员密码
                    </label>
                </div>
                
                <div class="form-floating">
                    <input type="password" 
                           class="form-control" 
                           id="confirm_password" 
                           name="confirm_password" 
                           placeholder="请确认管理员密码"
                           required
                           minlength="6"
                           autocomplete="new-password">
                    <label for="confirm_password">
                        <i class="bi bi-lock-fill me-2"></i>确认密码
                    </label>
                </div>

                <!-- 可选配置 -->
                <div class="optional-section">
                    <h6>
                        <i class="bi bi-bell me-2"></i>
                        通知配置（可选）
                    </h6>
                    
                    <div class="form-floating">
                        <input type="text"
                               class="form-control"
                               id="bark_keys"
                               name="bark_keys"
                               placeholder="请输入Bark API密钥">
                        <label for="bark_keys">
                            <i class="bi bi-phone me-2"></i>Bark API密钥（多个用逗号分隔）
                        </label>
                    </div>

                    <div class="alert alert-info mt-3">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>邮件通知：</strong>系统已内置SMTP.dev邮件服务，无需额外配置即可发送邮件通知。
                    </div>
                </div>
                
                <div class="d-grid mt-3">
                    <button type="submit" class="btn btn-primary btn-setup">
                        <i class="bi bi-check-circle me-2"></i>
                        完成设置
                    </button>
                </div>
            </form>
            
            <div class="setup-notice">
                <i class="bi bi-info-circle me-2"></i>
                <strong>设置说明：</strong>
                <ul class="mt-2">
                    <li>管理员密码用于访问管理后台，请设置复杂密码</li>
                    <li>Bark API密钥用于手机推送通知（可选）</li>
                    <li>邮件通知已内置，无需额外配置</li>
                    <li>通知配置可以在设置完成后在管理页面中修改</li>
                    <li>交易对监控可以在管理页面中添加和配置</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('setupForm');
    const password = document.getElementById('admin_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    // 密码确认验证
    function validatePasswords() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('密码不一致');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    password.addEventListener('input', validatePasswords);
    confirmPassword.addEventListener('input', validatePasswords);
    
    // 表单提交验证
    form.addEventListener('submit', function(e) {
        if (password.value !== confirmPassword.value) {
            e.preventDefault();
            alert('两次输入的密码不一致，请重新输入');
            return false;
        }
        
        if (password.value.length < 6) {
            e.preventDefault();
            alert('密码长度至少6位');
            return false;
        }
    });
});
</script>
{% endblock %}

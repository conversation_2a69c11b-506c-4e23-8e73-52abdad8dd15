<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Relay Monitor{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Chart.js Date Adapter -->
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-running {
            background-color: #28a745;
        }
        .status-stopped {
            background-color: #dc3545;
        }
        .price-change-positive {
            color: #28a745;
        }
        .price-change-negative {
            color: #dc3545;
        }
        .card-metric {
            text-align: center;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .alert-item {
            border-left: 4px solid #007bff;
            padding-left: 15px;
        }
        .alert-price-change {
            border-left-color: #ffc107;
        }
        .alert-system {
            border-left-color: #dc3545;
        }
        .table-responsive {
            max-height: 400px;
            overflow-y: auto;
        }
        .footer {
            margin-top: 50px;
            padding: 20px 0;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            text-align: center;
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="bi bi-graph-up"></i> Relay Monitor
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">
                            <i class="bi bi-house"></i> 仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.alerts_page') }}">
                            <i class="bi bi-bell"></i> 警报
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.status') }}">
                            <i class="bi bi-gear"></i> 状态
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.dashboard') }}" title="管理后台">
                            <i class="bi bi-shield-lock"></i> 管理
                        </a>
                    </li>
                </ul>

                <span class="navbar-text" id="status-indicator">
                    <span class="status-indicator status-stopped"></span>
                    <span id="status-text">检查中...</span>
                </span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Flash Messages Container -->
        <div id="flash-messages"></div>

        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <div class="footer">
        <div class="container">
            <p>&copy; 2025 Relay Monitor. 基于 Flask 和 Bootstrap 构建。</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Common utilities -->
    <script src="{{ url_for('static', filename='js/common.js') }}"></script>

    <!-- Auto-refresh functionality -->
    <script>
        // Update status indicator
        function updateStatus() {
            fetch('/api/monitor/status')
                .then(response => response.json())
                .then(data => {
                    const indicator = document.querySelector('.status-indicator');
                    const text = document.getElementById('status-text');

                    if (data.running) {
                        indicator.className = 'status-indicator status-running';
                        text.textContent = '运行中';
                    } else {
                        indicator.className = 'status-indicator status-stopped';
                        text.textContent = '已停止';
                    }
                })
                .catch(error => {
                    console.error('Error fetching status:', error);
                    const indicator = document.querySelector('.status-indicator');
                    const text = document.getElementById('status-text');
                    indicator.className = 'status-indicator status-stopped';
                    text.textContent = '错误';
                });
        }

        // Auto-refresh page content every 30 seconds if monitoring is running
        function setupAutoRefresh() {
            updateStatus(); // Initial status check

            setInterval(updateStatus, 5000); // Update status every 5 seconds

            // Auto-refresh page content every 30 seconds if monitoring is running
            setInterval(() => {
                fetch('/api/monitor/status')
                    .then(response => response.json())
                    .then(data => {
                        if (data.running) {
                            refreshPageContent();
                        }
                    })
                    .catch(error => console.error('Error:', error));
            }, 30000);
        }

        // 异步刷新页面内容
        function refreshPageContent() {
            // 只在首页进行异步刷新
            if (window.location.pathname === '/' || window.location.pathname === '/index') {
                refreshDashboardData();
            }
        }

        // 刷新仪表板数据
        function refreshDashboardData() {
            // 显示加载指示器
            showRefreshIndicator();

            // 获取最新的仪表板数据
            fetch('/api/dashboard/data')
                .then(response => response.json())
                .then(data => {
                    updateDashboardContent(data);
                    hideRefreshIndicator();
                })
                .catch(error => {
                    console.error('刷新数据失败:', error);
                    hideRefreshIndicator();
                    // 如果异步刷新失败，回退到页面刷新
                    window.location.reload();
                });
        }

        // 显示刷新指示器
        function showRefreshIndicator() {
            const indicator = document.getElementById('refresh-indicator');
            if (indicator) {
                indicator.style.display = 'inline-block';
            }
        }

        // 隐藏刷新指示器
        function hideRefreshIndicator() {
            const indicator = document.getElementById('refresh-indicator');
            if (indicator) {
                indicator.style.display = 'none';
            }
        }

        // 更新仪表板内容
        function updateDashboardContent(data) {
            // 更新监控交易对表格
            if (data.pairs && document.querySelector('.table tbody')) {
                updatePairsTable(data.pairs);
            }

            // 更新最近警报
            if (data.recent_alerts && document.querySelector('.alert-item')) {
                updateRecentAlerts(data.recent_alerts);
            }

            // 更新系统状态卡片
            if (data.monitor_status) {
                updateStatusCards(data.monitor_status);
            }
        }

        // 更新交易对表格
        function updatePairsTable(pairs) {
            const tbody = document.querySelector('.table tbody');
            if (!tbody) return;

            let html = '';
            pairs.forEach(pair => {
                const latestPrice = pair.latest_price ? formatNumber(pair.latest_price, 6) : '无数据';
                const feeUsd = pair.latest_fee_usd ? '$' + formatNumber(pair.latest_fee_usd, 4) : '无数据';

                html += `
                    <tr>
                        <td>
                            <div class="fw-bold">${pair.name}</div>
                            ${pair.description ? `<small class="text-muted">${pair.description}</small>` : ''}
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-primary me-1" style="font-size: 0.7rem;">${pair.origin_chain}</span>
                                <small class="text-muted me-1">${pair.origin_token}</small>
                                <i class="bi bi-arrow-right mx-1"></i>
                                <span class="badge bg-success me-1" style="font-size: 0.7rem;">${pair.destination_chain}</span>
                                <small class="text-muted">${pair.destination_token}</small>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class="fw-bold text-primary">${latestPrice}</div>
                        </td>
                        <td class="text-center">
                            <div class="fw-bold text-warning">${feeUsd}</div>
                        </td>
                        <td>
                            <div style="font-size: 0.75rem;">
                                <div>平均: <span class="fw-bold">N/A</span></div>
                                <div>波动: <span class="fw-bold">N/A</span></div>
                                <div class="text-muted">样本: 0</div>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="/pair/${pair.id}" class="btn btn-outline-primary btn-sm" title="查看详情">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <button class="btn btn-outline-secondary btn-sm"
                                        onclick="checkPairPrice('${pair.id}')" title="立即检查">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });
            tbody.innerHTML = html;
        }

        // 更新最近警报
        function updateRecentAlerts(alerts) {
            const alertContainer = document.querySelector('.list-group-flush');
            if (!alertContainer) return;

            let html = '';
            alerts.slice(0, 5).forEach(alert => {
                const alertClass = alert.alert_type === 'price_change' ? 'alert-price-change' : 'alert-system';
                const time = new Date(alert.created_at).toLocaleString('zh-CN');

                html += `
                    <div class="list-group-item alert-item ${alertClass}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    ${alert.alert_type === 'price_change' ? '价格变化警报' : '系统警报'}
                                </h6>
                                <p class="mb-1">${alert.message}</p>
                                <small class="text-muted">${time}</small>
                            </div>
                        </div>
                    </div>
                `;
            });
            alertContainer.innerHTML = html;
        }

        // 更新状态卡片
        function updateStatusCards(status) {
            // 更新运行状态
            const statusCard = document.querySelector('.metric-value.text-success, .metric-value.text-danger');
            if (statusCard) {
                statusCard.className = `metric-value ${status.running ? 'text-success' : 'text-danger'}`;
                statusCard.innerHTML = status.running ? '<i class="bi bi-play-circle"></i>' : '<i class="bi bi-stop-circle"></i>';

                const statusLabel = statusCard.nextElementSibling;
                if (statusLabel) {
                    statusLabel.textContent = status.running ? '运行中' : '已停止';
                }
            }
        }

        // Initialize auto-refresh
        document.addEventListener('DOMContentLoaded', setupAutoRefresh);
        
        // Utility functions for API calls
        function formatNumber(value, decimals = 4) {
            if (value === null || value === undefined) return 'N/A';
            return parseFloat(value).toFixed(decimals);
        }
        
        function formatPercentage(value, decimals = 2) {
            if (value === null || value === undefined) return 'N/A';
            const sign = value >= 0 ? '+' : '';
            return sign + parseFloat(value).toFixed(decimals) + '%';
        }
        
        function formatCurrency(value, decimals = 4) {
            if (value === null || value === undefined) return 'N/A';
            return '$' + parseFloat(value).toFixed(decimals);
        }
        
        function formatDateTime(dateString) {
            if (!dateString) return '从未';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }
        
        // Show flash message
        function showFlashMessage(message, type = 'info') {
            const flashContainer = document.getElementById('flash-messages');
            if (!flashContainer) return;

            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            flashContainer.appendChild(alertDiv);

            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // Manual price check function
        function checkPairPrice(pairName) {
            const button = document.getElementById(`check-${pairName}`);
            if (button) {
                button.disabled = true;
                button.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 检查中...';

                fetch(`/api/monitor/check/${pairName}`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showFlashMessage(`✅ Price check completed for ${pairName}`, 'success');
                        setTimeout(() => window.location.reload(), 2000);
                    } else {
                        showFlashMessage(`❌ Price check failed: ${data.error}`, 'danger');
                    }
                })
                .catch(error => {
                    showFlashMessage(`❌ Error: ${error.message}`, 'danger');
                })
                .finally(() => {
                    button.disabled = false;
                    button.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 立即检查';
                });
            }
        }
    </script>
    
    <style>
        .spin {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>

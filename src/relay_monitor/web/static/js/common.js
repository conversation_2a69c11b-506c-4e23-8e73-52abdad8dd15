/**
 * Relay Monitor - 通用JavaScript工具函数
 * 提供跨页面使用的通用功能
 */

/**
 * 显示通用警报消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 (success, danger, warning, info)
 * @param {number} duration - 显示时长(毫秒)，默认5000
 * @param {string} containerId - 指定容器ID，可选
 */
function showAlert(message, type = 'info', duration = 5000, containerId = null) {
    // 移除现有的提示
    const existingAlerts = document.querySelectorAll('.alert-message');
    existingAlerts.forEach(alert => alert.remove());

    // 创建新的提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-message mb-3`;

    // 图标映射
    const iconMap = {
        'danger': 'bi-exclamation-triangle-fill',
        'warning': 'bi-exclamation-triangle',
        'success': 'bi-check-circle-fill',
        'info': 'bi-info-circle-fill'
    };

    alertDiv.innerHTML = `
        <i class="bi ${iconMap[type] || iconMap.info} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 查找容器
    let container = null;
    
    if (containerId) {
        container = document.getElementById(containerId);
    }
    
    if (!container) {
        // 尝试多个可能的容器位置
        const selectors = [
            '.modal-body',
            '.card-body',
            '.container-fluid',
            '.container',
            'main',
            'body'
        ];
        
        for (const selector of selectors) {
            container = document.querySelector(selector);
            if (container) break;
        }
    }

    // 插入警报
    if (container && container.firstChild) {
        container.insertBefore(alertDiv, container.firstChild);
    } else if (container) {
        container.appendChild(alertDiv);
    } else {
        // 如果都找不到，创建一个固定位置的提示
        alertDiv.className = `alert alert-${type} alert-dismissible fade show alert-message position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        document.body.appendChild(alertDiv);
    }

    // 自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, duration);

    return alertDiv;
}

/**
 * 显示成功消息
 * @param {string} message - 消息内容
 * @param {number} duration - 显示时长
 */
function showSuccess(message, duration = 3000) {
    return showAlert(message, 'success', duration);
}

/**
 * 显示错误消息
 * @param {string} message - 消息内容
 * @param {number} duration - 显示时长
 */
function showError(message, duration = 5000) {
    return showAlert(message, 'danger', duration);
}

/**
 * 显示警告消息
 * @param {string} message - 消息内容
 * @param {number} duration - 显示时长
 */
function showWarning(message, duration = 4000) {
    return showAlert(message, 'warning', duration);
}

/**
 * 显示信息消息
 * @param {string} message - 消息内容
 * @param {number} duration - 显示时长
 */
function showInfo(message, duration = 4000) {
    return showAlert(message, 'info', duration);
}

/**
 * 显示加载状态
 * @param {string} message - 加载消息
 * @returns {HTMLElement} 加载元素
 */
function showLoading(message = '加载中...') {
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'alert alert-info alert-loading d-flex align-items-center';
    loadingDiv.innerHTML = `
        <div class="spinner-border spinner-border-sm me-2" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        ${message}
    `;

    // 查找容器
    let container = document.querySelector('.card-body') || 
                   document.querySelector('.container-fluid') || 
                   document.body;

    if (container.firstChild) {
        container.insertBefore(loadingDiv, container.firstChild);
    } else {
        container.appendChild(loadingDiv);
    }

    return loadingDiv;
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    const loadingElements = document.querySelectorAll('.alert-loading');
    loadingElements.forEach(element => element.remove());
}

/**
 * 确认对话框
 * @param {string} message - 确认消息
 * @param {string} title - 对话框标题
 * @returns {Promise<boolean>} 用户选择结果
 */
function confirmDialog(message, title = '确认操作') {
    return new Promise((resolve) => {
        // 创建模态框
        const modalId = 'confirmModal_' + Date.now();
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="confirmBtn">确认</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById(modalId));
        
        // 绑定事件
        document.getElementById('confirmBtn').addEventListener('click', () => {
            modal.hide();
            resolve(true);
        });

        modal._element.addEventListener('hidden.bs.modal', () => {
            document.getElementById(modalId).remove();
            resolve(false);
        });

        modal.show();
    });
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化时间
 * @param {string|Date} dateTime - 时间
 * @returns {string} 格式化后的时间
 */
function formatDateTime(dateTime) {
    const date = new Date(dateTime);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @returns {Promise<boolean>} 复制是否成功
 */
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showSuccess('已复制到剪贴板');
        return true;
    } catch (err) {
        console.error('复制失败:', err);
        showError('复制失败，请手动复制');
        return false;
    }
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间
 * @returns {Function} 节流后的函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 自动初始化所有工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 自动初始化所有弹出框
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
});

"""
身份验证模块，用于管理页面的访问控制。
"""

import logging
import re
import threading
from datetime import datetime, timedelta, timezone
from functools import wraps
from flask import session, request, redirect, url_for, current_app, flash
from typing import Optional, Dict, Tuple

logger = logging.getLogger(__name__)


def validate_password_strength(password: str) -> Tuple[bool, str]:
    """
    验证密码强度。

    Args:
        password: 要验证的密码

    Returns:
        (is_valid, error_message) tuple
    """
    if len(password) < 8:
        return False, "密码长度至少需要8个字符"

    if len(password) > 128:
        return False, "密码长度不能超过128个字符"

    # 检查是否包含至少一个大写字母
    if not re.search(r'[A-Z]', password):
        return False, "密码必须包含至少一个大写字母"

    # 检查是否包含至少一个小写字母
    if not re.search(r'[a-z]', password):
        return False, "密码必须包含至少一个小写字母"

    # 检查是否包含至少一个数字
    if not re.search(r'[0-9]', password):
        return False, "密码必须包含至少一个数字"

    # 检查是否包含至少一个特殊字符
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        return False, "密码必须包含至少一个特殊字符 (!@#$%^&*(),.?\":{}|<>)"

    # 检查是否包含常见弱密码模式（仅检查明显的弱模式）
    weak_patterns = [
        '123456',
        'qwerty',
        'abc123',
        '111111',
        '000000'
    ]

    password_lower = password.lower()
    for pattern in weak_patterns:
        if pattern in password_lower:
            return False, f"密码不能包含常见弱密码模式: {pattern}"

    return True, "密码强度符合要求"


class ThreadSafeLoginTracker:
    """线程安全的登录尝试跟踪器。"""

    def __init__(self):
        self._login_attempts: Dict[str, int] = {}
        self._lockouts: Dict[str, datetime] = {}
        self._lock = threading.RLock()  # 使用可重入锁

    def record_attempt(self, ip: str, success: bool, max_attempts: int) -> bool:
        """
        记录登录尝试。

        Args:
            ip: 客户端IP地址
            success: 登录是否成功
            max_attempts: 最大尝试次数

        Returns:
            True if login should be allowed, False if locked out
        """
        with self._lock:
            if success:
                # 登录成功，清除记录
                self._login_attempts.pop(ip, None)
                self._lockouts.pop(ip, None)
                return True

            # 登录失败，增加计数
            self._login_attempts[ip] = self._login_attempts.get(ip, 0) + 1

            if self._login_attempts[ip] >= max_attempts:
                # 达到最大尝试次数，锁定IP
                self._lockouts[ip] = datetime.now(timezone.utc)
                logger.warning(f"IP {ip} locked out after {max_attempts} failed login attempts")
                return False

            return True

    def is_locked_out(self, ip: str, lockout_duration_minutes: int) -> bool:
        """
        检查IP是否被锁定。

        Args:
            ip: 客户端IP地址
            lockout_duration_minutes: 锁定持续时间（分钟）

        Returns:
            True if IP is locked out
        """
        with self._lock:
            if ip not in self._lockouts:
                return False

            lockout_time = self._lockouts[ip]
            if datetime.now(timezone.utc) - lockout_time > timedelta(minutes=lockout_duration_minutes):
                # 锁定期已过，清除记录
                self._lockouts.pop(ip, None)
                self._login_attempts.pop(ip, None)
                return False

            return True

    def get_remaining_attempts(self, ip: str, max_attempts: int) -> int:
        """
        获取IP剩余的登录尝试次数。

        Args:
            ip: 客户端IP地址
            max_attempts: 最大尝试次数

        Returns:
            剩余尝试次数
        """
        with self._lock:
            current_attempts = self._login_attempts.get(ip, 0)
            return max(0, max_attempts - current_attempts)

    def get_stats(self) -> Dict[str, int]:
        """获取统计信息。"""
        with self._lock:
            return {
                'active_attempts': len(self._login_attempts),
                'locked_ips': len(self._lockouts)
            }


# 全局线程安全的登录跟踪器实例
_login_tracker = ThreadSafeLoginTracker()


def verify_password(password: str) -> bool:
    """
    验证管理密码。

    Args:
        password: 用户输入的密码

    Returns:
        True if password is correct
    """
    try:
        # Only use runtime configuration with encrypted passwords
        config_manager = getattr(current_app, 'config_manager', None)
        if config_manager and config_manager.runtime_config:
            return config_manager.runtime_config.verify_admin_password(password)

        # No fallback to plaintext passwords for security
        logger.warning("Runtime configuration not available - password verification failed")
        return False
    except Exception as e:
        logger.error(f"Error verifying password: {e}")
        return False


def is_first_setup() -> bool:
    """
    检查是否是首次设置。

    Returns:
        True if this is the first setup
    """
    try:
        config_manager = getattr(current_app, 'config_manager', None)
        if config_manager:
            return config_manager.is_first_setup()

        # No fallback to file configuration for security
        logger.warning("Runtime configuration not available - assuming first setup")
        return True  # Assume first setup when runtime config is not available
    except Exception as e:
        logger.error(f"Error checking first setup: {e}")
        return True  # Assume first setup on error for safety


def is_logged_in() -> bool:
    """
    检查用户是否已登录。
    
    Returns:
        True if user is logged in and session is valid
    """
    if not session.get('admin_logged_in'):
        return False
    
    # 检查会话是否过期
    login_time = session.get('admin_login_time')
    if not login_time:
        return False
    
    try:
        admin_config = current_app.relay_config.admin
        timeout_minutes = admin_config.session_timeout_minutes
        
        login_datetime = datetime.fromisoformat(login_time)
        if datetime.now(timezone.utc) - login_datetime > timedelta(minutes=timeout_minutes):
            # 会话过期
            clear_session()
            return False
        
        return True
    except Exception as e:
        logger.error(f"Error checking login status: {e}")
        return False


def clear_session():
    """清除管理员会话。"""
    session.pop('admin_logged_in', None)
    session.pop('admin_login_time', None)
    session.pop('admin_user_ip', None)


def get_client_ip() -> str:
    """获取客户端IP地址。"""
    if request.environ.get('HTTP_X_FORWARDED_FOR'):
        return request.environ['HTTP_X_FORWARDED_FOR'].split(',')[0].strip()
    elif request.environ.get('HTTP_X_REAL_IP'):
        return request.environ['HTTP_X_REAL_IP']
    else:
        return request.environ.get('REMOTE_ADDR', 'unknown')


def is_locked_out(ip: str) -> bool:
    """
    检查IP是否被锁定。

    Args:
        ip: 客户端IP地址

    Returns:
        True if IP is locked out
    """
    try:
        admin_config = current_app.relay_config.admin
        lockout_duration = admin_config.lockout_duration_minutes

        return _login_tracker.is_locked_out(ip, lockout_duration)
    except Exception as e:
        logger.error(f"Error checking lockout status: {e}")
        return False


def record_login_attempt(ip: str, success: bool) -> bool:
    """
    记录登录尝试。

    Args:
        ip: 客户端IP地址
        success: 登录是否成功

    Returns:
        True if login should be allowed, False if locked out
    """
    try:
        admin_config = current_app.relay_config.admin
        max_attempts = admin_config.max_login_attempts

        return _login_tracker.record_attempt(ip, success, max_attempts)
    except Exception as e:
        logger.error(f"Error recording login attempt: {e}")
        return True


def login_required(f):
    """
    装饰器：要求用户登录才能访问。
    
    Args:
        f: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not is_logged_in():
            flash('请先登录管理后台', 'warning')
            return redirect(url_for('admin.login'))
        return f(*args, **kwargs)
    return decorated_function


def perform_login(password: str) -> tuple[bool, str]:
    """
    执行登录操作。
    
    Args:
        password: 用户输入的密码
        
    Returns:
        (success, message) tuple
    """
    client_ip = get_client_ip()
    
    # 检查是否被锁定
    if is_locked_out(client_ip):
        try:
            admin_config = current_app.relay_config.admin
            lockout_duration = admin_config.lockout_duration_minutes
            message = f"IP地址已被锁定，请等待 {lockout_duration} 分钟后重试"
            return False, message
        except Exception as e:
            logger.warning(f"Failed to get lockout duration from config: {e}")
            return False, "IP地址已被锁定，请稍后重试"
    
    # 验证密码
    success = verify_password(password)
    
    # 记录登录尝试
    if not record_login_attempt(client_ip, success):
        try:
            admin_config = current_app.relay_config.admin
            lockout_duration = admin_config.lockout_duration_minutes
            message = f"登录失败次数过多，IP已被锁定 {lockout_duration} 分钟"
            return False, message
        except Exception as e:
            logger.warning(f"Failed to get lockout duration from config: {e}")
            return False, "登录失败次数过多，IP已被锁定"
    
    if success:
        # 设置会话
        session['admin_logged_in'] = True
        session['admin_login_time'] = datetime.now(timezone.utc).isoformat()
        session['admin_user_ip'] = client_ip
        session.permanent = True
        
        logger.info(f"Admin login successful from IP {client_ip}")
        return True, "登录成功"
    else:
        try:
            admin_config = current_app.relay_config.admin
            remaining_attempts = _login_tracker.get_remaining_attempts(client_ip, admin_config.max_login_attempts)
            message = f"密码错误，还有 {remaining_attempts} 次尝试机会"
            return False, message
        except Exception as e:
            logger.warning(f"Failed to get remaining attempts from config: {e}")
            return False, "密码错误"


def perform_logout():
    """执行登出操作。"""
    client_ip = get_client_ip()
    clear_session()
    logger.info(f"Admin logout from IP {client_ip}")


def get_session_info() -> Optional[dict]:
    """
    获取当前会话信息。

    Returns:
        会话信息字典，如果未登录则返回None
    """
    if not is_logged_in():
        return None

    try:
        login_time = session.get('admin_login_time')
        user_ip = session.get('admin_user_ip')
        admin_config = current_app.relay_config.admin

        login_datetime = datetime.fromisoformat(login_time)
        timeout_minutes = admin_config.session_timeout_minutes
        expires_at = login_datetime + timedelta(minutes=timeout_minutes)

        # 计算剩余时间（秒）
        now = datetime.now(timezone.utc)
        remaining_seconds = int((expires_at - now).total_seconds())

        return {
            'login_time': login_datetime,
            'user_ip': user_ip,
            'expires_at': expires_at,
            'timeout_minutes': timeout_minutes,
            'remaining_seconds': max(0, remaining_seconds)  # 确保不为负数
        }
    except Exception as e:
        logger.error(f"Error getting session info: {e}")
        return None

"""
Relay Monitor Web 界面的 Flask 应用工厂。
"""

import logging
from datetime import timed<PERSON>ta
from flask import Flask
from typing import Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from ..api.client import RelayAPIClient
    from ..alerts.system import AlertSystem
    from ..config.models import Config
    from ..trading.executor import TradingExecutor

from ..config.manager import ConfigManager
from ..config.runtime import RuntimeConfigManager
from ..storage.database import DataStorage
from ..monitor.engine import PriceMonitor
from .routes import main_bp, api_bp, admin_bp


logger = logging.getLogger(__name__)


class RelayMonitorApp(Flask):
    """扩展的Flask应用类，包含Relay Monitor特定的属性。"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 定义自定义属性以避免Pylance警告
        self.config_manager: Optional[ConfigManager] = None
        self.storage: Optional[DataStorage] = None
        self.monitor: Optional[PriceMonitor] = None
        self.relay_config: Optional['Config'] = None
        self.alert_system: Optional['AlertSystem'] = None
        self.api_client: Optional['RelayAPIClient'] = None
        self.trading_executor: Optional['TradingExecutor'] = None


def create_app(
    config_manager: Optional[ConfigManager] = None,
    storage: Optional[DataStorage] = None,
    monitor: Optional[PriceMonitor] = None,
    alert_system = None,
    trading_executor = None
) -> RelayMonitorApp:
    """
    创建并配置 Flask 应用。

    Args:
        config_manager: 配置管理器实例
        storage: 数据存储实例
        monitor: 价格监控实例
        alert_system: 警报系统实例

    Returns:
        配置好的 Flask 应用
    """
    app = RelayMonitorApp(__name__)
    
    # 首先初始化存储（运行时配置需要）
    if storage is None:
        # 创建临时配置管理器以获取数据库路径
        temp_config_manager = ConfigManager() if config_manager is None else config_manager
        temp_config = temp_config_manager.get_config()
        storage = DataStorage(temp_config.database.path)

    # 加载支持存储的配置
    if config_manager is None:
        config_manager = ConfigManager(storage=storage)
    elif config_manager.storage is None:
        # 使用存储更新现有配置管理器
        config_manager.storage = storage
        config_manager.runtime_config = config_manager.runtime_config or RuntimeConfigManager(storage)

    config = config_manager.get_config()

    # 配置 Flask
    import os
    import secrets

    # Generate or use persistent SECRET_KEY
    secret_key = os.environ.get('FLASK_SECRET_KEY')
    if not secret_key:
        # Try to read from file for persistence across container restarts
        secret_key_file = '/app/data/.secret_key'
        try:
            if os.path.exists(secret_key_file):
                with open(secret_key_file, 'r') as f:
                    secret_key = f.read().strip()
            else:
                # Generate new secret key and save it
                secret_key = secrets.token_hex(32)
                os.makedirs(os.path.dirname(secret_key_file), exist_ok=True)
                with open(secret_key_file, 'w') as f:
                    f.write(secret_key)
        except Exception:
            # Fallback to generated key (will not persist)
            secret_key = secrets.token_hex(32)

    app.config.update({
        'SECRET_KEY': secret_key,
        'DEBUG': config.web.debug,
        'TESTING': False,
        'JSON_SORT_KEYS': False,
        'JSONIFY_PRETTYPRINT_REGULAR': True,
        'PERMANENT_SESSION_LIFETIME': timedelta(minutes=config.admin.session_timeout_minutes)
    })
    
    # Store instances in app context for access in routes
    app.config_manager = config_manager
    app.storage = storage
    app.monitor = monitor
    app.relay_config = config
    app.alert_system = alert_system
    app.trading_executor = trading_executor

    # Add API client access
    if monitor and hasattr(monitor, 'api_client'):
        app.api_client = monitor.api_client
    else:
        # Create standalone API client if monitor is not available
        from ..api.client import RelayAPIClient
        app.api_client = RelayAPIClient(
            base_url=config.api.base_url,
            timeout=config.api.timeout,
            retry_attempts=config.api.retry_attempts,
            cache_ttl=config.api.cache_ttl
        )
    
    # Register blueprints
    app.register_blueprint(main_bp)
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(admin_bp, url_prefix='/admin')
    
    # Configure logging - use setup_logging for consistency
    from ..utils.logging import setup_logging
    setup_logging(config.logging, debug_mode=app.debug)
    
    # Add template filters
    @app.template_filter('datetime')
    def datetime_filter(dt):  # noqa: F841
        """为模板格式化日期时间并进行时区转换。"""
        if dt is None:
            return '从未'

        # 将UTC转换为本地时区（Asia/Shanghai）
        from datetime import timezone, timedelta

        # 假设存储的日期时间是UTC（如果没有时区信息）
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)

        # 转换为Asia/Shanghai时区（UTC+8）
        local_tz = timezone(timedelta(hours=8))
        local_dt = dt.astimezone(local_tz)

        return local_dt.strftime('%Y-%m-%d %H:%M:%S')
    
    @app.template_filter('number')
    def number_filter(value, decimals=4):  # noqa: F841
        """为模板格式化数字。"""
        if value is None:
            return '不可用'
        return f"{value:.{decimals}f}"
    
    @app.template_filter('percentage')
    def percentage_filter(value, decimals=2):  # noqa: F841
        """为模板格式化百分比。"""
        if value is None:
            return '不可用'
        return f"{value:+.{decimals}f}%"

    @app.template_filter('currency')
    def currency_filter(value, decimals=4):  # noqa: F841
        """为模板格式化货币。"""
        if value is None:
            return '不可用'
        return f"${value:.{decimals}f}"
    
    # 错误处理器
    @app.errorhandler(404)
    def not_found_error(error):  # noqa: F841
        """处理404错误。"""
        _ = error  # 忽略未使用的参数
        return {'error': '页面未找到'}, 404

    @app.errorhandler(500)
    def internal_error(error):  # noqa: F841
        """处理500错误。"""
        logger.error(f"内部服务器错误: {error}")
        return {'error': '内部服务器错误'}, 500
    
    logger.info("Flask application created successfully")
    return app


def run_web_server(
    host: str = '127.0.0.1',
    port: int = 5000,
    debug: bool = False,
    config_manager: Optional[ConfigManager] = None,
    storage: Optional[DataStorage] = None,
    monitor: Optional[PriceMonitor] = None,
    alert_system = None,
    trading_executor = None
):
    """
    运行Web服务器。

    Args:
        host: 绑定的主机地址
        port: 绑定的端口
        debug: 启用调试模式
        config_manager: 配置管理器实例
        storage: 数据存储实例
        monitor: 价格监控实例
        alert_system: 警报系统实例
        trading_executor: 交易执行器实例
    """
    import os

    # In debug mode, Flask starts a reloader process
    # We only want to start the monitor in the reloader process, not the main process
    if debug and os.environ.get('WERKZEUG_RUN_MAIN') == 'true':
        # This is the reloader process - start the monitor if it exists and isn't running
        if monitor and not monitor.running:
            logger.info("Debug mode: Starting monitor in reloader process")
            monitor.start()
        elif monitor:
            logger.info("Debug mode: Monitor already running in reloader process")
    elif debug:
        # This is the main process before reloader starts
        # Don't start monitor here, it will be started in the reloader process
        monitor = None
        logger.info("Debug mode: Skipping monitor in main process")

    app = create_app(config_manager, storage, monitor, alert_system, trading_executor)

    logger.info(f"Starting web server on {host}:{port}")
    app.run(host=host, port=port, debug=debug)

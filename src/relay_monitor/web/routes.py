"""
Relay Monitor Web 界面的 Flask 路由。
"""

import logging
from datetime import datetime, timezone
from flask import Blueprint, render_template, jsonify, request, current_app, redirect, url_for, flash
from typing import cast, TYPE_CHECKING

if TYPE_CHECKING:
    from .app import RelayMonitorApp

from ..config.models import MonitorPair
from ..crypto.wallet import WalletManager
from ..crypto.exceptions import WalletError, WalletNotFoundError, InvalidPasswordError
from .auth import login_required, perform_login, perform_logout, is_logged_in, get_session_info, is_first_setup


logger = logging.getLogger(__name__)


def get_app() -> 'RelayMonitorApp':
    """获取类型安全的应用实例。"""
    return cast('RelayMonitorApp', current_app)


def get_storage():
    """获取存储实例，确保不为None。"""
    app = get_app()
    if app.storage is None:
        raise RuntimeError("Storage not initialized")
    return app.storage


def get_config():
    """获取配置实例，确保不为None。"""
    app = get_app()
    if app.relay_config is None:
        raise RuntimeError("Config not initialized")
    return app.relay_config


def get_monitor():
    """获取监控实例，确保不为None。"""
    app = get_app()
    if app.monitor is None:
        raise RuntimeError("Monitor not initialized")
    return app.monitor


def get_config_manager():
    """获取配置管理器实例，确保不为None。"""
    app = get_app()
    if app.config_manager is None:
        raise RuntimeError("Config manager not initialized")
    return app.config_manager


def get_wallet_manager():
    """获取钱包管理器实例。"""
    storage = get_storage()
    return WalletManager(storage)


def get_api_client():
    """获取API客户端实例，确保不为None。"""
    app = get_app()
    if app.api_client is None:
        raise RuntimeError("API client not initialized")
    return app.api_client


def get_trading_executor():
    """获取交易执行器实例。"""
    app = get_app()
    if hasattr(app, 'trading_executor') and app.trading_executor:
        return app.trading_executor

    # 如果没有初始化，创建一个新的交易执行器
    from ..trading.executor import TradingExecutor
    storage = get_storage()
    wallet_manager = get_wallet_manager()
    api_client = get_api_client()

    # 不再需要主密码

    trading_executor = TradingExecutor(
        wallet_manager=wallet_manager,
        api_client=api_client,
        storage=storage
    )

    # 缓存到应用实例
    app.trading_executor = trading_executor
    return trading_executor


# 辅助函数用于类型安全的应用访问


# Create blueprints
main_bp = Blueprint('main', __name__)
api_bp = Blueprint('api', __name__)
admin_bp = Blueprint('admin', __name__)


@main_bp.route('/')
def index():
    """Main dashboard page."""
    try:
        storage = get_storage()
        # config = get_config()  # 未使用
        monitor = get_monitor()
        
        # Get enabled pairs from database
        enabled_pairs = storage.get_enabled_monitor_pairs()
        
        # Get latest prices for each pair
        pair_data = []
        for pair in enabled_pairs:
            latest_price = storage.get_latest_price(pair.name)
            price_stats = storage.get_price_statistics(pair.name, hours=24)
            
            pair_info = {
                'name': pair.name,
                'description': pair.description,
                'origin_chain': pair.origin_chain,
                'destination_chain': pair.destination_chain,
                'origin_token': pair.origin_token,
                'destination_token': pair.destination_token,
                'latest_price': latest_price.exchange_rate if latest_price else None,
                'latest_fee': latest_price.total_fee_usd if latest_price else None,
                'latest_timestamp': latest_price.timestamp if latest_price else None,
                'stats_24h': price_stats
            }
            pair_data.append(pair_info)
        
        # Get system status
        monitor_status = monitor.get_status() if monitor else {
            'running': False,
            'stats': {'total_checks': 0, 'alerts_triggered': 0}
        }

        # If monitor exists but not running, try to get some basic status
        if monitor and not monitor_status['running']:
            monitor_status['stats'] = {
                'total_checks': 0,
                'alerts_triggered': 0,
                'errors_encountered': 0,
                'pairs_monitored': len(enabled_pairs)
            }
        
        # Get recent alerts
        recent_alerts = storage.get_alert_history(hours=24, limit=10)
        
        return render_template('dashboard.html',
                             pairs=pair_data,
                             monitor_status=monitor_status,
                             recent_alerts=recent_alerts,
                             page_title='Dashboard')
        
    except Exception as e:
        logger.error(f"Error loading dashboard: {e}")
        return render_template('error.html', error=str(e)), 500


@main_bp.route('/pair/<pair_name>')
def pair_detail(pair_name):
    """Detailed view for a specific pair."""
    try:
        storage = get_storage()
        # config = get_config()  # 未使用
        
        # Find the pair configuration from database
        enabled_pairs = storage.get_enabled_monitor_pairs()
        pair_config = next((p for p in enabled_pairs if p.name == pair_name), None)
        
        if not pair_config:
            return render_template('error.html', error=f"Pair '{pair_name}' not found"), 404
        
        # Get price history
        price_history = storage.get_price_history(pair_name, hours=168, limit=1000)  # 7 days
        
        # Get price statistics
        stats_24h = storage.get_price_statistics(pair_name, hours=24)
        stats_7d = storage.get_price_statistics(pair_name, hours=168)
        
        # Get alerts for this pair
        pair_alerts = storage.get_alert_history(pair_name, hours=168, limit=50)
        
        return render_template('pair_detail.html',
                             pair=pair_config,
                             price_history=price_history,
                             stats_24h=stats_24h,
                             stats_7d=stats_7d,
                             alerts=pair_alerts,
                             page_title=f'{pair_name} Details')
        
    except Exception as e:
        logger.error(f"Error loading pair detail: {e}")
        return render_template('error.html', error=str(e)), 500


@main_bp.route('/alerts')
def alerts_page():
    """Alerts history page."""
    try:
        storage = get_storage()

        # Get pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = 50

        # Get alerts
        try:
            all_alerts = storage.get_alert_history(hours=168, limit=per_page * page)
        except Exception as e:
            logger.warning(f"Error getting alert history: {e}")
            all_alerts = []

        # Simple pagination
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        alerts_page = all_alerts[start_idx:end_idx] if all_alerts else []

        has_next = len(all_alerts) > end_idx
        has_prev = page > 1

        return render_template('alerts.html',
                             alerts=alerts_page,
                             page=page,
                             has_next=has_next,
                             has_prev=has_prev,
                             page_title='Alert History')

    except Exception as e:
        logger.error(f"Error loading alerts: {e}")
        return render_template('error.html', error=str(e)), 500


@main_bp.route('/status')
def status():
    """System status page."""
    try:
        storage = get_storage()
        monitor = get_monitor()
        config = get_config()

        # Get monitor status
        monitor_status = monitor.get_status() if monitor else {
            'running': False,
            'stats': {'total_checks': 0, 'alerts_triggered': 0, 'errors_encountered': 0}
        }

        # Get database stats
        db_stats = storage.get_database_stats()

        # Get pairs summary
        pairs_summary = storage.get_monitor_pairs_summary()

        return render_template('status.html',
                             monitor_status=monitor_status,
                             db_stats=db_stats,
                             pairs_summary=pairs_summary,
                             config=config,
                             page_title='System Status')

    except Exception as e:
        logger.error(f"Error loading status: {e}")
        return render_template('error.html', error=str(e)), 500


# API Routes
@api_bp.route('/pairs')
def api_pairs():
    """API endpoint for pairs data."""
    try:
        storage = get_storage()
        # config = get_config()  # 未使用
        
        enabled_pairs = storage.get_enabled_monitor_pairs()
        pairs_data = []
        
        for pair in enabled_pairs:
            latest_price = storage.get_latest_price(pair.name)
            stats = storage.get_price_statistics(pair.name, hours=24)
            
            pair_data = {
                'name': pair.name,
                'description': pair.description,
                'origin_chain': pair.origin_chain,
                'destination_chain': pair.destination_chain,
                'origin_token': pair.origin_token,
                'destination_token': pair.destination_token,
                'latest_price': latest_price.exchange_rate if latest_price else None,
                'latest_fee': latest_price.total_fee_usd if latest_price else None,
                'latest_timestamp': latest_price.timestamp.isoformat() if latest_price else None,
                'stats_24h': stats
            }
            pairs_data.append(pair_data)
        
        return jsonify({'pairs': pairs_data})
        
    except Exception as e:
        logger.error(f"API error: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/pair/<pair_name>/history')
def api_pair_history(pair_name):
    """API endpoint for pair price history."""
    try:
        storage = get_storage()
        
        # Get query parameters
        hours = request.args.get('hours', 24, type=int)
        limit = request.args.get('limit', 100, type=int)
        
        # Limit the maximum values for performance
        hours = min(hours, 168)  # Max 7 days
        limit = min(limit, 1000)  # Max 1000 records
        
        history = storage.get_price_history(pair_name, hours=hours, limit=limit)
        
        history_data = []
        for record in history:
            history_data.append({
                'timestamp': record.timestamp.isoformat(),
                'exchange_rate': record.exchange_rate,
                'total_fee_usd': record.total_fee_usd,
                'time_estimate_seconds': record.time_estimate_seconds
            })
        
        return jsonify({
            'pair_name': pair_name,
            'history': history_data,
            'count': len(history_data)
        })
        
    except Exception as e:
        logger.error(f"API error: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/monitor/status')
def api_monitor_status():
    """API endpoint for monitor status."""
    try:
        monitor = get_monitor()

        if monitor:
            status = monitor.get_status()

            # Clean up task_status to remove non-serializable objects
            if 'task_status' in status:
                clean_task_status = {}
                for task_name, task_info in status['task_status'].items():
                    clean_task_info = {}
                    for key, value in task_info.items():
                        if key == 'function':
                            clean_task_info[key] = str(value)  # Convert function to string
                        elif hasattr(value, 'isoformat'):  # datetime objects
                            clean_task_info[key] = value.isoformat()
                        else:
                            clean_task_info[key] = value
                    clean_task_status[task_name] = clean_task_info
                status['task_status'] = clean_task_status

            # Convert datetime objects to strings for JSON serialization
            if 'stats' in status:
                for key, value in status['stats'].items():
                    if hasattr(value, 'isoformat'):
                        status['stats'][key] = value.isoformat()

            if 'scheduler_stats' in status:
                for key, value in status['scheduler_stats'].items():
                    if hasattr(value, 'isoformat'):
                        status['scheduler_stats'][key] = value.isoformat()

            return jsonify(status)
        else:
            return jsonify({
                'running': False,
                'error': 'Monitor not initialized'
            })

    except Exception as e:
        logger.error(f"API error: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/monitor/check/<pair_name>', methods=['POST'])
def api_check_pair(pair_name):
    """API endpoint to manually trigger price check."""
    try:
        monitor = get_monitor()
        
        if not monitor:
            return jsonify({'error': 'Monitor not initialized'}), 400
        
        result = monitor.check_pair_now(pair_name)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"API error: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/stats')
def api_stats():
    """API endpoint for system statistics."""
    try:
        storage = get_storage()
        monitor = get_monitor()

        # Get database stats
        db_stats = storage.get_database_stats()

        # Get monitor stats
        monitor_stats = monitor.get_status() if monitor else None

        # Get pairs summary
        pairs_summary = storage.get_monitor_pairs_summary()

        return jsonify({
            'database': db_stats,
            'monitor': monitor_stats,
            'pairs_summary': pairs_summary,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"API error: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/dashboard/data')
def api_dashboard_data():
    """API endpoint for dashboard data (for async refresh)."""
    try:
        storage = get_storage()
        monitor = get_monitor()

        # Get enabled pairs with latest data
        enabled_pairs = storage.get_enabled_monitor_pairs()
        pairs_data = []

        for pair in enabled_pairs:
            # Get latest price record
            latest_price = storage.get_latest_price(pair.name)

            pair_data = {
                'id': pair.name,
                'name': pair.name,
                'description': pair.description,
                'origin_chain': pair.origin_chain.title(),
                'destination_chain': pair.destination_chain.title(),
                'origin_token': pair.origin_token,
                'destination_token': pair.destination_token,
                'latest_price': latest_price.exchange_rate if latest_price else None,
                'latest_fee_usd': latest_price.total_fee_usd if latest_price else None,
                'last_update': latest_price.timestamp.isoformat() if latest_price else None
            }
            pairs_data.append(pair_data)

        # Get recent alerts
        recent_alerts = storage.get_alert_history(hours=24, limit=5)
        alerts_data = []

        for alert in recent_alerts:
            alerts_data.append({
                'id': alert.id,
                'alert_type': alert.alert_type,
                'message': alert.message,
                'created_at': alert.timestamp.isoformat()
            })

        # Get monitor status (ensure JSON serializable)
        if monitor:
            raw_status = monitor.get_status()
            monitor_status = {
                'running': raw_status.get('running', False),
                'stats': raw_status.get('stats', {}),
                'uptime': raw_status.get('uptime', 0),
                'last_check': raw_status.get('last_check', None)
            }
        else:
            monitor_status = {'running': False}

        return jsonify({
            'pairs': pairs_data,
            'recent_alerts': alerts_data,
            'monitor_status': monitor_status,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"API error: {e}")
        return jsonify({'error': str(e)}), 500


# =============================================================================
# 管理页面路由
# =============================================================================

@admin_bp.route('/login', methods=['GET', 'POST'])
def login():
    """管理员登录页面。"""
    # 检查是否是首次设置
    if is_first_setup():
        return redirect(url_for('admin.setup'))

    # 如果已经登录，重定向到仪表板
    if is_logged_in():
        return redirect(url_for('admin.dashboard'))

    if request.method == 'POST':
        password = request.form.get('password', '')
        success, message = perform_login(password)

        if success:
            flash(message, 'success')
            return redirect(url_for('admin.dashboard'))
        else:
            flash(message, 'error')

    return render_template('admin/login.html', page_title='管理员登录')


@admin_bp.route('/setup', methods=['GET', 'POST'])
def setup():
    """首次设置页面。"""
    # 如果已经设置过，重定向到登录页面
    if not is_first_setup():
        return redirect(url_for('admin.login'))

    if request.method == 'POST':
        try:
            # 获取表单数据
            admin_password = request.form.get('admin_password', '').strip()
            confirm_password = request.form.get('confirm_password', '').strip()
            bark_keys_str = request.form.get('bark_keys', '').strip()

            # 验证密码
            if not admin_password:
                flash('管理员密码不能为空', 'error')
                return render_template('admin/setup.html', page_title='首次设置')

            if admin_password != confirm_password:
                flash('两次输入的密码不一致', 'error')
                return render_template('admin/setup.html', page_title='首次设置')

            if len(admin_password) < 6:
                flash('密码长度至少6位', 'error')
                return render_template('admin/setup.html', page_title='首次设置')

            # 处理Bark API keys
            bark_keys = []
            if bark_keys_str:
                bark_keys = [key.strip() for key in bark_keys_str.split(',') if key.strip()]

            # 执行初始设置
            config_manager = get_config_manager()
            if config_manager.setup_initial_config(
                admin_password=admin_password,
                bark_keys=bark_keys or []
            ):
                flash('初始设置完成！请使用新密码登录。', 'success')
                return redirect(url_for('admin.login'))
            else:
                flash('设置失败，请重试', 'error')

        except Exception as e:
            logger.error(f"Setup error: {e}")
            flash('设置过程中发生错误，请重试', 'error')

    return render_template('admin/setup.html', page_title='首次设置')


@admin_bp.route('/logout')
def logout():
    """管理员登出。"""
    perform_logout()
    flash('已成功登出', 'info')
    return redirect(url_for('admin.login'))


@admin_bp.route('/dashboard')
@login_required
def dashboard():
    """管理仪表板。"""
    try:
        # config = get_config()  # 未使用
        storage = get_storage()
        monitor = get_monitor()

        # 获取系统统计
        db_stats = storage.get_database_stats()
        monitor_status = monitor.get_status() if monitor else {'running': False}

        # 获取监控对统计
        enabled_pairs = storage.get_enabled_monitor_pairs()
        all_pairs = storage.get_all_monitor_configs()
        total_pairs = len(all_pairs)

        # 获取会话信息
        session_info = get_session_info()

        return render_template('admin/dashboard.html',
                             page_title='管理仪表板',
                             db_stats=db_stats,
                             monitor_status=monitor_status,
                             enabled_pairs_count=len(enabled_pairs),
                             total_pairs_count=total_pairs,
                             session_info=session_info)

    except Exception as e:
        logger.error(f"Error loading admin dashboard: {e}")
        flash(f'加载仪表板时出错: {e}', 'error')
        return redirect(url_for('admin.login'))


@admin_bp.route('/pairs')
@login_required
def pairs():
    """交易对管理页面。"""
    try:
        storage = get_storage()
        # Get pairs from database instead of config
        monitor_configs = storage.get_all_monitor_configs()

        # Convert to MonitorPair objects for template compatibility
        pairs = []
        for config in monitor_configs:
            pair = MonitorPair(
                name=config.name,
                description=config.description,
                origin_chain=config.origin_chain,
                destination_chain=config.destination_chain,
                origin_token=config.origin_token,
                destination_token=config.destination_token,
                amount=config.amount,
                enabled=config.enabled,
                alert_threshold_percent=config.alert_threshold_percent
            )
            pairs.append(pair)

        return render_template('admin/pairs.html',
                             page_title='交易对管理',
                             pairs=pairs)

    except Exception as e:
        logger.error(f"Error loading pairs page: {e}")
        flash(f'加载交易对页面时出错: {e}', 'error')
        return redirect(url_for('admin.dashboard'))


@admin_bp.route('/alerts')
@login_required
def alerts():
    """警报配置页面。"""
    try:
        config = get_config()
        alerts_config = config.alerts

        return render_template('admin/alerts.html',
                             page_title='警报配置',
                             alerts_config=alerts_config)

    except Exception as e:
        logger.error(f"Error loading alerts page: {e}")
        flash(f'加载警报配置页面时出错: {e}', 'error')
        return redirect(url_for('admin.dashboard'))


@admin_bp.route('/settings')
@login_required
def settings():
    """系统设置页面。"""
    try:

        config = get_config()

        return render_template('admin/settings.html',
                             page_title='系统设置',
                             config=config)

    except Exception as e:
        logger.error(f"Error loading settings page: {e}")
        flash(f'加载设置页面时出错: {e}', 'error')
        return redirect(url_for('admin.dashboard'))


@admin_bp.route('/wallets')
@login_required
def wallets():
    """钱包管理页面。"""
    try:
        wallet_manager = get_wallet_manager()
        wallets_list = wallet_manager.list_wallets()

        return render_template('admin/wallets.html',
                             page_title='钱包管理',
                             wallets=wallets_list)

    except Exception as e:
        logger.error(f"Error loading wallets page: {e}")
        flash(f'加载钱包管理页面时出错: {e}', 'error')
        return redirect(url_for('admin.dashboard'))


@admin_bp.route('/transactions')
@login_required
def transactions():
    """交易历史页面。"""
    try:
        storage = get_storage()

        # 获取所有交易对用于过滤
        monitor_configs = storage.get_all_monitor_configs()
        pairs = [config.name for config in monitor_configs]

        return render_template('admin/transactions.html',
                             pairs=pairs,
                             page_title='交易历史')

    except Exception as e:
        logger.error(f"Error loading transactions page: {e}")
        flash(f'加载交易历史页面时出错: {e}', 'error')
        return redirect(url_for('admin.dashboard'))


@admin_bp.route('/trading')
@login_required
def trading():
    """自动交易配置页面。"""
    try:
        storage = get_storage()
        wallet_manager = get_wallet_manager()

        # 获取所有监控对
        pairs = storage.get_all_monitor_configs()

        # 获取所有钱包
        wallets_list = wallet_manager.list_wallets()

        return render_template('admin/trading.html',
                             page_title='自动交易配置',
                             pairs=pairs,
                             wallets=wallets_list)

    except Exception as e:
        logger.error(f"Error loading trading page: {e}")
        flash(f'加载自动交易配置页面时出错: {e}', 'error')
        return redirect(url_for('admin.dashboard'))


# =============================================================================
# 管理API端点
# =============================================================================

@api_bp.route('/admin/pairs', methods=['GET'])
@login_required
def api_admin_get_pairs():
    """获取所有交易对配置。"""
    try:
        storage = get_storage()
        monitor_configs = storage.get_all_monitor_configs()
        pairs_data = []

        for config in monitor_configs:
            pairs_data.append({
                'name': config.name,
                'description': config.description,
                'origin_chain': config.origin_chain,
                'destination_chain': config.destination_chain,
                'origin_token': config.origin_token,
                'destination_token': config.destination_token,
                'amount': config.amount,
                'enabled': config.enabled,
                'alert_threshold_percent': config.alert_threshold_percent
            })

        return jsonify({'pairs': pairs_data})

    except Exception as e:
        logger.error(f"Error getting pairs: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/admin/pairs', methods=['POST'])
@login_required
def api_admin_add_pair():
    """添加新的交易对。"""
    try:
        data = request.get_json()

        # 验证必需字段
        required_fields = ['name', 'origin_chain', 'destination_chain',
                          'origin_token', 'destination_token', 'amount']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必需字段: {field}'}), 400

        # 创建新的监控对
        new_pair = MonitorPair(
            name=data['name'],
            description=data.get('description', ''),
            origin_chain=data['origin_chain'],
            destination_chain=data['destination_chain'],
            origin_token=data['origin_token'],
            destination_token=data['destination_token'],
            amount=data['amount'],
            enabled=data.get('enabled', True),
            alert_threshold_percent=data.get('alert_threshold_percent', 5.0)
        )

        # 检查名称是否已存在
        storage = get_storage()
        existing_config = storage.get_monitor_config(new_pair.name)
        if existing_config:
            return jsonify({'error': f'交易对名称 "{new_pair.name}" 已存在'}), 400

        # 保存到数据库
        from ..storage.models import MonitorConfigRecord
        config_record = MonitorConfigRecord(
            name=new_pair.name,
            description=new_pair.description,
            origin_chain=new_pair.origin_chain,
            destination_chain=new_pair.destination_chain,
            origin_token=new_pair.origin_token,
            destination_token=new_pair.destination_token,
            amount=new_pair.amount,
            enabled=new_pair.enabled,
            alert_threshold_percent=new_pair.alert_threshold_percent
        )

        if storage.store_monitor_config(config_record):
            # 重载监控系统

            monitor = get_monitor()
            if monitor and hasattr(monitor, 'reload_config'):
                monitor.reload_config()

            return jsonify({'success': True, 'message': '交易对添加成功'})
        else:
            return jsonify({'error': '保存配置失败'}), 500

    except Exception as e:
        logger.error(f"Error adding pair: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/admin/pairs/<pair_name>', methods=['PUT'])
@login_required
def api_admin_update_pair(pair_name):
    """更新交易对配置。"""
    try:
        data = request.get_json()
        storage = get_storage()

        # 查找要更新的交易对
        existing_config = storage.get_monitor_config(pair_name)
        if not existing_config:
            return jsonify({'error': f'交易对 "{pair_name}" 不存在'}), 404

        # 更新字段
        if 'description' in data:
            existing_config.description = data['description']
        if 'enabled' in data:
            existing_config.enabled = data['enabled']
        if 'alert_threshold_percent' in data:
            existing_config.alert_threshold_percent = data['alert_threshold_percent']
        if 'amount' in data:
            existing_config.amount = data['amount']

        # 保存到数据库
        if storage.store_monitor_config(existing_config):
            # 重载监控系统

            monitor = get_monitor()
            if monitor and hasattr(monitor, 'reload_config'):
                monitor.reload_config()

            return jsonify({'success': True, 'message': '交易对更新成功'})
        else:
            return jsonify({'error': '保存配置失败'}), 500

    except Exception as e:
        logger.error(f"Error updating pair: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/admin/pairs/<pair_name>', methods=['DELETE'])
@login_required
def api_admin_delete_pair(pair_name):
    """删除交易对。"""
    try:
        storage = get_storage()

        # 检查交易对是否存在
        existing_config = storage.get_monitor_config(pair_name)
        if not existing_config:
            return jsonify({'error': f'交易对 "{pair_name}" 不存在'}), 404

        # 从数据库删除
        if storage.delete_monitor_config(pair_name):
            # 重载监控系统

            monitor = get_monitor()
            if monitor and hasattr(monitor, 'reload_config'):
                monitor.reload_config()

            return jsonify({'success': True, 'message': '交易对删除成功'})
        else:
            return jsonify({'error': '删除配置失败'}), 500

    except Exception as e:
        logger.error(f"Error deleting pair: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/admin/alerts', methods=['GET'])
@login_required
def api_admin_get_alerts():
    """获取警报配置。"""
    try:

        config = get_config()
        alerts_config = config.alerts

        return jsonify({
            'enabled': alerts_config.enabled,
            'rate_limit_minutes': alerts_config.rate_limit_minutes,
            'console': {
                'enabled': alerts_config.console.enabled
            },
            'email': {
                'enabled': alerts_config.email.enabled,
                'smtp_server': alerts_config.email.smtp_server,
                'smtp_port': alerts_config.email.smtp_port,
                'username': alerts_config.email.username,
                'from_email': alerts_config.email.from_email,
                'to_emails': alerts_config.email.to_emails
            },
            'webhook': {
                'enabled': alerts_config.webhook.enabled,
                'url': alerts_config.webhook.url,
                'headers': alerts_config.webhook.headers
            },
            'bark': {
                'enabled': alerts_config.bark.enabled,
                'keys': alerts_config.bark.keys,
                'server_url': alerts_config.bark.server_url,
                'timeout': alerts_config.bark.timeout
            }
        })

    except Exception as e:
        logger.error(f"Error getting alerts config: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/admin/alerts', methods=['PUT'])
@login_required
def api_admin_update_alerts():
    """更新警报配置。"""
    try:
        data = request.get_json()

        config = get_config()
        alerts_config = config.alerts

        # 更新主要设置
        if 'enabled' in data:
            alerts_config.enabled = data['enabled']
        if 'rate_limit_minutes' in data:
            alerts_config.rate_limit_minutes = data['rate_limit_minutes']

        # 更新控制台设置
        if 'console' in data:
            console_data = data['console']
            if 'enabled' in console_data:
                alerts_config.console.enabled = console_data['enabled']

        # 更新邮件设置
        if 'email' in data:
            email_data = data['email']
            if 'enabled' in email_data:
                alerts_config.email.enabled = email_data['enabled']
            if 'smtp_server' in email_data:
                alerts_config.email.smtp_server = email_data['smtp_server']
            if 'smtp_port' in email_data:
                alerts_config.email.smtp_port = email_data['smtp_port']
            if 'username' in email_data:
                alerts_config.email.username = email_data['username']
            if 'password' in email_data:
                alerts_config.email.password = email_data['password']
            if 'from_email' in email_data:
                alerts_config.email.from_email = email_data['from_email']
            if 'to_emails' in email_data:
                alerts_config.email.to_emails = email_data['to_emails']

        # 更新Bark设置
        if 'bark' in data:
            bark_data = data['bark']
            if 'enabled' in bark_data:
                alerts_config.bark.enabled = bark_data['enabled']
            if 'keys' in bark_data:
                alerts_config.bark.keys = bark_data['keys']
            if 'server_url' in bark_data:
                alerts_config.bark.server_url = bark_data['server_url']
            if 'timeout' in bark_data:
                alerts_config.bark.timeout = bark_data['timeout']

        # 保存配置

        config_manager = get_config_manager()
        if config and config_manager.save_config(config):
            # 重载监控系统

            monitor = get_monitor()
            if monitor and hasattr(monitor, 'reload_config'):
                monitor.reload_config()

            return jsonify({'success': True, 'message': '警报配置更新成功'})
        else:
            return jsonify({'error': '保存配置失败'}), 500

    except Exception as e:
        logger.error(f"Error updating alerts config: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/admin/test-alert', methods=['POST'])
@login_required
def api_admin_test_alert():
    """发送测试警报。"""
    try:
        data = request.get_json()
        alert_type = data.get('type', 'console')
        message = data.get('message', '测试警报')

        alert_system = getattr(current_app, 'alert_system', None)
        if not alert_system:
            return jsonify({'error': '警报系统未初始化'}), 500

        if alert_type == 'console':
            # 测试控制台警报
            console_channel = alert_system.channels.get('console')
            if not console_channel:
                return jsonify({'error': '控制台警报通道未启用'}), 500

            test_alert_data = {
                'alert_type': 'system',
                'system_alert_type': 'test',
                'message': message,
                'details': {
                    'test_time': datetime.now().isoformat(),
                    'test_source': 'Web管理界面'
                },
                'timestamp': datetime.now().timestamp()
            }

            success = console_channel.send_alert(test_alert_data)

        elif alert_type == 'email':
            # 测试邮件警报 - 使用SMTP.dev通道
            smtp_dev_channel = alert_system.channels.get('smtp_dev')
            if not smtp_dev_channel:
                return jsonify({'error': 'SMTP.dev邮件通道未启用'}), 500

            test_alert_data = {
                'alert_type': 'system',
                'system_alert_type': 'test',
                'message': message,
                'details': {
                    'test_time': datetime.now().isoformat(),
                    'test_source': 'Web管理界面',
                    'sender': smtp_dev_channel.from_email,
                    'recipients': smtp_dev_channel.to_emails
                },
                'timestamp': datetime.now().timestamp()
            }

            success = smtp_dev_channel.send_alert(test_alert_data)

        elif alert_type == 'bark':
            # 测试Bark推送警报
            bark_channel = alert_system.channels.get('bark')
            if not bark_channel:
                return jsonify({'error': 'Bark推送通道未启用'}), 500

            test_alert_data = {
                'type': 'test',
                'message': message,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            success = bark_channel.send_alert(test_alert_data)

        else:
            return jsonify({'error': f'不支持的警报类型: {alert_type}'}), 400

        if success:
            return jsonify({
                'success': True,
                'message': f'{alert_type}警报测试成功'
            })
        else:
            return jsonify({'error': f'{alert_type}警报测试失败'}), 500

    except Exception as e:
        logger.error(f"Error sending test alert: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/admin/chains', methods=['GET'])
@login_required
def api_admin_get_chains():
    """获取所有可用的区块链。"""
    try:
        # 获取API客户端

        api_client = get_api_client()
        chains = api_client.get_chains()

        chains_data = []
        for _, chain in chains.items():
            chains_data.append({
                'name': chain.name,
                'display_name': chain.display_name,
                'id': chain.id,
                'icon_url': getattr(chain, 'icon_url', None),
                'native_currency': {
                    'symbol': chain.native_currency.symbol,
                    'name': chain.native_currency.name,
                    'decimals': chain.native_currency.decimals
                } if chain.native_currency else None
            })

        # 按显示名称排序
        chains_data.sort(key=lambda x: x['display_name'])

        return jsonify({'chains': chains_data})

    except Exception as e:
        logger.error(f"Error getting chains: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/admin/chains/<chain_name>/tokens', methods=['GET'])
@login_required
def api_admin_get_tokens(chain_name):
    """获取指定链的所有代币。"""
    try:
        # 获取API客户端

        api_client = get_api_client()
        tokens = api_client.get_tokens_for_chain(chain_name)

        tokens_data = []
        for _, token in tokens.items():
            tokens_data.append({
                'symbol': token.symbol,
                'name': token.name,
                'address': token.address,
                'decimals': token.decimals,
                'icon_url': getattr(token, 'icon_url', None)
            })

        # 按符号排序
        tokens_data.sort(key=lambda x: x['symbol'])

        return jsonify({'tokens': tokens_data})

    except Exception as e:
        logger.error(f"Error getting tokens for chain {chain_name}: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/admin/reload', methods=['POST'])
@login_required
def api_admin_reload():
    """重载系统配置。"""
    try:

        config_manager = get_config_manager()

        # 重新加载配置
        new_config = config_manager.load_config(force_reload=True)
        get_app().relay_config = new_config

        # 重载监控系统
        monitor = get_monitor()
        if monitor and hasattr(monitor, 'reload_config'):
            monitor.reload_config()

        return jsonify({'success': True, 'message': '配置重载成功'})

    except Exception as e:
        logger.error(f"Error reloading config: {e}")
        return jsonify({'error': str(e)}), 500


@admin_bp.route('/emails')
@login_required
def emails():
    """查看邮件管理。"""
    try:
        # 获取SMTP.dev通道
        alert_system = getattr(current_app, 'alert_system', None)
        if not alert_system:
            flash('警报系统未初始化', 'error')
            return redirect(url_for('admin.dashboard'))

        smtp_dev_channel = alert_system.channels.get('smtp_dev')
        if not smtp_dev_channel:
            flash('SMTP.dev邮件通道未启用', 'warning')
            return redirect(url_for('admin.dashboard'))

        # 获取可用账户
        accounts = smtp_dev_channel.get_available_accounts()

        # 获取选中账户的邮件
        selected_email = request.args.get('email', accounts[0]['address'] if accounts else None)
        messages = []
        if selected_email:
            messages = smtp_dev_channel.get_received_messages(target_email=selected_email, limit=20)

        return render_template('admin/emails.html',
                             messages=messages,
                             accounts=accounts,
                             selected_email=selected_email,
                             from_email=smtp_dev_channel.from_email,
                             to_emails=smtp_dev_channel.to_emails)

    except Exception as e:
        logger.error(f"Error loading emails: {e}")
        flash(f'加载邮件失败: {str(e)}', 'error')
        return redirect(url_for('admin.dashboard'))


@admin_bp.route('/emails/<message_id>')
@login_required
def email_detail(message_id):
    """查看邮件详情。"""
    try:
        # 获取SMTP.dev通道
        alert_system = getattr(current_app, 'alert_system', None)
        if not alert_system:
            return jsonify({'error': '警报系统未初始化'}), 500

        smtp_dev_channel = alert_system.channels.get('smtp_dev')
        if not smtp_dev_channel:
            return jsonify({'error': 'SMTP.dev邮件通道未启用'}), 500

        # 获取账户邮箱参数
        account_email = request.args.get('email')

        # 获取邮件详情
        message_details = smtp_dev_channel.get_message_details(message_id, account_email)
        if not message_details:
            return jsonify({'error': '邮件未找到'}), 404

        return jsonify(message_details)

    except Exception as e:
        logger.error(f"Error loading email detail: {e}")
        return jsonify({'error': str(e)}), 500


@admin_bp.route('/test-email-alert', methods=['POST'])
@login_required
def test_email_alert():
    """发送测试邮件警报。"""
    try:
        alert_system = getattr(current_app, 'alert_system', None)
        if not alert_system:
            return jsonify({'error': '警报系统未初始化'}), 500

        smtp_dev_channel = alert_system.channels.get('smtp_dev')
        if not smtp_dev_channel:
            return jsonify({'error': 'SMTP.dev邮件通道未启用'}), 500

        # 发送测试警报
        test_alert_data = {
            'alert_type': 'system',
            'system_alert_type': 'test',
            'message': '邮件系统测试警报',
            'details': {
                'test_time': datetime.now().isoformat(),
                'test_source': 'Web管理界面',
                'sender': smtp_dev_channel.from_email,
                'recipients': smtp_dev_channel.to_emails
            },
            'timestamp': datetime.now().timestamp()
        }

        success = smtp_dev_channel.send_alert(test_alert_data)

        if success:
            return jsonify({
                'success': True,
                'message': f'测试警报已发送到 {", ".join(smtp_dev_channel.to_emails)}'
            })
        else:
            return jsonify({'error': '发送测试警报失败'}), 500

    except Exception as e:
        logger.error(f"Error sending test email alert: {e}")
        return jsonify({'error': str(e)}), 500


@main_bp.route('/health')
def health_check():
    """Health check endpoint for Docker and monitoring systems."""
    try:
        import os
        from datetime import datetime

        # Basic health information
        health_info = {
            'status': 'healthy',
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'version': os.environ.get('VERSION', 'unknown'),
            'uptime': None,
            'checks': {}
        }

        # Check configuration
        try:
            # config = get_config()  # 未使用
            health_info['checks']['config'] = 'ok'
        except Exception as e:
            health_info['checks']['config'] = f'error: {str(e)}'
            health_info['status'] = 'unhealthy'

        # Check storage
        try:
            storage = get_storage()
            # Try a simple database operation
            storage.get_latest_price('test')  # This will return None but tests DB connection
            health_info['checks']['database'] = 'ok'
        except Exception as e:
            health_info['checks']['database'] = f'error: {str(e)}'
            health_info['status'] = 'unhealthy'

        # Check monitor
        try:

            monitor = get_monitor()
            if monitor and hasattr(monitor, 'is_running') and monitor.is_running:
                health_info['checks']['monitor'] = 'running'
            else:
                health_info['checks']['monitor'] = 'stopped'
        except Exception as e:
            health_info['checks']['monitor'] = f'error: {str(e)}'

        # Check alert system
        try:
            app = get_app()

            alert_system = app.alert_system
            if alert_system and hasattr(alert_system, 'channels'):
                enabled_channels = len([ch for ch in alert_system.channels.values() if ch.enabled])
                health_info['checks']['alerts'] = f'{enabled_channels} channels enabled'
            else:
                health_info['checks']['alerts'] = 'not configured'
        except Exception as e:
            health_info['checks']['alerts'] = f'error: {str(e)}'

        # Return appropriate HTTP status
        status_code = 200 if health_info['status'] == 'healthy' else 503

        return jsonify(health_info), status_code

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'error': str(e)
        }), 503


@main_bp.route('/ready')
def readiness_check():
    """Readiness check endpoint for Kubernetes and container orchestration."""
    try:
        # Check if all critical components are ready
        # config = get_config()  # 未使用
        storage = get_storage()

        # Verify configuration is valid
        enabled_pairs = storage.get_enabled_monitor_pairs()
        if not enabled_pairs:
            return jsonify({
                'status': 'not_ready',
                'reason': 'No monitoring pairs configured'
            }), 503

        # Verify database is accessible
        try:
            storage.get_latest_price('test')
        except Exception as e:
            return jsonify({
                'status': 'not_ready',
                'reason': f'Database not accessible: {str(e)}'
            }), 503

        return jsonify({
            'status': 'ready',
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'pairs_configured': len(enabled_pairs)
        }), 200

    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return jsonify({
            'status': 'not_ready',
            'reason': str(e)
        }), 503


@api_bp.route('/admin/pairs/<pair_name>/test-trade', methods=['POST'])
@login_required
def api_admin_test_trade(pair_name):
    """执行测试交易。"""
    try:
        data = request.get_json()
        test_amount = data.get('test_amount')

        if not test_amount or test_amount <= 0:
            return jsonify({'error': '测试金额必须大于0'}), 400

        storage = get_storage()

        # 获取交易对配置
        pair_config = storage.get_monitor_config(pair_name)
        if not pair_config:
            return jsonify({'error': '交易对不存在'}), 404

        # 检查自动交易是否启用
        if not pair_config.auto_trading_enabled:
            return jsonify({'error': '该交易对未启用自动交易'}), 400

        # 检查钱包配置
        if not pair_config.wallet_name:
            return jsonify({'error': '该交易对未配置钱包'}), 400

        # 获取交易执行器
        trading_executor = get_trading_executor()
        if not trading_executor:
            return jsonify({'error': '交易执行器未初始化'}), 500

        # 获取 API 客户端
        config_manager = get_config_manager()
        api_client = get_api_client()

        # 获取测试交易的 quote
        logger.info(f"获取测试交易 quote: {pair_name}, 金额: {test_amount}")
        try:
            quote_response = api_client.get_quote(
                origin_chain=pair_config.origin_chain,
                destination_chain=pair_config.destination_chain,
                origin_token=pair_config.origin_token,
                destination_token=pair_config.destination_token,
                amount=str(test_amount)
            )
        except Exception as e:
            logger.warning(f"无法获取真实 quote，使用模拟数据: {e}")
            # 创建模拟 quote 用于开发测试
            from ..api.models import Quote, QuoteDetails
            mock_quote = Quote(
                steps=[],
                fees={},
                details=QuoteDetails(
                    operation="bridge",
                    sender="0x0000000000000000000000000000000000000000",
                    recipient="0x0000000000000000000000000000000000000000",
                    currency_in={"amount": str(test_amount)},
                    currency_out={"amount": str(test_amount * 0.99)},  # 模拟 1% 费用
                    rate="0.99",
                    time_estimate=300
                )
            )

            class MockQuoteResponse:
                def __init__(self, quote):
                    self.quote = quote

            quote_response = MockQuoteResponse(mock_quote)

        # 执行测试交易
        logger.info(f"执行测试交易: {pair_name}")
        trading_result = trading_executor.execute_test_trade(
            pair_config, test_amount, quote_response.quote
        )

        if trading_result.success:
            return jsonify({
                'success': True,
                'message': '测试交易执行成功',
                'transaction_hash': trading_result.tx_hash,
                'amount': test_amount,
                'pair_name': pair_name
            })
        else:
            return jsonify({
                'success': False,
                'error': trading_result.error_message
            })

    except Exception as e:
        logger.error(f"Error executing test trade for {pair_name}: {e}")
        return jsonify({'error': f'执行测试交易时发生错误: {str(e)}'}), 500


# =============================================================================
# 钱包管理API端点
# =============================================================================

@api_bp.route('/admin/wallets', methods=['GET'])
@login_required
def api_admin_get_wallets():
    """获取所有钱包列表。"""
    try:
        wallet_manager = get_wallet_manager()
        wallets = wallet_manager.list_wallets()

        return jsonify({
            'success': True,
            'wallets': wallets
        })

    except Exception as e:
        logger.error(f"Error getting wallets: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/admin/wallets', methods=['POST'])
@login_required
def api_admin_add_wallet():
    """添加新钱包。"""
    try:
        data = request.get_json()

        name = data.get('name', '').strip()
        private_key = data.get('private_key', '').strip()

        if not all([name, private_key]):
            return jsonify({'error': '钱包名称和私钥都是必需的'}), 400

        # 不再需要主密码检查

        wallet_manager = get_wallet_manager()
        success = wallet_manager.add_wallet(name, private_key)

        if success:
            # 获取钱包地址
            address = wallet_manager.get_wallet_address(name)
            return jsonify({
                'success': True,
                'message': f'钱包 "{name}" 添加成功',
                'address': address
            })
        else:
            return jsonify({'error': '钱包添加失败'}), 400

    except WalletError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error adding wallet: {e}")
        return jsonify({'error': f'添加钱包时发生错误: {str(e)}'}), 500


@api_bp.route('/admin/wallets/<wallet_name>', methods=['DELETE'])
@login_required
def api_admin_delete_wallet(wallet_name):
    """删除钱包。"""
    try:
        wallet_manager = get_wallet_manager()
        success = wallet_manager.delete_wallet(wallet_name)

        if success:
            return jsonify({
                'success': True,
                'message': f'钱包 "{wallet_name}" 删除成功'
            })
        else:
            return jsonify({'error': '钱包删除失败'}), 400

    except WalletNotFoundError as e:
        return jsonify({'error': str(e)}), 404
    except Exception as e:
        logger.error(f"Error deleting wallet: {e}")
        return jsonify({'error': f'删除钱包时发生错误: {str(e)}'}), 500


@api_bp.route('/admin/pairs/<pair_name>/trading', methods=['GET'])
@login_required
def api_admin_get_trading_config(pair_name):
    """获取交易对的自动交易配置。"""
    try:
        storage = get_storage()

        # 获取监控对配置
        pairs = storage.get_all_monitor_configs()
        pair_config = None

        for pair in pairs:
            if pair.name == pair_name:
                pair_config = pair
                break

        if not pair_config:
            return jsonify({'error': '监控对不存在'}), 404

        config = {
            'auto_trading_enabled': getattr(pair_config, 'auto_trading_enabled', False),
            'wallet_name': getattr(pair_config, 'wallet_name', None),
            'trading_amount': pair_config.amount,  # 使用监控配置中的金额
            'max_slippage_percent': getattr(pair_config, 'max_slippage_percent', 20.0),
            'daily_limit_usd': getattr(pair_config, 'daily_limit_usd', None),
            'single_trade_limit_usd': getattr(pair_config, 'single_trade_limit_usd', None)
        }

        return jsonify({
            'success': True,
            'config': config
        })

    except Exception as e:
        logger.error(f"Error getting trading config: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/admin/pairs/<pair_name>/trading', methods=['PUT'])
@login_required
def api_admin_update_trading_config(pair_name):
    """更新交易对的自动交易配置。"""
    try:
        data = request.get_json()
        storage = get_storage()

        # 获取当前配置
        pairs = storage.get_all_monitor_configs()
        pair_config = None

        for pair in pairs:
            if pair.name == pair_name:
                pair_config = pair
                break

        if not pair_config:
            return jsonify({'error': '监控对不存在'}), 404

        # 更新配置字段
        updated_fields = {}

        if 'auto_trading_enabled' in data:
            updated_fields['auto_trading_enabled'] = bool(data['auto_trading_enabled'])

        if 'wallet_name' in data:
            updated_fields['wallet_name'] = data['wallet_name'] if data['wallet_name'] else None

        # 强制设置最大滑点为 20%
        updated_fields['max_slippage_percent'] = 20.0

        # 交易金额使用监控配置中的值，不允许单独设置
        # trading_amount 将从监控配置的 amount 字段获取

        if 'daily_limit_usd' in data:
            updated_fields['daily_limit_usd'] = float(data['daily_limit_usd']) if data['daily_limit_usd'] else None

        if 'single_trade_limit_usd' in data:
            updated_fields['single_trade_limit_usd'] = float(data['single_trade_limit_usd']) if data['single_trade_limit_usd'] else None

        # 更新数据库中的配置
        success = storage.update_monitor_config_trading(pair_name, updated_fields)

        if success:
            return jsonify({
                'success': True,
                'message': '自动交易配置更新成功'
            })
        else:
            return jsonify({'error': '配置更新失败'}), 400

    except Exception as e:
        logger.error(f"Error updating trading config: {e}")
        return jsonify({'error': f'更新配置时发生错误: {str(e)}'}), 500



# =============================================================================
# 设置管理API端点
# =============================================================================

@api_bp.route('/admin/settings/<section>', methods=['PUT'])
@login_required
def api_admin_update_settings(section):
    """更新系统设置。"""
    try:
        data = request.get_json()

        config_manager = get_config_manager()

        # 更新配置
        success = config_manager.update_section(section, data)

        if success:
            # 重新加载配置
            get_app().relay_config = config_manager.get_config()
            return jsonify({'success': True, 'message': f'{section}设置更新成功'})
        else:
            return jsonify({'error': '设置更新失败'}), 400

    except Exception as e:
        logger.error(f"Error updating settings for {section}: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/admin/database/status', methods=['GET'])
@login_required
def api_admin_database_status():
    """获取数据库状态。"""
    try:
        storage = get_storage()
        stats = storage.get_database_stats()

        # 获取维护管理器状态
        maintenance_manager = getattr(current_app, 'maintenance_manager', None)
        last_cleanup = None
        if maintenance_manager:
            maintenance_stats = maintenance_manager.get_maintenance_stats()
            last_cleanup = maintenance_stats.get('last_database_cleanup')
            if last_cleanup:
                last_cleanup = last_cleanup.strftime('%Y-%m-%d %H:%M:%S')

        return jsonify({
            'size_mb': stats.get('db_size_mb', 0),
            'last_cleanup': last_cleanup,
            'record_counts': {
                'price_history': stats.get('price_history_count', 0),
                'alert_history': stats.get('alert_history_count', 0),
                'system_stats': stats.get('system_stats_count', 0)
            }
        })

    except Exception as e:
        logger.error(f"Error getting database status: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/admin/maintenance/cleanup', methods=['POST'])
@login_required
def api_admin_maintenance_cleanup():
    """执行数据库清理。"""
    try:
        maintenance_manager = getattr(current_app, 'maintenance_manager', None)
        if not maintenance_manager:
            return jsonify({'error': '维护管理器未初始化'}), 500

        result = maintenance_manager.run_database_cleanup()

        if result['success']:
            return jsonify({
                'success': True,
                'space_saved_mb': result.get('space_saved_mb', 0),
                'message': '数据库清理完成'
            })
        else:
            return jsonify({'error': result.get('error', '清理失败')}), 500

    except Exception as e:
        logger.error(f"Error running database cleanup: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/admin/api/status', methods=['GET'])
@login_required
def api_admin_api_status():
    """检查API连接状态。"""
    try:

        api_client = get_api_client()

        # 简单的连接测试
        try:
            chains = api_client.get_chains()
            connected = len(chains) > 0
        except Exception as e:
            logger.debug(f"API connection test failed: {e}")
            connected = False

        return jsonify({
            'connected': connected,
            'base_url': api_client.base_url
        })

    except Exception as e:
        logger.error(f"Error checking API status: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/admin/api/test', methods=['POST'])
@login_required
def api_admin_api_test():
    """测试API连接。"""
    try:

        api_client = get_api_client()

        # 执行连接测试
        chains = api_client.get_chains()

        return jsonify({
            'success': True,
            'message': f'API连接正常，发现 {len(chains)} 条区块链',
            'chains_count': len(chains)
        })

    except Exception as e:
        logger.error(f"Error testing API connection: {e}")
        return jsonify({'error': str(e)}), 500


# =============================================================================
# 交易历史API端点
# =============================================================================

@api_bp.route('/admin/transactions', methods=['GET'])
@login_required
def api_admin_get_transactions():
    """获取交易历史记录。"""
    try:
        storage = get_storage()

        # 获取查询参数
        page = int(request.args.get('page', 1))
        limit = min(int(request.args.get('limit', 20)), 100)  # 最大100条
        status = request.args.get('status')  # 可选的状态过滤
        pair_name = request.args.get('pair_name')  # 可选的交易对过滤

        offset = (page - 1) * limit

        # 构建查询条件
        where_conditions = []
        params = []

        if status:
            where_conditions.append("status = ?")
            params.append(status)

        if pair_name:
            where_conditions.append("monitor_pair_name = ?")
            params.append(pair_name)

        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)

        with storage.get_connection() as conn:
            # 获取总数
            count_query = f"SELECT COUNT(*) FROM transaction_history {where_clause}"
            cursor = conn.execute(count_query, params)
            total_count = cursor.fetchone()[0]

            # 获取交易记录
            query = f"""
                SELECT id, monitor_pair_name, wallet_name, wallet_address, tx_hash,
                       request_id, status, amount_in, token_in_symbol, amount_out,
                       token_out_symbol, gas_used, gas_price, total_fee_usd,
                       trigger_price, execution_price, slippage_percent,
                       error_message, retry_count, created_at, submitted_at, confirmed_at
                FROM transaction_history
                {where_clause}
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            """

            cursor = conn.execute(query, params + [limit, offset])
            rows = cursor.fetchall()

            transactions = []
            for row in rows:
                transactions.append({
                    'id': row[0],
                    'monitor_pair_name': row[1],
                    'wallet_name': row[2],
                    'wallet_address': row[3],
                    'tx_hash': row[4],
                    'request_id': row[5],
                    'status': row[6],
                    'amount_in': row[7],
                    'token_in_symbol': row[8],
                    'amount_out': row[9],
                    'token_out_symbol': row[10],
                    'gas_used': row[11],
                    'gas_price': row[12],
                    'total_fee_usd': row[13],
                    'trigger_price': row[14],
                    'execution_price': row[15],
                    'slippage_percent': row[16],
                    'error_message': row[17],
                    'retry_count': row[18],
                    'created_at': row[19],
                    'submitted_at': row[20],
                    'confirmed_at': row[21]
                })

            return jsonify({
                'success': True,
                'transactions': transactions,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total_count,
                    'pages': (total_count + limit - 1) // limit
                }
            })

    except Exception as e:
        logger.error(f"Error getting transactions: {e}")
        return jsonify({'error': str(e)}), 500


@api_bp.route('/admin/transactions/stats', methods=['GET'])
@login_required
def api_admin_get_transaction_stats():
    """获取交易统计信息。"""
    try:
        storage = get_storage()

        with storage.get_connection() as conn:
            # 总体统计
            cursor = conn.execute("""
                SELECT
                    COUNT(*) as total_transactions,
                    COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_count,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
                    COUNT(CASE WHEN status = 'submitted' THEN 1 END) as pending_count,
                    COALESCE(SUM(CASE WHEN status = 'confirmed' THEN total_fee_usd END), 0) as total_fees_usd
                FROM transaction_history
            """)

            stats = cursor.fetchone()

            # 今日统计
            cursor = conn.execute("""
                SELECT
                    COUNT(*) as today_transactions,
                    COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as today_confirmed,
                    COALESCE(SUM(CASE WHEN status = 'confirmed' THEN total_fee_usd END), 0) as today_fees_usd
                FROM transaction_history
                WHERE DATE(created_at) = DATE('now')
            """)

            today_stats = cursor.fetchone()

            # 按交易对统计
            cursor = conn.execute("""
                SELECT
                    monitor_pair_name,
                    COUNT(*) as count,
                    COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_count
                FROM transaction_history
                GROUP BY monitor_pair_name
                ORDER BY count DESC
                LIMIT 10
            """)

            pair_stats = [
                {
                    'pair_name': row[0],
                    'total_count': row[1],
                    'confirmed_count': row[2],
                    'success_rate': (row[2] / row[1] * 100) if row[1] > 0 else 0
                }
                for row in cursor.fetchall()
            ]

            return jsonify({
                'success': True,
                'stats': {
                    'total_transactions': stats[0],
                    'confirmed_count': stats[1],
                    'failed_count': stats[2],
                    'pending_count': stats[3],
                    'total_fees_usd': float(stats[4]),
                    'success_rate': (stats[1] / stats[0] * 100) if stats[0] > 0 else 0,
                    'today_transactions': today_stats[0],
                    'today_confirmed': today_stats[1],
                    'today_fees_usd': float(today_stats[2]),
                    'pair_stats': pair_stats
                }
            })

    except Exception as e:
        logger.error(f"Error getting transaction stats: {e}")
        return jsonify({'error': str(e)}), 500

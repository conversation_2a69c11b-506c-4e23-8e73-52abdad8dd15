"""
Relay API 响应的数据模型。
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime


class Token(BaseModel):
    """表示区块链上的代币。"""

    id: Optional[str] = None
    symbol: str
    name: str
    address: str
    decimals: int
    supports_bridging: bool = Field(default=True, alias="supportsBridging")
    metadata: Optional[Dict[str, Any]] = None

    class Config:
        populate_by_name = True


class Chain(BaseModel):
    """表示区块链网络。"""
    
    id: int
    name: str
    display_name: str = Field(alias="displayName")
    http_rpc_url: str = Field(alias="httpRpcUrl")
    ws_rpc_url: Optional[str] = Field(default=None, alias="wsRpcUrl")
    explorer_url: str = Field(alias="explorerUrl")
    explorer_name: str = Field(alias="explorerName")
    deposit_enabled: bool = Field(alias="depositEnabled")
    token_support: str = Field(alias="tokenSupport")
    disabled: bool = False
    native_currency: Token = Field(alias="currency")
    featured_tokens: List[Token] = Field(default=[], alias="featuredTokens")
    erc20_currencies: List[Token] = Field(default=[], alias="erc20Currencies")
    icon_url: Optional[str] = Field(default=None, alias="iconUrl")
    
    class Config:
        populate_by_name = True


class Fee(BaseModel):
    """表示费用结构。"""
    
    currency: Token
    amount: str
    amount_formatted: str = Field(alias="amountFormatted")
    amount_usd: str = Field(alias="amountUsd")
    minimum_amount: Optional[str] = Field(default=None, alias="minimumAmount")
    
    class Config:
        populate_by_name = True


class QuoteDetails(BaseModel):
    """报价的详细信息。"""
    
    operation: str
    sender: str
    recipient: str
    currency_in: Dict[str, Any] = Field(alias="currencyIn")
    currency_out: Dict[str, Any] = Field(alias="currencyOut")
    total_impact: Optional[Dict[str, str]] = Field(default=None, alias="totalImpact")
    swap_impact: Optional[Dict[str, str]] = Field(default=None, alias="swapImpact")
    rate: str
    time_estimate: float = Field(alias="timeEstimate")
    
    class Config:
        populate_by_name = True


class Quote(BaseModel):
    """表示桥接报价响应。"""
    
    steps: List[Dict[str, Any]]
    fees: Dict[str, Fee]
    details: QuoteDetails
    
    @property
    def total_fee_usd(self) -> float:
        """计算总费用（美元）。"""
        total = 0.0
        for fee in self.fees.values():
            try:
                total += float(fee.amount_usd)
            except (ValueError, TypeError):
                continue
        return total
    
    @property
    def exchange_rate(self) -> float:
        """基于美元金额获取汇率。"""
        import logging
        logger = logging.getLogger(__name__)

        try:
            # 获取输入和输出的USD金额
            currency_in_usd = float(self.details.currency_in.get('amountUsd', 0))
            currency_out_usd = float(self.details.currency_out.get('amountUsd', 0))

            logger.debug(f"Currency amounts - In: ${currency_in_usd:.6f}, Out: ${currency_out_usd:.6f}")

            # 如果输入金额为0，返回0避免除零错误
            if currency_in_usd == 0:
                logger.debug("Input amount is 0, returning 0.0")
                return 0.0

            # 计算汇率：输出金额 / 输入金额
            rate = currency_out_usd / currency_in_usd
            logger.debug(f"Calculated exchange rate: {rate:.6f} (${currency_out_usd:.6f} / ${currency_in_usd:.6f})")
            return rate

        except (ValueError, TypeError, KeyError) as e:
            logger.debug(f"Error calculating USD-based rate: {e}, falling back to details.rate")
            # 如果出错，回退到原来的rate字段
            try:
                fallback_rate = float(self.details.rate)
                logger.debug(f"Using fallback rate: {fallback_rate}")
                return fallback_rate
            except (ValueError, TypeError):
                logger.debug("Fallback rate also failed, returning 0.0")
                return 0.0


class QuoteResponse(BaseModel):
    """报价请求的响应包装器。"""
    
    quote: Quote
    timestamp: datetime = Field(default_factory=datetime.now)
    origin_chain: str
    destination_chain: str
    origin_token: str
    destination_token: str
    amount: str
    
    class Config:
        arbitrary_types_allowed = True


class PriceData(BaseModel):
    """简单的价格数据模型。"""
    
    price: float
    timestamp: datetime = Field(default_factory=datetime.now)
    chain_id: int
    token_address: str
    token_symbol: str

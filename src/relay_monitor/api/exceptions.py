"""
Relay API 模块的自定义异常类。
"""


class RelayAPIError(Exception):
    """Relay API 相关错误的基础异常类。"""

    def __init__(self, message: str, status_code: int = None, response_data: dict = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class ChainNotFoundError(RelayAPIError):
    """当请求的区块链未找到或不支持时抛出。"""

    def __init__(self, chain_name: str):
        super().__init__(f"区块链 '{chain_name}' 未找到或不支持")
        self.chain_name = chain_name


class TokenNotFoundError(RelayAPIError):
    """当请求的代币在指定区块链上未找到时抛出。"""

    def __init__(self, token_symbol: str, chain_name: str):
        super().__init__(f"代币 '{token_symbol}' 在区块链 '{chain_name}' 上未找到")
        self.token_symbol = token_symbol
        self.chain_name = chain_name


class QuoteError(RelayAPIError):
    """获取报价时发生错误时抛出。"""
    pass


class NetworkError(RelayAPIError):
    """网络相关错误时抛出。"""
    pass


class RateLimitError(RelayAPIError):
    """API 速率限制超出时抛出。"""
    pass

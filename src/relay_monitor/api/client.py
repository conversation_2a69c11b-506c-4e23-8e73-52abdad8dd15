"""
具有自动链和代币解析功能的 Relay API 客户端。

此客户端为 Relay API 提供高级接口，具有智能缓存和自动将链名称解析为 ID、
代币符号解析为地址的功能。
"""

import time
import logging
from typing import Dict, Optional
from decimal import Decimal

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from .models import Chain, Token, Quote, QuoteResponse, PriceData
from .exceptions import (
    RelayAPIError, ChainNotFoundError, TokenNotFoundError,
    QuoteError, NetworkError, RateLimitError
)


logger = logging.getLogger(__name__)


class RelayAPIClient:
    """
    High-level Relay API client with automatic chain and token resolution.
    
    Features:
    - Automatic chain name to ID resolution
    - Token symbol to address resolution
    - Intelligent caching of chain and token data
    - Retry logic with exponential backoff
    - Rate limiting protection
    """
    
    def __init__(
        self,
        base_url: str = "https://api.relay.link",
        timeout: int = 30,
        retry_attempts: int = 3,
        retry_delay: float = 1.0,
        cache_ttl: int = 1800  # 默认30分钟，与配置文件保持一致
    ):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.retry_attempts = retry_attempts
        self.retry_delay = retry_delay
        self.cache_ttl = cache_ttl
        
        # Cache for chains and tokens
        self._chains_cache: Dict[str, Chain] = {}
        self._chains_by_id: Dict[int, Chain] = {}
        self._tokens_cache: Dict[str, Dict[str, Token]] = {}  # chain_name -> {symbol: token}
        self._cache_timestamps: Dict[str, float] = {}
        
        # Setup session with retry strategy
        self.session = requests.Session()
        retry_strategy = Retry(
            total=retry_attempts,
            backoff_factor=retry_delay,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Rate limiting
        self._last_request_time = 0
        self._min_request_interval = 0.1  # 100ms between requests
    
    def _wait_for_rate_limit(self):
        """Ensure we don't exceed rate limits."""
        current_time = time.time()
        time_since_last = current_time - self._last_request_time
        if time_since_last < self._min_request_interval:
            time.sleep(self._min_request_interval - time_since_last)
        self._last_request_time = time.time()
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> dict:
        """Make a request to the Relay API with error handling."""
        self._wait_for_rate_limit()
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            logger.debug(f"Making {method} request to {url}")
            response = self.session.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            
            if response.status_code == 429:
                raise RateLimitError("API rate limit exceeded")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.Timeout:
            raise NetworkError(f"Request timeout after {self.timeout}s")
        except requests.exceptions.ConnectionError:
            raise NetworkError("Connection error")
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 429:
                raise RateLimitError("API rate limit exceeded")
            raise RelayAPIError(
                f"HTTP {e.response.status_code}: {e.response.text}",
                status_code=e.response.status_code
            )
        except Exception as e:
            raise RelayAPIError(f"Unexpected error: {str(e)}")
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache entry is still valid."""
        if cache_key not in self._cache_timestamps:
            return False
        return time.time() - self._cache_timestamps[cache_key] < self.cache_ttl
    
    def _update_cache_timestamp(self, cache_key: str):
        """Update cache timestamp."""
        self._cache_timestamps[cache_key] = time.time()
    
    def get_chains(self, force_refresh: bool = False) -> Dict[str, Chain]:
        """
        Get all supported chains with caching.
        
        Args:
            force_refresh: Force refresh of cached data
            
        Returns:
            Dictionary mapping chain names to Chain objects
        """
        cache_key = "chains"
        
        if not force_refresh and self._chains_cache and self._is_cache_valid(cache_key):
            logger.debug("Using cached chains data")
            return self._chains_cache
        
        logger.info("Fetching chains from Relay API")
        try:
            data = self._make_request("GET", "/chains")
            chains = {}
            
            for chain_data in data.get("chains", []):
                try:
                    chain = Chain(**chain_data)
                    chains[chain.name] = chain
                    self._chains_by_id[chain.id] = chain
                except Exception as e:
                    logger.warning(f"Failed to parse chain data: {e}")
                    continue
            
            self._chains_cache = chains
            self._update_cache_timestamp(cache_key)
            
            logger.info(f"Loaded {len(chains)} chains")
            return chains
            
        except Exception as e:
            logger.error(f"Failed to fetch chains: {e}")
            raise
    
    def get_chain_by_name(self, chain_name: str) -> Chain:
        """Get chain by name with automatic caching."""
        chains = self.get_chains()
        
        if chain_name not in chains:
            raise ChainNotFoundError(chain_name)
        
        return chains[chain_name]
    
    def get_chain_by_id(self, chain_id: int) -> Chain:
        """Get chain by ID."""
        if not self._chains_by_id:
            self.get_chains()  # Load chains if not cached

        if chain_id not in self._chains_by_id:
            raise ChainNotFoundError(f"Chain ID {chain_id}")

        return self._chains_by_id[chain_id]

    def get_tokens_for_chain(self, chain_name: str, force_refresh: bool = False) -> Dict[str, Token]:
        """
        Get all tokens for a specific chain.

        Args:
            chain_name: Name of the chain
            force_refresh: Force refresh of cached data

        Returns:
            Dictionary mapping token symbols to Token objects
        """
        cache_key = f"tokens_{chain_name}"

        if not force_refresh and chain_name in self._tokens_cache and self._is_cache_valid(cache_key):
            logger.debug(f"Using cached tokens data for {chain_name}")
            return self._tokens_cache[chain_name]

        # Get chain info first
        chain = self.get_chain_by_name(chain_name)

        tokens = {}

        # Add native token
        if chain.native_currency:
            tokens[chain.native_currency.symbol] = chain.native_currency
     
        # Add featured tokens
        for token in chain.featured_tokens:
            tokens[token.symbol] = token

        # Add ERC20 tokens
        for token in chain.erc20_currencies:
            tokens[token.symbol] = token

        self._tokens_cache[chain_name] = tokens
        self._update_cache_timestamp(cache_key)

        logger.info(f"Loaded {len(tokens)} tokens for chain {chain_name}")
        return tokens

    def get_token(self, chain_name: str, token_symbol: str) -> Token:
        """
        Get token by symbol on a specific chain.

        Args:
            chain_name: Name of the chain
            token_symbol: Symbol of the token

        Returns:
            Token object
        """
        tokens = self.get_tokens_for_chain(chain_name)

        if token_symbol not in tokens:
            raise TokenNotFoundError(token_symbol, chain_name)

        return tokens[token_symbol]

    def resolve_token_address(self, chain_name: str, token_symbol: str) -> str:
        """Resolve token symbol to address on a specific chain."""
        token = self.get_token(chain_name, token_symbol)
        return token.address

    def get_quote(
        self,
        origin_chain: str,
        destination_chain: str,
        origin_token: str,
        destination_token: str,
        amount: str,
        user_address: Optional[str] = None
    ) -> QuoteResponse:
        """
        Get a bridge quote between chains and tokens.

        Args:
            origin_chain: Name of the origin chain
            destination_chain: Name of the destination chain
            origin_token: Symbol of the origin token
            destination_token: Symbol of the destination token
            amount: Amount to bridge (in token units, e.g., "1.0")
            user_address: User wallet address (optional)

        Returns:
            QuoteResponse object with quote details
        """
        try:
            # Resolve chain IDs
            origin_chain_obj = self.get_chain_by_name(origin_chain)
            dest_chain_obj = self.get_chain_by_name(destination_chain)

            # Set appropriate user address based on chain types
            if user_address is None:
                user_address = "******************************************"  # Default EVM address

            # Resolve token addresses
            origin_token_obj = self.get_token(origin_chain, origin_token)
            dest_token_obj = self.get_token(destination_chain, destination_token)

            # Convert amount to wei/smallest unit
            amount_decimal = Decimal(amount)
            amount_wei = str(int(amount_decimal * (10 ** origin_token_obj.decimals)))

            # Set appropriate addresses for each chain
            origin_user_address = user_address
            dest_user_address = user_address

            # Use Solana-compatible address for Solana chains
            if origin_chain.lower() == 'solana':
                origin_user_address = "11111111111111111111111111111111"  # Solana system program address
            if destination_chain.lower() == 'solana':
                dest_user_address = "11111111111111111111111111111111"  # Solana system program address

            # Prepare quote request
            quote_data = {
                "user": origin_user_address,
                "recipient": dest_user_address,
                "originChainId": origin_chain_obj.id,
                "destinationChainId": dest_chain_obj.id,
                "originCurrency": origin_token_obj.address,
                "destinationCurrency": dest_token_obj.address,
                "amount": amount_wei,
                "tradeType": "EXACT_INPUT"
            }

            logger.info(f"Getting quote: {origin_chain} {origin_token} -> {destination_chain} {destination_token}")
            logger.debug(f"Quote request: {quote_data}")

            response_data = self._make_request("POST", "/quote", json=quote_data)

            # Parse response
            quote = Quote(**response_data)

            return QuoteResponse(
                quote=quote,
                origin_chain=origin_chain,
                destination_chain=destination_chain,
                origin_token=origin_token,
                destination_token=destination_token,
                amount=amount
            )

        except (ChainNotFoundError, TokenNotFoundError):
            raise
        except Exception as e:
            logger.error(f"Failed to get quote: {e}")
            raise QuoteError(f"Failed to get quote: {str(e)}")

    def get_token_price(self, chain_name: str, token_symbol: str) -> PriceData:
        """
        Get current price of a token.

        Args:
            chain_name: Name of the chain
            token_symbol: Symbol of the token

        Returns:
            PriceData object with current price
        """
        try:
            chain = self.get_chain_by_name(chain_name)
            token = self.get_token(chain_name, token_symbol)

            params = {
                "chainId": chain.id,
                "address": token.address
            }

            response_data = self._make_request("GET", "/currencies/token/price", params=params)

            return PriceData(
                price=float(response_data.get("price", 0)),
                chain_id=chain.id,
                token_address=token.address,
                token_symbol=token_symbol
            )

        except Exception as e:
            logger.error(f"Failed to get token price: {e}")
            raise RelayAPIError(f"Failed to get token price: {str(e)}")

    def clear_cache(self):
        """Clear all cached data."""
        self._chains_cache.clear()
        self._chains_by_id.clear()
        self._tokens_cache.clear()
        self._cache_timestamps.clear()
        logger.info("Cache cleared")

    def get_cache_info(self) -> dict:
        """Get information about current cache state."""
        return {
            "chains_cached": len(self._chains_cache),
            "tokens_cached": {chain: len(tokens) for chain, tokens in self._tokens_cache.items()},
            "cache_timestamps": dict(self._cache_timestamps),
            "cache_ttl": self.cache_ttl
        }

"""
Relay Monitor 的命令行界面命令。
"""

import sys
import signal
import argparse
import threading
import time
from typing import Optional

from ..config.manager import ConfigManager
from ..storage.database import DataStorage
from ..monitor.engine import PriceMonitor
from ..alerts.system import AlertSystem
from ..maintenance.tasks import MaintenanceManager
from ..crypto.wallet import WalletManager
from ..trading.executor import TradingExecutor
from ..web.app import run_web_server
from ..utils.logging import setup_logging, get_logger
from ..utils.formatting import (
    print_header, print_success, print_error, print_warning, print_info,
    format_status, format_table, format_number, format_percentage, 
    format_currency, format_timestamp
)

logger = get_logger(__name__)

# Global variables for signal handling
monitor_instance: Optional[PriceMonitor] = None
web_thread: Optional[threading.Thread] = None


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    print_info("Received shutdown signal, stopping...")
    
    if monitor_instance and monitor_instance.is_running():
        monitor_instance.stop()
    
    sys.exit(0)


def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown."""
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def start_monitor(args):
    """Start the price monitoring system."""
    global monitor_instance
    
    try:
        print_header("Starting Relay Monitor")
        
        # Initialize storage first (needed for runtime config)
        # Use a temporary config to get database path
        temp_config_manager = ConfigManager(args.config)
        temp_config = temp_config_manager.get_config()
        storage = DataStorage(temp_config.database.path)

        # Load configuration with storage for runtime config support
        config_manager = ConfigManager(args.config, storage=storage)
        config = config_manager.get_config()

        # Setup logging
        setup_logging(config.logging, debug_mode=False)
        logger.info("Starting Relay Monitor")

        # Initialize alert system
        alert_system = AlertSystem(config.alerts, storage)

        # Create monitor
        monitor_instance = PriceMonitor(config_manager, storage=storage, alert_system=alert_system)
        
        # Setup signal handlers
        setup_signal_handlers()
        
        # Start monitoring
        monitor_instance.start()
        
        print_success("Price monitoring started successfully")
        # Note: Monitor pairs are now loaded from database
        print_info("Press Ctrl+C to stop")
        
        # Keep the main thread alive
        try:
            while monitor_instance.is_running():
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        
    except Exception as e:
        print_error(f"Failed to start monitoring: {e}")
        logger.error(f"Failed to start monitoring: {e}")
        return 1
    
    finally:
        if monitor_instance and monitor_instance.is_running():
            print_info("Stopping monitor...")
            monitor_instance.stop()
            print_success("Monitor stopped")
    
    return 0


def stop_monitor(args):
    """Stop the price monitoring system."""
    print_info("Stop command is only applicable when running as a service")
    print_info("Use Ctrl+C to stop the monitor when running in foreground")
    return 0


def status(args):
    """Show system status."""
    try:
        print_header("Relay Monitor Status")
        
        # Load configuration
        config_manager = ConfigManager(args.config)
        config = config_manager.get_config()
        
        # Initialize storage
        storage = DataStorage(config.database.path)
        
        # Get database stats
        db_stats = storage.get_database_stats()
        
        # Configuration info
        print("📋 Configuration:")
        print(f"   Config file: {config_manager.find_config_file()}")
        print(f"   Monitor pairs: {len(storage.get_enabled_monitor_pairs())}")
        print(f"   Database: {config.database.path}")
        print(f"   Web interface: {config.web.host}:{config.web.port}")
        
        # Database info
        print("\n💾 Database:")
        print(f"   Size: {db_stats['db_size_mb']} MB")
        print(f"   Price records: {db_stats['price_history_count']}")
        print(f"   Alert records: {db_stats['alert_history_count']}")
        print(f"   Chains: {db_stats['chains_count']}")
        print(f"   Tokens: {db_stats['tokens_count']}")
        
        # Monitor pairs info
        enabled_pairs = storage.get_enabled_monitor_pairs()
        if enabled_pairs:
            print("\n🔗 Monitor Pairs:")
            for pair in enabled_pairs:
                latest = storage.get_latest_price(pair.name)
                if latest:
                    print(f"   {pair.name}:")
                    print(f"     Latest price: {format_number(latest.exchange_rate, 6)}")
                    print(f"     Latest fee: {format_currency(latest.total_fee_usd)}")
                    print(f"     Last update: {format_timestamp(latest.timestamp)}")
                else:
                    print(f"   {pair.name}: No data")
        
        # Recent alerts
        recent_alerts = storage.get_alert_history(hours=24, limit=5)
        if recent_alerts:
            print("\n🚨 Recent Alerts (24h):")
            for alert in recent_alerts:
                print(f"   {alert.timestamp.strftime('%H:%M:%S')} - {alert.alert_type}: {alert.message}")
        else:
            print("\n🚨 No recent alerts")
        
        return 0
        
    except Exception as e:
        print_error(f"Failed to get status: {e}")
        return 1


def config_cmd(args):
    """Configuration management commands."""
    try:
        config_manager = ConfigManager(args.config)
        
        if args.action == 'show':
            print_header("Configuration")
            
            config_file = config_manager.find_config_file()
            print(f"📁 Config file: {config_file}")
            
            config = config_manager.get_config()

            # Initialize storage for database queries
            from ..storage.database import DataStorage
            storage = DataStorage(config.database.path)

            # Show key configuration
            print(f"\n🔗 API: {config.api.base_url}")
            print(f"⏱️  Monitoring interval: {config.monitoring.interval_seconds}s")
            print(f"📊 Price threshold: {config.monitoring.price_change_threshold_percent}%")
            print(f"💾 Database: {config.database.path}")
            print(f"🌐 Web: {config.web.host}:{config.web.port}")
            
            enabled_pairs = storage.get_enabled_monitor_pairs()
            print(f"\n📋 Monitor Pairs ({len(enabled_pairs)} enabled):")
            for pair in enabled_pairs:
                print(f"   • {pair.name}: {pair.origin_chain} {pair.origin_token} → {pair.destination_chain} {pair.destination_token}")
            
        elif args.action == 'validate':
            print_info("Validating configuration...")
            
            if config_manager.validate_config():
                print_success("Configuration is valid")
                return 0
            else:
                print_error("Configuration validation failed")
                return 1
        
        elif args.action == 'example':
            print_info("Creating example configuration...")
            config_manager.create_example_config("config/config.example.toml")
            print_success("Example configuration created at config/config.example.toml")
        
        return 0
        
    except Exception as e:
        print_error(f"Configuration error: {e}")
        return 1


def web_cmd(args):
    """Start the web interface."""
    global monitor_instance

    try:
        print_header("Starting Web Interface")

        # Load configuration
        config_manager = ConfigManager(args.config)
        config = config_manager.get_config()

        # Setup logging
        setup_logging(config.logging)

        # Initialize components
        storage = DataStorage(config.database.path)

        # Create alert system
        alert_system = AlertSystem(config.alerts, storage)

        # Create trading executor
        trading_executor = None
        try:
            from ..api.client import RelayAPIClient
            api_client = RelayAPIClient(
                base_url=config.api.base_url,
                timeout=config.api.timeout,
                retry_attempts=config.api.retry_attempts,
                cache_ttl=config.api.cache_ttl
            )
            wallet_manager = WalletManager(storage)
            trading_executor = TradingExecutor(api_client, wallet_manager, storage)
            print_info("Trading executor initialized")
        except Exception as e:
            print_warning(f"Failed to initialize trading executor: {e}")

        # Create monitor instance
        monitor = PriceMonitor(config_manager, storage=storage, alert_system=alert_system, trading_executor=trading_executor)
        monitor_instance = monitor

        # Prepare monitoring for web server
        # In debug mode, let the web server handle monitor startup to avoid duplicate instances
        if args.with_monitor and not (args.debug or config.web.debug):
            print_info("Starting price monitoring...")
            monitor.start()
            print_success("Price monitoring started")
        elif args.with_monitor:
            print_info("Debug mode: Monitor will be started by web server")

        host = args.host or config.web.host
        port = args.port or config.web.port
        debug = args.debug or config.web.debug

        # Re-setup logging with debug mode if needed
        # This ensures CLI debug flag overrides config
        if debug:
            setup_logging(config.logging, debug_mode=True)

        print_success(f"Starting web server on http://{host}:{port}")
        if not args.with_monitor:
            print_warning("Monitor is not running. Use --with-monitor to start monitoring.")
        print_info("Press Ctrl+C to stop")

        # Setup signal handlers
        setup_signal_handlers()

        # Start web server
        run_web_server(host, port, debug, config_manager, storage, monitor, alert_system, trading_executor)

        return 0

    except Exception as e:
        print_error(f"Failed to start web interface: {e}")
        return 1


def check_pair(args):
    """Manually check price for a specific pair."""
    try:
        print_header(f"Checking Price for {args.pair}")
        
        # Load configuration
        config_manager = ConfigManager(args.config)
        config = config_manager.get_config()
        
        # Setup logging
        setup_logging(config.logging)
        
        # Initialize components
        storage = DataStorage(config.database.path)
        monitor = PriceMonitor(config_manager, storage=storage)
        
        # Check if pair exists
        enabled_pairs = config.get_enabled_pairs()
        pair_names = [p.name for p in enabled_pairs]
        
        if args.pair not in pair_names:
            print_error(f"Pair '{args.pair}' not found")
            print_info(f"Available pairs: {', '.join(pair_names)}")
            return 1
        
        # Perform check
        print_info(f"Checking price for {args.pair}...")
        result = monitor.check_pair_now(args.pair)
        
        if result['success']:
            print_success("Price check completed")
            print(f"   Latest price: {format_number(result['latest_price'], 6)}")
            print(f"   Latest fee: {format_currency(result['latest_fee'])}")
            print(f"   Timestamp: {result['timestamp']}")
        else:
            print_error(f"Price check failed: {result['error']}")
            return 1
        
        return 0
        
    except Exception as e:
        print_error(f"Failed to check pair: {e}")
        return 1


def maintenance_cmd(args):
    """Handle maintenance operations."""
    try:
        print_header(f"Maintenance: {args.action.title()}")

        # Load configuration
        config_manager = ConfigManager(args.config)
        config = config_manager.get_config()

        # Setup logging
        setup_logging(config.logging)

        # Initialize components
        storage = DataStorage(config.database.path)
        maintenance_manager = MaintenanceManager(config, storage)

        if args.action == 'cleanup':
            print_info("Running cleanup operations...")

            # Database cleanup
            if config.database.cleanup_enabled:
                print_info("Cleaning up database...")
                result = maintenance_manager.run_database_cleanup()
                if result['success']:
                    print_success(f"Database cleanup completed:")
                    print(f"   Space saved: {result.get('space_saved_mb', 0):.2f} MB")
                    print(f"   Max history: {result.get('max_history_days', 0)} days")
                else:
                    print_error(f"Database cleanup failed: {result.get('error', 'Unknown error')}")
            else:
                print_warning("Database cleanup is disabled")

            # Log cleanup
            if config.logging.cleanup_enabled:
                print_info("Cleaning up log files...")
                result = maintenance_manager.run_log_cleanup()
                if result['success']:
                    print_success(f"Log cleanup completed:")
                    print(f"   Files removed: {result.get('files_removed', 0)}")
                    print(f"   Space freed: {result.get('total_size_cleaned_mb', 0):.2f} MB")
                else:
                    print_error(f"Log cleanup failed: {result.get('error', 'Unknown error')}")
            else:
                print_warning("Log cleanup is disabled")



        elif args.action == 'status':
            print_info("Maintenance Status:")
            print(f"   Database cleanup: {'Enabled' if config.database.cleanup_enabled else 'Disabled'}")
            print(f"   Log cleanup: {'Enabled' if config.logging.cleanup_enabled else 'Disabled'}")
            print()

            # Check if tasks should run
            print_info("Next scheduled runs:")
            if config.database.cleanup_enabled:
                should_cleanup = maintenance_manager.should_run_database_cleanup()
                print(f"   Database cleanup: {'Due now' if should_cleanup else 'Not due'}")

            if config.logging.cleanup_enabled:
                should_log_cleanup = maintenance_manager.should_run_log_cleanup()
                print(f"   Log cleanup: {'Due now' if should_log_cleanup else 'Not due'}")

        elif args.action == 'stats':
            stats = maintenance_manager.get_maintenance_stats()
            print_info("Maintenance Statistics:")
            print(f"   Database cleanups performed: {stats.get('database_cleanups_performed', 0)}")
            print(f"   Log cleanups performed: {stats.get('log_cleanups_performed', 0)}")
            print(f"   Total data cleaned: {stats.get('total_data_cleaned_mb', 0):.2f} MB")
            print()

            # Show last run times
            print_info("Last maintenance runs:")
            last_db_cleanup = stats.get('last_database_cleanup')
            print(f"   Database cleanup: {format_timestamp(last_db_cleanup) if last_db_cleanup else 'Never'}")

            last_log_cleanup = stats.get('last_log_cleanup')
            print(f"   Log cleanup: {format_timestamp(last_log_cleanup) if last_log_cleanup else 'Never'}")

        return 0

    except Exception as e:
        print_error(f"Maintenance operation failed: {e}")
        return 1


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Relay Monitor - Cross-chain bridge price monitoring system",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  relay-monitor start                    # Start monitoring
  relay-monitor status                   # Show system status
  relay-monitor web                      # Start web interface
  relay-monitor config show             # Show configuration
  relay-monitor check ARB_ETH_to_POL    # Check specific pair
        """
    )
    
    parser.add_argument(
        '--config', '-c',
        help='Configuration file path',
        default=None
    )
    
    parser.add_argument(
        '--version', '-v',
        action='version',
        version='Relay Monitor 0.1.0'
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Start command
    start_parser = subparsers.add_parser('start', help='Start price monitoring')
    start_parser.set_defaults(func=start_monitor)
    
    # Stop command
    stop_parser = subparsers.add_parser('stop', help='Stop price monitoring')
    stop_parser.set_defaults(func=stop_monitor)
    
    # Status command
    status_parser = subparsers.add_parser('status', help='Show system status')
    status_parser.set_defaults(func=status)
    
    # Config command
    config_parser = subparsers.add_parser('config', help='Configuration management')
    config_parser.add_argument(
        'action',
        choices=['show', 'validate', 'example'],
        help='Configuration action'
    )
    config_parser.set_defaults(func=config_cmd)
    
    # Web command
    web_parser = subparsers.add_parser('web', help='Start web interface')
    web_parser.add_argument('--host', help='Host to bind to')
    web_parser.add_argument('--port', type=int, help='Port to bind to')
    web_parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    web_parser.add_argument('--with-monitor', action='store_true', help='Start monitoring along with web interface')
    web_parser.set_defaults(func=web_cmd)
    
    # Check command
    check_parser = subparsers.add_parser('check', help='Check price for a specific pair')
    check_parser.add_argument('pair', help='Pair name to check')
    check_parser.set_defaults(func=check_pair)

    # Maintenance command
    maintenance_parser = subparsers.add_parser('maintenance', help='Maintenance operations')
    maintenance_parser.add_argument(
        'action',
        choices=['cleanup', 'status', 'stats'],
        help='Maintenance action'
    )
    maintenance_parser.set_defaults(func=maintenance_cmd)
    
    # Parse arguments
    args = parser.parse_args()
    
    if not hasattr(args, 'func'):
        parser.print_help()
        return 1
    
    # Execute command
    try:
        return args.func(args)
    except KeyboardInterrupt:
        print_info("\nOperation cancelled by user")
        return 1
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())

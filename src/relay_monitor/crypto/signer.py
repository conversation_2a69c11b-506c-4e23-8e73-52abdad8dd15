"""
交易签名器。

提供区块链交易的构建和签名功能。
"""

import logging
from typing import Dict, Any, Optional
from eth_account import Account
from eth_account.signers.local import LocalAccount
from web3 import Web3
from web3.types import TxParams

from .wallet import WalletManager
from .exceptions import SigningError, WalletNotFoundError, TransactionError


logger = logging.getLogger(__name__)


class TransactionSigner:
    """
    交易签名器。
    
    提供区块链交易的构建、签名和提交功能。
    """
    
    def __init__(self, wallet_manager: WalletManager):
        """
        初始化交易签名器。
        
        Args:
            wallet_manager: 钱包管理器实例
        """
        self.wallet_manager = wallet_manager
        self.logger = logger
        
        # Web3 实例缓存
        self._web3_instances: Dict[int, Web3] = {}
    
    def sign_transaction(
        self,
        wallet_name: str,
        transaction_data: Dict[str, Any],
        chain_id: Optional[int] = None
    ) -> str:
        """
        签名交易。
        
        Args:
            wallet_name: 钱包名称
            transaction_data: 交易数据，包含 to, data, value, gas 等字段
            master_password: 主密码
            chain_id: 链ID（可选，如果交易数据中没有）
            
        Returns:
            签名后的交易哈希（十六进制字符串）
            
        Raises:
            WalletNotFoundError: 钱包不存在
            SigningError: 签名失败
            TransactionError: 交易数据无效
        """
        try:
            # 获取私钥
            private_key = self.wallet_manager.get_private_key(wallet_name)
            
            # 创建账户对象
            account = Account.from_key(private_key)
            
            # 构建交易参数
            tx_params = self._build_transaction_params(transaction_data, chain_id)
            
            # 验证交易参数
            self._validate_transaction_params(tx_params)
            
            # 签名交易
            signed_txn = account.sign_transaction(tx_params)
            
            self.logger.info(f"交易签名成功，钱包: {wallet_name}, 哈希: {signed_txn.hash.hex()}")
            
            # 兼容不同版本的 eth_account
            raw_tx = getattr(signed_txn, 'rawTransaction', None) or getattr(signed_txn, 'raw_transaction', None)
            if raw_tx is None:
                raise SigningError("无法获取签名后的原始交易数据")
            return raw_tx.hex()
            
        except (WalletNotFoundError, SigningError, TransactionError):
            raise
        except Exception as e:
            self.logger.error(f"交易签名失败: {e}")
            raise SigningError(f"交易签名失败: {str(e)}")
    
    def sign_and_submit_transaction(
        self,
        wallet_name: str,
        transaction_data: Dict[str, Any],
        rpc_url: str,
        chain_id: Optional[int] = None
    ) -> str:
        """
        签名并提交交易。
        
        Args:
            wallet_name: 钱包名称
            transaction_data: 交易数据
            master_password: 主密码
            rpc_url: RPC 节点 URL
            chain_id: 链ID
            
        Returns:
            交易哈希
            
        Raises:
            SigningError: 签名或提交失败
        """
        try:
            # 获取 Web3 实例
            web3 = self._get_web3_instance(rpc_url, chain_id)
            
            # 获取私钥和账户
            private_key = self.wallet_manager.get_private_key(wallet_name)
            account = Account.from_key(private_key)
            
            # 构建完整的交易参数
            tx_params = self._build_complete_transaction_params(
                transaction_data, web3, account.address, chain_id
            )
            
            # 签名交易
            signed_txn = account.sign_transaction(tx_params)
            
            # 提交交易
            # 兼容不同版本的 eth_account
            raw_tx = getattr(signed_txn, 'rawTransaction', None) or getattr(signed_txn, 'raw_transaction', None)
            if raw_tx is None:
                raise SigningError("无法获取签名后的原始交易数据")
            tx_hash = web3.eth.send_raw_transaction(raw_tx)
            
            self.logger.info(f"交易提交成功，哈希: {tx_hash.hex()}")
            
            return tx_hash.hex()
            
        except Exception as e:
            self.logger.error(f"签名并提交交易失败: {e}")
            raise SigningError(f"签名并提交交易失败: {str(e)}")
    
    def estimate_gas(
        self,
        transaction_data: Dict[str, Any],
        rpc_url: str,
        from_address: str,
        chain_id: Optional[int] = None
    ) -> int:
        """
        估算交易的 Gas 费用。
        
        Args:
            transaction_data: 交易数据
            rpc_url: RPC 节点 URL
            from_address: 发送方地址
            chain_id: 链ID
            
        Returns:
            估算的 Gas 数量
        """
        try:
            web3 = self._get_web3_instance(rpc_url, chain_id)
            
            # 构建估算参数
            estimate_params = {
                'from': from_address,
                'to': transaction_data.get('to'),
                'data': transaction_data.get('data', '0x'),
                'value': int(transaction_data.get('value', 0))
            }
            
            # 估算 Gas
            gas_estimate = web3.eth.estimate_gas(estimate_params)
            
            self.logger.debug(f"Gas 估算结果: {gas_estimate}")
            
            return gas_estimate
            
        except Exception as e:
            self.logger.error(f"Gas 估算失败: {e}")
            raise TransactionError(f"Gas 估算失败: {str(e)}")
    
    def get_transaction_status(self, tx_hash: str, rpc_url: str) -> Dict[str, Any]:
        """
        获取交易状态。
        
        Args:
            tx_hash: 交易哈希
            rpc_url: RPC 节点 URL
            
        Returns:
            交易状态信息
        """
        try:
            web3 = self._get_web3_instance(rpc_url)
            
            # 获取交易信息
            try:
                tx = web3.eth.get_transaction(tx_hash)
                tx_receipt = web3.eth.get_transaction_receipt(tx_hash)
                
                status = {
                    'hash': tx_hash,
                    'status': 'success' if tx_receipt.status == 1 else 'failed',
                    'block_number': tx_receipt.blockNumber,
                    'gas_used': tx_receipt.gasUsed,
                    'effective_gas_price': getattr(tx_receipt, 'effectiveGasPrice', None),
                    'confirmations': web3.eth.block_number - tx_receipt.blockNumber + 1
                }
                
            except Exception:
                # 交易可能还在 pending 状态
                status = {
                    'hash': tx_hash,
                    'status': 'pending',
                    'block_number': None,
                    'gas_used': None,
                    'effective_gas_price': None,
                    'confirmations': 0
                }
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取交易状态失败: {e}")
            raise TransactionError(f"获取交易状态失败: {str(e)}")
    
    def _build_transaction_params(
        self,
        transaction_data: Dict[str, Any],
        chain_id: Optional[int] = None
    ) -> TxParams:
        """构建交易参数。"""
        # 确保 'to' 地址格式正确
        to_address = transaction_data.get('to')
        if to_address and not to_address.startswith('0x'):
            to_address = f"0x{to_address}"

        # 构建基础交易参数
        tx_params = TxParams({
            'chainId': chain_id or transaction_data.get('chainId')
        })

        # 添加 'to' 地址（如果存在）
        if to_address:
            tx_params['to'] = Web3.to_checksum_address(to_address)

        # 添加数据字段
        data = transaction_data.get('data', '0x')
        if data and data != '0x':
            tx_params['data'] = data

        # 添加价值字段
        value = transaction_data.get('value', 0)
        if value:
            tx_params['value'] = int(value)

        # 添加gas限制
        gas = transaction_data.get('gas', transaction_data.get('gasLimit', 21000))
        tx_params['gas'] = int(gas)
        
        # 添加 Gas 价格相关参数
        if 'maxFeePerGas' in transaction_data:
            tx_params['maxFeePerGas'] = int(transaction_data['maxFeePerGas'])
        if 'maxPriorityFeePerGas' in transaction_data:
            tx_params['maxPriorityFeePerGas'] = int(transaction_data['maxPriorityFeePerGas'])
        if 'gasPrice' in transaction_data:
            tx_params['gasPrice'] = int(transaction_data['gasPrice'])
        
        # 添加 nonce（如果提供）
        if 'nonce' in transaction_data:
            tx_params['nonce'] = int(transaction_data['nonce'])
        
        return tx_params
    
    def _build_complete_transaction_params(
        self,
        transaction_data: Dict[str, Any],
        web3: Web3,
        from_address: str,
        chain_id: Optional[int] = None
    ) -> TxParams:
        """构建完整的交易参数，包括自动获取 nonce 和 gas price。"""
        tx_params = self._build_transaction_params(transaction_data, chain_id)
        
        # 自动获取 nonce
        if 'nonce' not in tx_params:
            tx_params['nonce'] = web3.eth.get_transaction_count(from_address)
        
        # 自动获取 gas price（如果没有提供 EIP-1559 参数）
        if 'maxFeePerGas' not in tx_params and 'gasPrice' not in tx_params:
            try:
                # 尝试使用 EIP-1559
                latest_block = web3.eth.get_block('latest')
                if hasattr(latest_block, 'baseFeePerGas') and latest_block.baseFeePerGas:
                    base_fee = latest_block.baseFeePerGas
                    max_priority_fee = web3.eth.max_priority_fee
                    tx_params['maxFeePerGas'] = base_fee * 2 + max_priority_fee
                    tx_params['maxPriorityFeePerGas'] = max_priority_fee
                else:
                    # 回退到传统 gas price
                    tx_params['gasPrice'] = web3.eth.gas_price
            except Exception:
                # 如果获取失败，使用默认值
                tx_params['gasPrice'] = web3.eth.gas_price
        
        return tx_params
    
    def _validate_transaction_params(self, tx_params: TxParams) -> None:
        """验证交易参数。"""
        required_fields = ['to', 'chainId']
        
        for field in required_fields:
            if field not in tx_params or tx_params[field] is None:
                raise TransactionError(f"缺少必需的交易参数: {field}")
        
        # 验证地址格式
        if not Web3.is_address(tx_params['to']):
            raise TransactionError(f"无效的目标地址: {tx_params['to']}")
        
        # 验证数值参数
        numeric_fields = ['value', 'gas', 'chainId']
        for field in numeric_fields:
            if field in tx_params:
                try:
                    int(tx_params[field])
                except (ValueError, TypeError):
                    raise TransactionError(f"无效的数值参数 {field}: {tx_params[field]}")
    
    def _get_web3_instance(self, rpc_url: str, chain_id: Optional[int] = None) -> Web3:
        """获取 Web3 实例（带缓存）。"""
        cache_key = chain_id or hash(rpc_url)
        
        if cache_key not in self._web3_instances:
            web3 = Web3(Web3.HTTPProvider(rpc_url))
            
            # 验证连接
            if not web3.is_connected():
                raise TransactionError(f"无法连接到 RPC 节点: {rpc_url}")
            
            self._web3_instances[cache_key] = web3
        
        return self._web3_instances[cache_key]

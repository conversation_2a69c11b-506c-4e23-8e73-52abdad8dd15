"""
加密和解密工具类。

提供私钥的安全加密存储和解密功能。
"""

import os
import json
import hashlib
import logging
from typing import Dict, Any, Optional
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography.hazmat.backends import default_backend

from .exceptions import EncryptionError, InvalidPasswordError


logger = logging.getLogger(__name__)


class CryptoManager:
    """
    加密管理器，提供私钥的安全加密存储功能。
    
    使用 PBKDF2 + AES-256-GCM 进行加密：
    - PBKDF2: 密码派生，防止彩虹表攻击
    - AES-256-GCM: 认证加密，提供机密性和完整性
    """
    
    # 加密参数
    SALT_LENGTH = 32  # 盐值长度
    NONCE_LENGTH = 12  # GCM 模式的 nonce 长度
    KEY_LENGTH = 32   # AES-256 密钥长度
    PBKDF2_ITERATIONS = 100000  # PBKDF2 迭代次数
    
    def __init__(self):
        self.backend = default_backend()
    
    def encrypt_private_key(self, private_key: str, master_password: str) -> Dict[str, Any]:
        """
        加密私钥。
        
        Args:
            private_key: 要加密的私钥（十六进制字符串）
            master_password: 主密码
            
        Returns:
            包含加密数据的字典：{
                'encrypted_data': str,  # base64 编码的加密数据
                'salt': str,           # base64 编码的盐值
                'nonce': str,          # base64 编码的 nonce
                'algorithm': str       # 加密算法标识
            }
            
        Raises:
            EncryptionError: 加密失败
        """
        try:
            # 生成随机盐值和 nonce
            salt = os.urandom(self.SALT_LENGTH)
            nonce = os.urandom(self.NONCE_LENGTH)
            
            # 使用 PBKDF2 派生密钥
            key = self._derive_key(master_password, salt)
            
            # 使用 AES-GCM 加密
            aesgcm = AESGCM(key)
            encrypted_data = aesgcm.encrypt(nonce, private_key.encode('utf-8'), None)
            
            # 安全清除密钥
            self._secure_delete(key)
            
            # 返回加密结果
            result = {
                'encrypted_data': self._bytes_to_base64(encrypted_data),
                'salt': self._bytes_to_base64(salt),
                'nonce': self._bytes_to_base64(nonce),
                'algorithm': 'AES-256-GCM-PBKDF2'
            }
            
            logger.debug("私钥加密成功")
            return result
            
        except Exception as e:
            logger.error(f"私钥加密失败: {e}")
            raise EncryptionError(f"私钥加密失败: {str(e)}")
    
    def decrypt_private_key(self, encrypted_data: Dict[str, Any], master_password: str) -> str:
        """
        解密私钥。
        
        Args:
            encrypted_data: 加密数据字典
            master_password: 主密码
            
        Returns:
            解密后的私钥字符串
            
        Raises:
            InvalidPasswordError: 密码错误
            EncryptionError: 解密失败
        """
        try:
            # 验证算法
            if encrypted_data.get('algorithm') != 'AES-256-GCM-PBKDF2':
                raise EncryptionError("不支持的加密算法")
            
            # 解码数据
            encrypted_bytes = self._base64_to_bytes(encrypted_data['encrypted_data'])
            salt = self._base64_to_bytes(encrypted_data['salt'])
            nonce = self._base64_to_bytes(encrypted_data['nonce'])
            
            # 派生密钥
            key = self._derive_key(master_password, salt)
            
            # 解密
            aesgcm = AESGCM(key)
            decrypted_data = aesgcm.decrypt(nonce, encrypted_bytes, None)
            
            # 安全清除密钥
            self._secure_delete(key)
            
            private_key = decrypted_data.decode('utf-8')
            logger.debug("私钥解密成功")
            return private_key
            
        except Exception as e:
            if "authentication" in str(e).lower() or "decrypt" in str(e).lower():
                raise InvalidPasswordError("主密码错误或数据已损坏")
            logger.error(f"私钥解密失败: {e}")
            raise EncryptionError(f"私钥解密失败: {str(e)}")
    
    def _derive_key(self, password: str, salt: bytes) -> bytes:
        """使用 PBKDF2 派生密钥。"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=self.KEY_LENGTH,
            salt=salt,
            iterations=self.PBKDF2_ITERATIONS,
            backend=self.backend
        )
        return kdf.derive(password.encode('utf-8'))
    
    def _bytes_to_base64(self, data: bytes) -> str:
        """将字节数据转换为 base64 字符串。"""
        import base64
        return base64.b64encode(data).decode('ascii')
    
    def _base64_to_bytes(self, data: str) -> bytes:
        """将 base64 字符串转换为字节数据。"""
        import base64
        return base64.b64decode(data.encode('ascii'))
    
    def _secure_delete(self, data: bytes) -> None:
        """安全清除内存中的敏感数据。"""
        # 用随机数据覆盖内存
        if data:
            # Python 中字节对象是不可变的，这里主要是为了代码完整性
            # 实际的内存清理由 Python 垃圾回收器处理
            pass
    
    def verify_password(self, password: str, encrypted_data: Dict[str, Any]) -> bool:
        """
        验证主密码是否正确。
        
        Args:
            password: 要验证的密码
            encrypted_data: 加密数据
            
        Returns:
            密码是否正确
        """
        try:
            # 尝试解密一个测试字符串
            test_data = self.encrypt_private_key("test", password)
            self.decrypt_private_key(test_data, password)
            return True
        except (InvalidPasswordError, EncryptionError):
            return False
    
    def generate_password_hash(self, password: str) -> str:
        """
        生成密码哈希值，用于验证。
        
        Args:
            password: 密码
            
        Returns:
            密码哈希值
        """
        salt = os.urandom(self.SALT_LENGTH)
        key = self._derive_key(password, salt)
        
        # 组合盐值和哈希值
        hash_data = {
            'hash': self._bytes_to_base64(key),
            'salt': self._bytes_to_base64(salt),
            'algorithm': 'PBKDF2-SHA256'
        }
        
        self._secure_delete(key)
        return json.dumps(hash_data)
    
    def verify_password_hash(self, password: str, password_hash: str) -> bool:
        """
        验证密码哈希值。
        
        Args:
            password: 密码
            password_hash: 密码哈希值
            
        Returns:
            密码是否正确
        """
        try:
            hash_data = json.loads(password_hash)
            
            if hash_data.get('algorithm') != 'PBKDF2-SHA256':
                return False
            
            salt = self._base64_to_bytes(hash_data['salt'])
            expected_hash = self._base64_to_bytes(hash_data['hash'])
            
            # 计算密码哈希
            actual_key = self._derive_key(password, salt)
            
            # 比较哈希值
            result = actual_key == expected_hash
            
            # 安全清除
            self._secure_delete(actual_key)
            
            return result
            
        except (json.JSONDecodeError, KeyError, Exception):
            return False

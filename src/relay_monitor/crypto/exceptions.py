"""
加密模块的自定义异常类。
"""


class CryptoError(Exception):
    """加密相关错误的基础异常类。"""
    pass


class WalletError(CryptoError):
    """钱包操作相关错误。"""
    pass


class SigningError(CryptoError):
    """交易签名相关错误。"""
    pass


class InvalidPasswordError(CryptoError):
    """无效密码错误。"""
    pass


class WalletNotFoundError(WalletError):
    """钱包未找到错误。"""
    
    def __init__(self, wallet_name: str):
        super().__init__(f"钱包 '{wallet_name}' 未找到")
        self.wallet_name = wallet_name


class PrivateKeyError(CryptoError):
    """私钥相关错误。"""
    pass


class EncryptionError(CryptoError):
    """加密/解密错误。"""
    pass


class TransactionError(CryptoError):
    """交易构建或提交错误。"""
    pass

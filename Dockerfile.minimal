# Relay Monitor - Minimal Docker Image
# Ultra-optimized build using distroless base

# Build stage
FROM python:3.13.5-alpine as builder

# Install build dependencies
RUN apk add --no-cache \
    gcc \
    musl-dev \
    linux-headers

# Set working directory
WORKDIR /app

# Copy production requirements only
COPY requirements-prod.txt .

# Install dependencies to local directory
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir --target /app/packages -r requirements-prod.txt && \
    find /app/packages -name "*.pyc" -delete && \
    find /app/packages -name "__pycache__" -type d -exec rm -rf {} + || true && \
    find /app/packages -name "*.pyo" -delete || true

# Copy application source
COPY src/ ./src/
COPY main.py ./

# Compile Python files
RUN python -m compileall -b src/ && \
    find src/ -name "*.py" -delete && \
    find src/ -name "*.pyc" -exec mv {} {}.bak \; && \
    find src/ -name "*.pyc.bak" -exec sh -c 'mv "$1" "${1%.bak}"' _ {} \;

# Production stage - Distroless
FROM gcr.io/distroless/python3-debian12:latest as production

# Set environment variables
ENV PYTHONPATH=/app/src:/app/packages \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

# Copy packages and compiled application
COPY --from=builder /app/packages /app/packages
COPY --from=builder /app/src /app/src
COPY --from=builder /app/main.py /app/

# Copy configuration
COPY config/config.example.toml /app/config/config.example.toml

# Create directories (distroless doesn't have mkdir)
# We'll create them in the entrypoint

# Expose port
EXPOSE 5000

# No health check in distroless (no curl)
# Health checks should be handled by orchestrator

# Default command
WORKDIR /app
CMD ["python", "main.py", "web", "--host", "0.0.0.0", "--port", "5000"]

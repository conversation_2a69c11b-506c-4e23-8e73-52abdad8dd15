# Development override for docker-compose.yml
# Docker Compose automatically loads this file when present
# Usage: docker compose up -d

services:
  relay-monitor:
    # Override build for development
    build:
      context: .
      dockerfile: Dockerfile.optimized  # Use optimized dockerfile for consistency
      target: production
    
    # Development environment variables
    environment:
      # 时区设置
      - TZ=Asia/Shanghai

      # 开发环境配置
      - FLASK_ENV=development
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      - PYTHONUNBUFFERED=1

      # SOCKS5 代理配置（用于访问 Relay API）
      - HTTP_PROXY=socks5://host.docker.internal:30006
      - HTTPS_PROXY=socks5://host.docker.internal:30006
      - NO_PROXY=localhost,127.0.0.1
    
    # Development port (different from production)
    ports:
      - "${DEV_HOST_PORT:-5001}:5000"
    
    # Development volumes (mount source code for live reload)
    volumes:
      # Mount source code for development
      - ./src:/app/src:ro
      - ./config:/app/config:rw
      - ./main.py:/app/main.py:ro
      # Data persistence
      - ./docker-data/dev-data:/app/data
      - ./docker-data/dev-logs:/app/logs
    
    # Override command for development
    command: ["web", "--with-monitor", "--host", "0.0.0.0", "--port", "5000", "--debug"]
    
    # Development resource limits (more generous)
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    
    # Development health check (more frequent)
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 20s

# Development volumes
volumes:
  dev_data:
    driver: local
  dev_logs:
    driver: local

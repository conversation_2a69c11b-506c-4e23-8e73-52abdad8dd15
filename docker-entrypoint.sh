#!/bin/sh
set -e

# Relay Monitor Docker Entrypoint Script

# Set timezone
if [ -n "$TZ" ]; then
    echo "Setting timezone to $TZ"
    # For read-only filesystem, we rely on TZ environment variable
    # which is already set in docker-compose.yml
    export TZ="$TZ"
    echo "Timezone set via TZ environment variable: $TZ"
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" >&2
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

# Function to wait for dependencies
wait_for_dependencies() {
    log "Checking dependencies..."
    # Add any dependency checks here (e.g., database, external APIs)
    success "Dependencies check completed"
}

# Function to initialize configuration
init_config() {
    log "Initializing configuration..."
    
    # Check if config file exists
    if [ ! -f "/app/config/config.toml" ]; then
        if [ -f "/app/config/config.example.toml" ]; then
            log "Creating config.toml from example..."
            cp /app/config/config.example.toml /app/config/config.toml
        else
            error "No configuration file found!"
            exit 1
        fi
    fi
    
    # Override configuration with environment variables if provided
    if [ -n "$ADMIN_PASSWORD" ]; then
        log "Updating admin password from environment variable"
        # Note: In production, use a proper configuration management tool
    fi
    
    if [ -n "$SMTP_API_KEY" ]; then
        log "Updating SMTP API key from environment variable"
    fi
    
    if [ -n "$BARK_KEYS" ]; then
        log "Updating Bark keys from environment variable"
    fi
    
    success "Configuration initialized"
}

# Function to initialize database
init_database() {
    log "Initializing database..."
    
    # Create data directory if it doesn't exist
    mkdir -p /app/data
    
    # Initialize database if needed
    if [ ! -f "/app/data/relay_monitor.db" ]; then
        log "Creating new database..."
        python main.py config validate || {
            error "Configuration validation failed"
            exit 1
        }
    fi
    
    success "Database initialized"
}

# Function to run health check
health_check() {
    log "Running health check..."
    
    # Basic configuration validation
    python main.py config validate || {
        error "Configuration validation failed"
        return 1
    }
    
    success "Health check passed"
}

# Main execution
main() {
    log "Starting Relay Monitor..."
    log "Version: ${VERSION:-unknown}"
    log "Build Date: ${BUILD_DATE:-unknown}"
    
    # Wait for dependencies
    wait_for_dependencies
    
    # Initialize configuration
    init_config
    
    # Initialize database
    init_database
    
    # Run health check
    health_check
    
    # Handle special commands
    case "$1" in
        "health")
            health_check
            exit $?
            ;;
        "config")
            shift
            exec python main.py config "$@"
            ;;
        "monitor")
            shift
            log "Starting monitor mode..."
            exec python main.py monitor "$@"
            ;;
        "web")
            shift
            log "Starting web server..."
            exec python main.py web "$@"
            ;;
        "bash"|"sh")
            log "Starting interactive shell..."
            exec /bin/bash
            ;;
        *)
            log "Starting with custom command: $*"
            exec "$@"
            ;;
    esac
}

# Trap signals for graceful shutdown
trap 'log "Received shutdown signal, stopping..."; exit 0' TERM INT

# Run main function
main "$@"

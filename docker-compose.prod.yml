# Relay Monitor - 生产部署配置（本地构建）
# 项目名称（避免警告）
name: relay-monitor

services:
  relay-monitor:
    build:
      context: .
      dockerfile: Dockerfile.optimized
      args:
        BUILD_DATE: ${BUILD_DATE:-}
        VERSION: ${VERSION:-optimized}
        VCS_REF: ${VCS_REF:-}
    image: relay-monitor:optimized
    container_name: relay-monitor-prod
    restart: unless-stopped
    
    # Port mapping
    ports:
      - "${HOST_PORT:-5001}:5000"
    
    # Environment variables for production
    environment:
      - PYTHONUNBUFFERED=1
      - FLASK_ENV=production
      - RELAY_MONITOR_CONFIG_PATH=/app/config/config.toml
      # Timezone configuration
      - TZ=Asia/Shanghai
      # Production environment variables
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-}
      - SMTP_API_KEY=${SMTP_API_KEY:-}
      - BARK_KEYS=${BARK_KEYS:-}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEBUG=false
    
    # Volume mounts for data persistence
    volumes:
      # Configuration
      - ./config:/app/config:rw
      # Data persistence
      - relay_monitor_data:/app/data
      # Logs
      - relay_monitor_logs:/app/logs
      # Timezone data (writable for timezone setup)
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Production resource limits
    deploy:
      resources:
        limits:
          memory: 256M  # Reduced due to optimized image
          cpus: '0.5'
        reservations:
          memory: 128M  # Reduced due to optimized image
          cpus: '0.25'
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # Security (additional production settings)
    read_only: true
    tmpfs:
      - /tmp
      - /var/tmp
    
    # Network
    networks:
      - relay-monitor-network

# Named volumes for data persistence
volumes:
  relay_monitor_data:
    driver: local

  relay_monitor_logs:
    driver: local

# Networks
networks:
  relay-monitor-network:
    driver: bridge

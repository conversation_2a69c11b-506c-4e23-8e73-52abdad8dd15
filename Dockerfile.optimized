# Relay Monitor - Optimized Docker Image
# Multi-stage build for minimal production image

# Build stage
FROM python:3.13.5-alpine AS builder

# Set build arguments
ARG BUILD_DATE
ARG VERSION=latest
ARG VCS_REF

# Install build dependencies
RUN apk add --no-cache \
    gcc \
    musl-dev \
    linux-headers \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Copy production requirements only
COPY requirements-prod.txt .

# Create virtual environment and install dependencies
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Install Python dependencies with optimizations
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements-prod.txt && \
    find /opt/venv -name "*.pyc" -delete && \
    find /opt/venv -name "__pycache__" -type d -exec rm -rf {} + || true

# Production stage - Ultra minimal
FROM python:3.13.5-alpine AS production

# Add metadata
LABEL maintainer="Relay Monitor Team" \
      org.label-schema.build-date=$BUILD_DATE \
      org.label-schema.name="relay-monitor" \
      org.label-schema.description="Cross-chain Bridge Price Monitoring System" \
      org.label-schema.version=$VERSION \
      org.label-schema.vcs-ref=$VCS_REF \
      org.label-schema.schema-version="1.0"

# Install only essential runtime dependencies
RUN apk add --no-cache \
    curl \
    ca-certificates \
    tzdata \
    && rm -rf /var/cache/apk/*

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Create non-root user
RUN addgroup -g 1000 relaymonitor && \
    adduser -D -u 1000 -G relaymonitor -h /app -s /bin/sh relaymonitor

# Set working directory
WORKDIR /app

# Create necessary directories
RUN mkdir -p data logs config && \
    chown -R relaymonitor:relaymonitor /app

# Copy only essential application files
COPY --chown=relaymonitor:relaymonitor src/ ./src/
COPY --chown=relaymonitor:relaymonitor main.py ./
COPY --chown=relaymonitor:relaymonitor config/config.example.toml ./config/
COPY --chown=relaymonitor:relaymonitor docker-entrypoint.sh /usr/local/bin/

# Make entrypoint executable
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Switch to non-root user
USER relaymonitor

# Expose port
EXPOSE 5000

# Health check with minimal overhead
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Set environment variables
ENV PYTHONPATH=/app/src \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    FLASK_ENV=production \
    RELAY_MONITOR_CONFIG_PATH=/app/config/config.toml

# Default command
ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
CMD ["web", "--with-monitor", "--host", "0.0.0.0", "--port", "5000"]

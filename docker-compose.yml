# Relay Monitor - 开发/本地部署配置
# 项目名称（避免警告）
name: relay-monitor

services:
  relay-monitor:
    build:
      context: .
      dockerfile: ${DOCKERFILE:-Dockerfile.optimized}
      args:
        BUILD_DATE: ${BUILD_DATE:-}
        VERSION: ${VERSION:-latest}
        VCS_REF: ${VCS_REF:-}
    image: relay-monitor:${VERSION:-optimized}
    container_name: relay-monitor
    restart: unless-stopped
    
    # Port mapping
    ports:
      - "${HOST_PORT:-5001}:5000"
    
    # Environment variables
    environment:
      # 时区设置
      - TZ=Asia/Shanghai

      # 应用配置
      - PYTHONUNBUFFERED=1
      - FLASK_ENV=production
      - RELAY_MONITOR_CONFIG_PATH=/app/config/config.toml

      # Override these in .env file or docker-compose.override.yml
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-}
      - SMTP_API_KEY=${SMTP_API_KEY:-}
      - BARK_KEYS=${BARK_KEYS:-}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEBUG=${DEBUG:-false}
    
    # Volume mounts for data persistence
    volumes:
      # Configuration
      - ./config:/app/config:rw
      # Data persistence
      - relay_monitor_data:/app/data
      # Logs
      - relay_monitor_logs:/app/logs
      # Optional: Mount custom config
      # - ./config/config.toml:/app/config/config.toml:ro
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # Security (basic configuration)
    security_opt:
      - no-new-privileges:true
    
    # Network
    networks:
      - relay-monitor-network

# Named volumes for data persistence
volumes:
  relay_monitor_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./docker-data/data}
  
  relay_monitor_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOGS_PATH:-./docker-data/logs}

# Networks
networks:
  relay-monitor-network:
    driver: bridge

# Relay Monitor - Minimal Configuration
# This is a minimal configuration file for first-time setup
# Users can configure monitoring pairs and alerts through the web interface

[api]
base_url = "https://api.relay.link"
timeout = 30
retry_attempts = 3
retry_delay = 1.0
cache_ttl = 1800  # 30分钟缓存，减少频繁的token加载日志

[monitoring]
interval_seconds = 30
price_change_threshold_percent = 5.0
enabled = true
max_history_days = 15

[alerts]
enabled = true
rate_limit_minutes = 5

[database]
path = "data/relay_monitor.db"
cleanup_enabled = true
cleanup_interval_hours = 6

[web]
host = "127.0.0.1"
port = 5000
debug = false

[logging]
level = "INFO"
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
file = "logs/relay_monitor.log"
max_file_size_mb = 10
backup_count = 5
cleanup_enabled = true
cleanup_interval_hours = 24
max_log_age_days = 7

[cache]
chains_ttl = 3600
tokens_ttl = 3600
refresh_on_startup = true

[admin]
enabled = true
password = ""  # Will be set through web interface
session_timeout_minutes = 60
max_login_attempts = 5
lockout_duration_minutes = 15

[alerts.console]
enabled = true

[alerts.smtp_dev]
enabled = false
api_key = ""
to_emails = []
from_email = ""

[alerts.bark]
enabled = false
keys = []
server_url = "https://api.day.app"
timeout = 10

[alerts.webhook]
enabled = false
url = ""

[alerts.webhook.headers]
# Add custom headers here if needed

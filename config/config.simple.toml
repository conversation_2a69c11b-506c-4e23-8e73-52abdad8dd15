# Relay Monitor - Simplified Configuration
#
# 🚀 ZERO-CONFIG STARTUP: This file is now optional!
#
# For first-time users:
# 1. Start the application: python main.py web
# 2. Visit: http://localhost:5000
# 3. Complete the setup wizard
# 4. Add monitor pairs in the web interface
#
# This file is only used for:
# - Advanced deployment scenarios
# - Batch configuration import
# - CI/CD automation
#
# Monitor pairs should be configured via: Admin Panel > 交易对管理
# Sensitive data (passwords, API keys) are configured via web interface

# Monitoring Configuration
[monitoring]
interval_seconds = 30
price_change_threshold_percent = 5.0
enabled = true
max_history_days = 15

# Alert Configuration
[alerts]
enabled = true
rate_limit_minutes = 5

[alerts.console]
enabled = true

[alerts.smtp_dev]
enabled = false  # Set to true and configure SMTP_API_KEY in .env

[alerts.bark]
enabled = false  # Set to true and configure BARK_KEYS in .env

# Note: All sensitive configuration (passwords, API keys) is in .env file
# See .env.example for required environment variables

# Relay Monitor - Configuration Example
#
# 🚀 ZERO-CONFIG STARTUP: Monitor pairs are now configured through the web interface!
#
# For first-time users:
# 1. Start the application: python main.py web
# 2. Visit: http://localhost:5000
# 3. Complete the setup wizard
# 4. Add monitor pairs in the web interface
#
# This file shows system configuration only.
# Monitor pairs should be added via: Admin Panel > 交易对管理
#
# Example monitor pairs (configure via web interface):
# - Name: "ARB_ETH_to_POLYGON_POL"
# - Description: "Monitor Arbitrum ETH to Polygon POL bridge price"
# - Origin Chain: "arbitrum", Token: "ETH", Amount: "1.0"
# - Destination Chain: "polygon", Token: "POL"
# - Alert Threshold: 2.0%
#
# - Name: "ABSTRACT_ETH_to_POLYGON_USDT"
# - Origin Chain: "abstract", Token: "ETH", Amount: "1"
# - Destination Chain: "polygon", Token: "USDT"
# - Alert Threshold: 2.5%

[api]
base_url = "https://api.relay.link"
timeout = 30
retry_attempts = 3
retry_delay = 1.0
cache_ttl = 1800  # 30分钟缓存，减少频繁的token加载日志

[monitoring]
interval_seconds = 30
price_change_threshold_percent = 5.0
enabled = true
max_history_days = 15

[alerts]
enabled = true
rate_limit_minutes = 5

[database]
path = "data/relay_monitor.db"
cleanup_enabled = true
cleanup_interval_hours = 6

[web]
host = "127.0.0.1"
port = 5000
debug = false

[logging]
level = "INFO"
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
file = "logs/relay_monitor.log"
max_file_size_mb = 10
backup_count = 5
cleanup_enabled = true
cleanup_interval_hours = 24
max_log_age_days = 7

[cache]
chains_ttl = 3600
tokens_ttl = 3600
refresh_on_startup = true

[admin]
enabled = true
password = "admin123!!."  # CHANGE THIS IN PRODUCTION!
session_timeout_minutes = 60
max_login_attempts = 5
lockout_duration_minutes = 15

[alerts.console]
enabled = true

[alerts.smtp_dev]
enabled = false
api_key = "smtplabs_wgQzuwQmBcdaMQs1T6jrjeEzxWzZhXgSeqGvxzurWLaorDtF"
to_emails = ["<EMAIL>"]
from_email = "<EMAIL>"

[alerts.bark]
enabled = false
keys = ["YOUR_BARK_KEY_HERE"]
server_url = "https://api.day.app"
timeout = 10

[alerts.webhook.headers]

name: Build and Push Docker Image

on:
  push:
    branches:
      - main  # 当推送到main分支时触发（PR合并后）
    tags:
      - 'v*'  # 当推送v开头的标签时触发（生产发布）
  pull_request:
    branches:
      - main  # 只有PR到main分支时才触发CI检查
  workflow_dispatch:  # 允许手动触发
    inputs:
      tag:
        description: 'Docker image tag'
        required: true
        default: 'latest'
      push_latest:
        description: 'Also push as latest tag'
        required: false
        type: boolean
        default: false

env:
  DOCKER_USERNAME: ljh740
  IMAGE_NAME: relay-monitor

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # 获取完整的Git历史
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Docker Hub
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        username: ${{ env.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.DOCKER_USERNAME }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=semver,pattern={{major}}
          type=raw,value=latest,enable={{is_default_branch}}
          type=raw,value=${{ github.event.inputs.tag }},enable=${{ github.event_name == 'workflow_dispatch' }}
    
    - name: Get build info
      id: build_info
      run: |
        echo "build_date=$(date -u +%Y-%m-%dT%H:%M:%SZ)" >> $GITHUB_OUTPUT
        echo "git_commit=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
        echo "git_branch=$(git rev-parse --abbrev-ref HEAD)" >> $GITHUB_OUTPUT
        echo "version=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.optimized
        platforms: linux/amd64,linux/arm64
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        build-args: |
          BUILD_DATE=${{ steps.build_info.outputs.build_date }}
          VERSION=${{ steps.build_info.outputs.version }}
          VCS_REF=${{ steps.build_info.outputs.git_commit }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: Push latest tag (manual trigger)
      if: github.event_name == 'workflow_dispatch' && github.event.inputs.push_latest == 'true'
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.optimized
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ env.DOCKER_USERNAME }}/${{ env.IMAGE_NAME }}:latest
        labels: ${{ steps.meta.outputs.labels }}
        build-args: |
          BUILD_DATE=${{ steps.build_info.outputs.build_date }}
          VERSION=${{ github.event.inputs.tag }}
          VCS_REF=${{ steps.build_info.outputs.git_commit }}
        cache-from: type=gha
    
    - name: Update Docker Hub description
      if: github.event_name != 'pull_request' && github.ref == 'refs/heads/main'
      uses: peter-evans/dockerhub-description@v3
      with:
        username: ${{ env.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
        repository: ${{ env.DOCKER_USERNAME }}/${{ env.IMAGE_NAME }}
        readme-filepath: ./README.md
    
    - name: Output image info
      if: github.event_name != 'pull_request'
      run: |
        echo "## 🐳 Docker镜像构建完成" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 📋 镜像信息" >> $GITHUB_STEP_SUMMARY
        echo "- **仓库**: ${{ env.DOCKER_USERNAME }}/${{ env.IMAGE_NAME }}" >> $GITHUB_STEP_SUMMARY
        echo "- **标签**: ${{ steps.meta.outputs.tags }}" >> $GITHUB_STEP_SUMMARY
        echo "- **构建时间**: ${{ steps.build_info.outputs.build_date }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Git提交**: ${{ steps.build_info.outputs.git_commit }}" >> $GITHUB_STEP_SUMMARY
        echo "- **平台**: linux/amd64, linux/arm64" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 🔗 链接" >> $GITHUB_STEP_SUMMARY
        echo "- [Docker Hub](https://hub.docker.com/r/${{ env.DOCKER_USERNAME }}/${{ env.IMAGE_NAME }})" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 📥 拉取命令" >> $GITHUB_STEP_SUMMARY
        echo '```bash' >> $GITHUB_STEP_SUMMARY
        echo "docker pull ${{ env.DOCKER_USERNAME }}/${{ env.IMAGE_NAME }}:latest" >> $GITHUB_STEP_SUMMARY
        echo '```' >> $GITHUB_STEP_SUMMARY

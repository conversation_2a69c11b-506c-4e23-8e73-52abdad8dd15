# 🌉 Relay Monitor

**智能跨链桥接价格监控系统** - 实时监控不同区块链网络间的桥接价格变化

> 一个功能完整、易于使用的跨链价格监控解决方案，帮助您掌握DeFi桥接市场的实时动态

## ✨ 核心特性

### 🔄 实时价格监控
- **自动化监控**：24/7不间断监控跨链桥接价格
- **智能检测**：基于阈值的价格变化检测
- **多链支持**：支持Relay协议的所有区块链网络（76+条链）

### 🚨 智能警报系统
- **多渠道通知**：邮件、Webhook、Slack等多种通知方式
- **可配置阈值**：自定义价格变化警报条件
- **频率控制**：防止警报轰炸的智能限制

### 📊 现代化Web界面
- **实时仪表板**：美观的价格监控面板
- **历史图表**：价格趋势和变化分析
- **响应式设计**：支持桌面和移动设备

### 💾 数据持久化
- **历史记录**：完整的价格历史数据存储
- **趋势分析**：价格波动和统计分析
- **RESTful API**：完整的程序化访问接口

### 🧹 自动化维护
- **智能清理**：自动清理15天以上的历史数据
- **日志管理**：自动轮转和清理日志文件
- **空间优化**：防止磁盘空间耗尽，保持系统性能
- **维护统计**：详细的维护操作记录和统计

### ⚙️ 灵活配置
- **Web界面配置**：启动后通过管理界面配置，无需编辑文件
- **实时配置更新**：配置变更立即生效，无需重启
- **可视化管理**：交易对、警报、系统设置的图形化配置
- **智能验证**：实时配置验证和错误提示

### 🎛️ 专业管理界面
- **交易对管理**：可视化添加/编辑监控对，支持76+条区块链
- **警报配置**：邮件、Bark推送等通知渠道设置
- **系统设置**：监控间隔、数据保留、API配置等
- **维护工具**：数据库清理、日志管理、系统监控

### 🐳 生产优化
- **Docker优化**：镜像体积减少76.7%（291MB → 76.7MB）
- **Alpine基础**：更安全、更轻量的容器镜像
- **多架构支持**：支持AMD64和ARM64架构
- **零配置启动**：首次使用通过Web界面完成所有设置
- **安全配置管理**：密码加密存储、运行时配置优先级

## 🌐 支持的区块链

系统自动支持Relay协议的所有区块链网络，包括：

| 主要网络 | 配置名称 | 原生代币 | 说明 |
|----------|----------|----------|------|
| Ethereum | `ethereum` | ETH | 以太坊主网 |
| Arbitrum | `arbitrum` | ETH | Arbitrum One |
| Polygon | `polygon` | POL | Polygon PoS |
| Optimism | `optimism` | ETH | Optimism |
| Base | `base` | ETH | Base |
| BSC | `bsc` | BNB | Binance Smart Chain |
| Avalanche | `avalanche` | AVAX | Avalanche C-Chain |

**总计支持76+条区块链网络** - 系统会自动发现和支持所有可用的链

## 🚀 快速开始

### ⚡ 超简单启动（推荐）

**一键启动脚本：**

```bash
# 开发环境
./scripts/dev-start.sh

# 生产环境
./scripts/prod-start.sh

# 停止开发环境
./scripts/dev-stop.sh
```

### 🐳 Docker Hub 一键部署

**无需构建，直接使用预构建镜像：**

```bash
# 1. 下载部署文件
git clone https://github.com/ljh740/relayMonitor.git
cd relayMonitor

# 2. 一键部署（默认端口5000）
./scripts/deploy-from-hub.sh

# 3. 自定义端口部署（例如8080端口）
./scripts/deploy-from-hub.sh -p 8080

# 4. 或直接使用Docker Compose
HOST_PORT=5000 docker compose -f docker-compose.deploy.yml up -d  # 生产环境默认5000
HOST_PORT=8080 docker compose -f docker-compose.deploy.yml up -d  # 自定义端口
```

### 🔧 本地构建部署

**如需自定义构建：**

```bash
# 生产环境（推荐）
./scripts/docker-deploy.sh

# 开发环境
./scripts/docker-deploy.sh -e development

# 或直接使用 docker compose
docker compose -f docker-compose.prod.yml up -d  # 生产环境（优化镜像）
docker compose up -d                              # 开发环境（自动加载override）
```

3. **首次设置**：
- 访问：http://localhost:5000 (生产环境) 或 http://localhost:5001 (开发环境)
- 系统自动跳转到设置向导
- 设置管理员密码和通知配置
- 完成后开始添加监控交易对

> 📖 **部署指南**：
> - [🚀 快速部署指南](docs/quick-deploy.md) - 使用Docker Hub镜像一键部署
> - [🔧 完整部署文档](docs/docker-deployment.md) - 详细的Docker部署说明

### 📦 本地安装

1. **获取项目**：
```bash
git clone https://github.com/yourusername/relay-monitor.git
cd relay-monitor
```

2. **创建虚拟环境**（推荐）：
```bash
python -m venv .venv
source .venv/bin/activate  # Windows: .venv\Scripts\activate
```

3. **安装依赖**：
```bash
pip install -r requirements.txt
```

### ⚡ 5分钟快速上手

#### 🚀 **零配置启动（推荐）**

1. **直接启动**：
```bash
# Docker方式
./scripts/docker-deploy.sh

# 或本地方式
python main.py web
```

2. **首次设置**：
```bash
# 访问设置向导
open http://localhost:5000

# 系统自动引导完成：
# 1. 设置管理员密码
# 2. 配置Bark推送（可选）
# 3. 添加监控交易对
```

3. **开始监控**：
   设置完成后，在管理界面添加交易对即可开始监控！

#### 🖥️ **方式二：命令行配置**

1. **检查系统状态**：
```bash
python main.py status
```

2. **启动监控系统**：
```bash
# 启动Web界面 + 监控系统（推荐）
python main.py web --with-monitor

# 或者只启动监控
python main.py start
```

3. **访问Web界面**：
   打开浏览器访问 http://127.0.0.1:5000

### ⚙️ 配置

系统采用**零配置启动**设计，首次使用时通过Web界面完成设置：

#### 🚀 **首次设置（推荐）**
1. **启动系统**：
```bash
python main.py web
```

2. **访问设置页面**：
- 打开浏览器访问：http://localhost:5000
- 系统会自动跳转到首次设置页面
- 设置管理员密码（必填）
- 配置通知渠道（可选）：
  - Bark API密钥（手机推送）
  - 邮件通知（已内置，无需配置）

3. **完成设置**：
- 点击"完成设置"按钮
- 系统自动跳转到管理后台
- 开始添加监控交易对

#### 🌐 **Web界面管理**
设置完成后，通过管理界面进行所有配置：
- **交易对管理**：添加/编辑/删除监控对
- **警报配置**：设置通知渠道和阈值
- **系统设置**：监控间隔、数据保留等
- **密码管理**：修改管理员密码
- **实时生效**：配置变更立即应用

#### 📁 **高级配置（可选）**
如需批量配置或自动化部署：
```bash
# 一键初始化配置文件
./scripts/init-config.sh

# 交互式配置
./scripts/init-config.sh --interactive
```

**配置优先级**：
1. **数据库配置**（Web界面设置）- 最高优先级
2. **环境变量** - 中等优先级
3. **配置文件** - 最低优先级

### 🔧 **配置管理命令**

```bash
# 查看当前配置
python main.py config show

# 验证配置
python main.py config validate

# 查看系统状态
python main.py status
```

### ⚠️ 安全提示

系统采用安全的配置管理方式：

1. **密码安全**：
   - 管理员密码使用bcrypt加密存储在数据库中
   - 首次设置时强制用户设置强密码
   - 支持通过Web界面修改密码

2. **API密钥保护**：
   - 敏感信息存储在数据库中，不在配置文件中明文显示
   - 支持多个Bark API密钥配置
   - 邮件服务已内置，无需额外配置

3. **访问控制**：
   - 会话超时保护
   - 登录失败锁定机制
   - IP地址跟踪

**配置存储层次**：
- **数据库配置**（Web界面）：密码、Bark API密钥、运行时设置
- **文件配置**（config.toml）：系统参数、默认设置
- **环境变量**：Docker部署时的覆盖配置
- **内置服务**：SMTP.dev邮件服务（无需配置）

## 🔧 使用方法

### 命令行工具

```bash
# 系统管理
python main.py start                    # 启动监控
python main.py status                   # 查看状态
python main.py web --with-monitor      # 启动Web界面+监控

# 配置管理
python main.py config show             # 显示配置
python main.py config validate         # 验证配置

# 价格检查
python main.py check ARB_ETH_to_POLYGON_POL  # 手动检查特定对

# 维护管理
python main.py maintenance status      # 查看维护状态
python main.py maintenance cleanup     # 手动执行清理
python main.py maintenance stats       # 查看维护统计
```

### 📱 Web界面功能

访问 http://127.0.0.1:5000 查看：

#### **用户界面**
- **📊 仪表板**：实时价格监控、系统状态、监控对列表
- **📈 Status页面**：系统详细状态、数据库统计、配置信息
- **🚨 Alerts页面**：警报历史记录、警报统计
- **🔄 实时更新**：自动刷新数据、动态状态指示器

#### **管理界面** (http://127.0.0.1:5000/admin/login)
- **🚀 首次设置向导**：零配置快速启动，自动引导用户完成初始设置
- **🎛️ 交易对管理**：可视化添加/编辑监控对，支持76+条区块链
- **🔔 警报配置**：配置邮件、Bark推送等通知渠道
- **📧 邮件管理**：查看接收的警报邮件
- **⚙️ 系统设置**：监控间隔、数据保留、API设置等
- **🔐 安全管理**：密码修改、会话管理、访问控制
- **🧹 维护管理**：数据库清理、日志管理、系统统计

## 🎛️ Web管理界面详解

### 🔐 管理员访问

#### **首次设置**
- **访问地址**：http://localhost:5000
- **自动引导**：首次访问自动跳转到设置向导
- **设置内容**：管理员密码、通知配置（Bark、SMTP.dev）
- **安全设计**：密码bcrypt加密、强制设置强密码

#### **后续登录**
- **登录地址**：http://localhost:5000/admin/login
- **密码验证**：使用首次设置的密码
- **安全特性**：会话管理、登录限制、自动锁定、IP跟踪

### 📊 管理仪表板
- **系统概览**：监控状态、数据库统计、运行时间
- **快速操作**：启动/停止监控、配置重载、系统维护
- **实时状态**：监控对状态、警报统计、性能指标

### 🎯 交易对管理
- **可视化添加**：通过表单添加新的监控对
- **智能选择**：支持76+条区块链的下拉选择
- **实时编辑**：修改阈值、启用/禁用监控对
- **批量操作**：批量启用/禁用、导入/导出配置

### 🔔 警报配置
- **多渠道设置**：邮件、Bark推送、Webhook等
- **实时测试**：发送测试警报验证配置
- **频率控制**：防止警报轰炸的智能限制
- **模板管理**：自定义警报消息模板

### ⚙️ 系统设置
- **监控设置**：间隔时间、价格阈值、数据保留期
- **数据库设置**：自动清理、维护统计、立即清理
- **日志设置**：日志级别、文件大小、保留策略
- **API设置**：超时时间、重试次数、连接测试
- **安全设置**：会话管理、登录限制

### 🧹 维护工具
- **数据库维护**：查看大小、执行清理、统计信息
- **日志管理**：查看日志、清理旧文件、下载日志
- **系统监控**：API状态、连接测试、性能监控
- **配置管理**：备份配置、恢复设置、重载配置

## ⚙️ 配置详解

### 监控对配置

在Web管理界面中添加要监控的桥接对：

1. **访问交易对管理**：
   - 登录管理后台：http://localhost:5000/admin/login
   - 点击"交易对管理"菜单

2. **添加新的监控对**：
   - 点击"添加交易对"按钮
   - 填写监控对信息：
     - **名称**：如 "ARB_ETH_to_POLYGON_POL"（唯一标识）
     - **描述**：如 "Arbitrum ETH to Polygon POL"
     - **源链**：选择 "Arbitrum"
     - **目标链**：选择 "Polygon"
     - **源代币**：输入 "ETH"
     - **目标代币**：输入 "POL"
     - **监控金额**：输入 "1.0"
     - **警报阈值**：输入 "5.0"（百分比）

3. **保存并启用**：
   - 点击"保存"按钮
   - 确保"启用"开关已打开

**配置说明**：
- **支持76+条区块链**：包括 Arbitrum、Polygon、Ethereum 等
- **实时生效**：Web界面配置立即生效，无需重启
- **可视化管理**：支持编辑、删除、启用/禁用监控对
- **批量操作**：支持批量启用/禁用多个监控对

### 警报配置

配置多种警报通知渠道：

```toml
[alerts]
enabled = true                          # 启用警报系统
rate_limit_minutes = 5                  # 警报频率限制

[alerts.console]
enabled = true                          # 控制台警报

[alerts.email]
enabled = true                          # 邮件警报
smtp_server = "smtp.gmail.com"
smtp_port = 587
username = "<EMAIL>"
password = "your-app-password"          # 使用应用专用密码
from_email = "<EMAIL>"
to_emails = ["<EMAIL>"]

[alerts.webhook]
enabled = true                          # Webhook警报
url = "https://hooks.slack.com/services/YOUR/WEBHOOK/URL"
headers = { "Content-Type" = "application/json" }
```

**支持的通知渠道**：
- 🖥️ **控制台**：直接在终端显示
- 📧 **邮件**：SMTP邮件通知
- 🔗 **Webhook**：HTTP POST通知
- 💬 **Slack**：专门的Slack集成

## 🧹 维护系统

系统内置自动化维护功能，确保长期稳定运行：

### 自动维护功能

- **📊 数据库清理**：自动清理15天以上的历史数据
- **📝 日志管理**：自动清理7天以上的日志文件
- **⏰ 智能调度**：每6小时检查数据库，每24小时检查日志
- **📈 维护统计**：跟踪清理操作和释放的空间

### 维护配置

```toml
[database]
cleanup_enabled = true          # 启用数据库清理
cleanup_interval_hours = 6      # 清理检查间隔

[monitoring]
max_history_days = 15           # 数据保留天数

[logging]
cleanup_enabled = true          # 启用日志清理
cleanup_interval_hours = 24     # 日志清理间隔
max_log_age_days = 7            # 日志保留天数
```

### 手动维护命令

```bash
# 查看维护状态
python main.py maintenance status

# 手动执行清理
python main.py maintenance cleanup

# 查看维护统计
python main.py maintenance stats
```

### 维护优势

- **🚀 性能优化**：保持数据库精简，提升查询速度
- **💾 空间管理**：防止磁盘空间耗尽
- **🔄 自动化**：无需人工干预，系统自动维护
- **📊 可观测性**：详细的维护统计和日志

## 🐳 Docker优化

系统提供高度优化的Docker镜像，适合生产环境部署：

### 镜像优化成果

| 版本 | 基础镜像 | 大小 | 优化幅度 |
|------|----------|------|----------|
| **标准版** | python:3.13.5-slim | 291MB | - |
| **优化版** | python:3.13.5-alpine | **76.7MB** | **-76.7%** |

### 优化特性

- **🏔️ Alpine基础**：更安全、更轻量的Linux发行版
- **📦 精简依赖**：只包含生产环境必需的包
- **🔧 多阶段构建**：优化的构建流程
- **⚡ 快速启动**：更小的镜像意味着更快的部署

### 使用优化镜像

```bash
# 生产环境（自动使用优化镜像）
./scripts/docker-deploy.sh

# 或直接使用生产compose文件
docker compose -f docker-compose.prod.yml up -d

# 手动构建优化镜像
docker build -f Dockerfile.optimized -t relay-monitor:optimized .

# 对比不同版本
./scripts/build-optimized.sh
```

### 生产部署建议

```bash
# 推荐：使用生产部署脚本（自动使用优化镜像）
./scripts/docker-deploy.sh

# 或使用生产compose文件
docker compose -f docker-compose.prod.yml up -d
```

生产环境配置特点：
- **自动使用优化镜像**：Dockerfile.optimized
- **更严格的安全设置**：只读文件系统、无特权
- **优化的资源限制**：内存256MB（vs标准512MB）
- **生产环境变量**：DEBUG=false, FLASK_ENV=production

## 🔌 API接口

系统提供完整的RESTful API：

### 主要端点

| 端点 | 方法 | 说明 |
|------|------|------|
| `/api/pairs` | GET | 获取所有监控对数据 |
| `/api/pair/{name}/history` | GET | 获取特定对的价格历史 |
| `/api/monitor/status` | GET | 获取监控系统状态 |
| `/api/monitor/check/{name}` | POST | 手动触发价格检查 |
| `/api/stats` | GET | 获取系统统计信息 |

### 使用示例

```python
import requests

# 获取监控状态
response = requests.get('http://localhost:5000/api/monitor/status')
status = response.json()
print(f"监控状态: {'运行中' if status['running'] else '已停止'}")

# 获取所有监控对数据
response = requests.get('http://localhost:5000/api/pairs')
pairs = response.json()
for pair in pairs['pairs']:
    print(f"{pair['name']}: {pair['latest_price']}")

# 手动触发价格检查
response = requests.post('http://localhost:5000/api/monitor/check/ARB_ETH_to_POLYGON_POL')
result = response.json()
print(f"检查结果: {result}")
```

## 📚 文档

- **[📖 文档中心](docs/README.md)** - 完整的文档导航和索引
- **[🚀 快速开始](docs/QUICK_START.md)** - 5分钟快速部署指南
- **[⚙️ 配置说明](docs/CONFIGURATION.md)** - 详细配置参数说明
- **[📋 使用示例](docs/EXAMPLES.md)** - 实际应用场景和示例
- **[🐳 部署指南](docs/deployment/DOCKER_DEPLOYMENT.md)** - Docker容器化部署
- **[🔧 维护系统](docs/maintenance/MAINTENANCE_SYSTEM.md)** - 系统维护和监控
- **[📊 项目结构](docs/project/PROJECT_STRUCTURE.md)** - 代码架构和组织

## 🛠️ 开发

### 项目结构

```
relayMonitor/
├── src/relay_monitor/          # 源代码包
│   ├── api/                    # Relay API客户端
│   ├── monitor/                # 价格监控引擎
│   ├── storage/                # 数据存储层
│   ├── config/                 # 配置管理
│   ├── alerts/                 # 警报系统
│   ├── maintenance/            # 维护系统 🧹
│   ├── web/                    # Web界面
│   ├── cli/                    # 命令行接口
│   └── utils/                  # 工具函数
├── tests/                      # 测试文件 ✅
├── docs/                       # 文档目录 ✅
├── scripts/                    # 部署和构建脚本 🚀
├── config/                     # 配置文件
├── data/                       # 数据库文件
├── logs/                       # 日志文件
├── Dockerfile                  # 标准Docker镜像
├── Dockerfile.optimized        # 优化Docker镜像 🐳
├── Dockerfile.minimal          # 最小Docker镜像
├── docker-compose.yml          # Docker Compose配置
├── main.py                     # 主入口点
├── run_tests.py               # 测试运行器 ✅
├── pytest.ini                # 测试配置 ✅
├── requirements.txt           # Python依赖
├── requirements-prod.txt      # 生产环境依赖 📦
├── MAINTENANCE_SYSTEM.md      # 维护系统文档 🧹
└── DOCKER_OPTIMIZATION_SUMMARY.md  # Docker优化文档 🐳
```

### 运行测试

```bash
# 运行所有测试（推荐）
python run_tests.py

# 运行特定测试
python tests/test_api_client.py

# 使用pytest（如果安装）
pytest tests/
```

### Code Formatting

```bash
black src/ tests/
flake8 src/ tests/
mypy src/
```

## API Reference

The system uses the [Relay API](https://docs.relay.link/) for fetching bridge prices and chain information.

## 🔧 开发指南

### Git工作流程

我们使用 **dev分支开发模式** 来减少CI频率并提高代码质量：

```bash
# 1. 克隆仓库并切换到dev分支
git clone https://github.com/ljh740/relayMonitor.git
cd relayMonitor
git checkout dev

# 2. 日常开发
git add .
git commit -m "feat: 添加新功能"
git push origin dev

# 3. 准备发布时创建PR
# 从dev分支向main分支创建Pull Request
```

### 分支策略

- **`main`**: 生产分支，只能通过PR合并，触发完整CI/CD
- **`dev`**: 开发分支，日常开发，轻量级CI检查
- **`feature/*`**: 功能分支，开发新功能时使用

### CI/CD流程

- **Dev分支**: 代码质量检查、安全扫描、构建测试
- **Main分支**: 完整CI/CD、Docker构建推送、生产部署
- **标签推送**: 自动发布到Docker Hub

📖 详细的Git工作流程请参考：[Git工作流程指南](docs/git-workflow.md)

## Contributing

1. Fork the repository
2. Create a feature branch from `dev`
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request to `dev` branch

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the configuration examples
